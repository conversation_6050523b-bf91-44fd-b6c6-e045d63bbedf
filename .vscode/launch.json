{
    "version": "0.2.0",
    "configurations": [
        {
            // 模拟器运行
            "name": "Run SoulApp [Simulator]",
            "type": "sweetpad-lldb",
            "request": "attach",
            "preLaunchTask": "sweetpad: launch",
            "codelldbAttributes": {
                "initCommands": [
                    "settings set target.prefer-dynamic-value run-target"
                ]
            }
        },
        {
            // 真机上启动（手机上安装过后， 建议优先使用）
			"name": "Launch SoulApp [Device]",
			"type": "lldb",
			"request": "launch",
            "program": "${command:sweetpad.debugger.getAppPath}",
			"iosBundleId": "com.soulapp.cn",
			"iosTarget": "select",
			"iosInstallApp": false,
            "initCommands": [
                "settings set target.prefer-dynamic-value run-target"
            ]
		},
        {
            // 真机上安装而且启动（会比较慢，建议只第一次使用）
			"name": "Install & Launch SoulApp [Device]",
			"type": "lldb",
			"request": "launch",
            "program": "${command:sweetpad.debugger.getAppPath}",
			"preLaunchTask": "sweetpad: launch",
            "iosTarget": "select",
            "iosBundleId": "com.soulapp.cn",
            "initCommands": [
                "settings set target.prefer-dynamic-value run-target",
            ]
		},
        {
            // 真机上Attch（用于已经运行了App，中途需要断点调试）
			"name": "Attach SoulApp [Device]",
			"type": "lldb",
			"request": "attach",
            "program": "${command:sweetpad.debugger.getAppPath}",
            "iosBundleId": "com.soulapp.cn",
            "iosTarget": "select",
            "initCommands": [
                "settings set target.prefer-dynamic-value run-target",
            ]
		},
    ]
}

