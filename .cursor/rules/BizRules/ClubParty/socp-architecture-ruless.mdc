---
alwaysApply: false
---
# Soul App 群聊业务架构文档

## 1. 概述

Soul App 的群聊业务采用模块化架构设计，主要由三个核心模块组成：
- **SoulRoomCommon**: 基础房间通用模块
- **SoulChatModule**: 聊天业务模块
- **SoulClubParty**: 派对房间主模块

这三个模块通过清晰的分层和协议通信，实现了一个功能丰富、可扩展的实时群聊系统。

## 2. 架构分层

### 2.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                     SoulClubParty                           │
│              (派对房间主控制器 - ViewController)               │
│  负责：房间生命周期管理、模块注册、UI协调                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ├──────────────┬──────────────┐
                              ▼              ▼              ▼
┌──────────────────────────────────┐ ┌──────────────────────────────────┐
│        SoulChatModule            │ │      SoulRoomCommon              │
│         (业务模块集合)              │ │       (基础能力层)                 │
├──────────────────────────────────┤ ├──────────────────────────────────┤
│ • 挚友小屋 (SORelationCabinModule)│ │ • 基类 (SOChatRoomBaseViewController)│
│ • 大咖模式 (SOMagnateModule)      │ │ • IM服务 (SOCPPartyIMService)       │
│ • 自习室 (SOStudyRoomModule)      │ │ • RTC服务 (SOCPRTCModule)           │
│ • AI剧本杀 (SOAIMMModule)         │ │ • 配置管理 (SOCPConfigModule)        │
│ • 狼人杀 (SOCPWereWolfModule)     │ │ • 消息类型定义                        │
│ • 仪式房 (SORiteModule)           │ │ • 通用UI组件                         │
│ • 群聊模块 (SOCPGroupModule)       │ └──────────────────────────────────┘
│ • 关系模块 (UserRelationshipModule)│
│ • 礼物模块 (SOCPGiftModule)       │
│ • 更多业务模块...                  │
└──────────────────────────────────┘
```

### 2.2 依赖关系

```yaml
SoulClubParty:
  dependencies:
    - SoulRoomCommon  # 基础能力
    - SoulChatModule  # 业务模块
    - SoulRTCSDK      # 音视频能力
    - SoulUIKit       # UI组件库
    - SoulCore        # 核心服务

SoulChatModule:
  dependencies:
    - SoulRoomCommon  # 复用基础能力
    - SoulProtocols   # 协议定义
    - SoulUIKit       # UI组件

SoulRoomCommon:
  dependencies:
    - SoulNBIM        # IM SDK
    - SoulRTCSDK      # RTC SDK
    - SoulCore        # 核心服务
```

## 3. 核心组件详解

### 3.1 SoulRoomCommon - 基础能力层

#### 3.1.1 SOChatRoomBaseViewController
房间基类控制器，提供房间生命周期管理的标准流程：

```objc
@interface SOChatRoomBaseViewController : BaseSONavigationViewController

// 房间核心属性
@property (nonatomic, strong, readonly) id roomInfo;        // 房间信息
@property (nonatomic, strong, readonly) id mineJoinM;       // 个人信息
@property (readonly) NSString *roomId;                      // 房间ID
@property (readonly) NSString *ownerId;                     // 房主ID

// IM服务实例
@property (nonatomic, strong) SOCPPartyIMService *imService;

// 生命周期方法
- (void)setup;                    // 整体配置
- (void)setupPreModules;          // 设置高优先级模块
- (void)registerPreModules;       // 注册高优先级模块
- (void)setupSubModules;          // 设置低优先级模块
- (void)registerModules;          // 注册子模块
- (void)setupViews;               // 配置视图
- (void)preEnterRoom;             // 进房准备
- (void)syncSubmodulesEnterRoom;  // 通知模块进房
- (void)syncSubmodulesLeaveRoom;  // 通知模块离房

@end
```

#### 3.1.2 SOCPPartyIMService - IM服务
负责房间内即时通讯的核心服务：

```objc
@interface SOCPPartyIMService : NSObject

// IM消息类型基数（语音派对：600，视频派对：2000）
@property (nonatomic, assign) NSInteger imTypeBase;

// 消息白名单（安全过滤）
@property (nonatomic, copy) NSSet<NSNumber *> *messageTypeWhiteList;

// 核心方法
- (void)addDelegate:(id<SOCPPartyIMServiceDelegate>)delegate;
- (void)startServiceIsSham:(BOOL)isSham;  // 启动服务（支持假进房）
- (void)configService:(NSString *)roomId;  // 配置服务
- (void)sendMessage:(ChatRoomMessage *)message;  // 发送消息
- (void)stopService;  // 停止服务

@end
```

#### 3.1.3 消息类型定义
在 `SOChatRoomMsgTypeDefine.h` 中定义了所有IM消息类型：

```objc
typedef NS_ENUM(NSInteger, MsgType) {
    // 基础消息
    ChatRoomTextMsg = 1 + 600,           // 文本消息
    ChatRoomEnterMsg = 2 + 600,          // 进房消息
    ChatRoomLeaveMsg = 3 + 600,          // 离房消息
    
    // 游戏相关
    ChatRoomGameOn = 850 + 600,          // 游戏开启
    ChatRoomGameOff = 851 + 600,         // 游戏关闭
    ChatRoomPlayerJoinGameMsg = 876 + 600, // 玩家加入游戏
    
    // 关系相关
    ChatRoomInviteGuideMsg = 878 + 600,  // 引导邀请进房
    
    // 更多消息类型...
};
```

### 3.2 SoulChatModule - 业务模块层

#### 3.2.1 模块基类 - SOCRBaseSubModule

所有业务模块的基类，定义了标准的模块接口：

```objc
@interface SOCRBaseSubModule : NSObject <SOCRSubModuleProtocol>

// 核心属性
@property (nonatomic, strong) id roomInfo;  // 房间信息
@property (nonatomic, strong) id mineJoinM; // 个人信息
@property (nonatomic, weak) UIViewController *viewController; // 宿主控制器
@property (readonly) NSString *identifier;  // 模块唯一标识
@property (readonly) UIView *view;          // 模块视图

// 事件系统
- (void)postEvent:(NSString *)event info:(id)info;  // 发送事件
- (NSSet<NSString *> *)eventsToSubscribe;           // 订阅事件
- (void)handleEvent:(NSString *)event info:(id)info; // 处理事件

// IM消息
- (NSSet<NSNumber *> *)messagesToReceive;  // 订阅的IM消息
- (void)receiveMessage:(ChatRoomMessage *)message; // 接收IM消息

// 生命周期
- (void)syncEnterRoom;   // 进房
- (void)syncLeaveRoom;   // 离房
- (void)resetModule;     // 重置

@end
```

#### 3.2.2 典型业务模块示例

**挚友小屋模块 (SORelationCabinModule)**
```swift
public class SORelationCabinModule: SOCRBaseSubModule {
    public static var identifier = "SORelationCabinModule.identifier"
    
    // 模块状态
    private var onMode: Bool = false
    
    // 主视图
    private var mainView: SORelationCabinMainBaseView?
    
    // 视图模型
    private var roomVM: SORelationCabinRoomVM?
    
    // 订阅的IM消息
    override public func messagesToReceive() -> Set<NSNumber> {
        return [
            NSNumber(value: MsgType.ChatRoomRelationChange.rawValue),
            NSNumber(value: MsgType.ChatRoomTemplateChange.rawValue)
        ]
    }
    
    // 进房处理
    override public func syncEnterRoom() {
        super.syncEnterRoom()
        loadTemplateData()
        setupMainView()
    }
}
```

**AI剧本杀模块 (SOAIMMModule)**
```swift
public class SOAIMMModule: SOCRBaseSubModule {
    public static var identifier = "SOAIMMModule.identifier"
    
    // 游戏状态管理
    private var gameState: GameState = .idle
    
    // 订阅的IM消息
    override public func messagesToReceive() -> Set<NSNumber> {
        return [
            NSNumber(value: MsgType.ChatRoomGameOn.rawValue),
            NSNumber(value: MsgType.ChatRoomGameOff.rawValue),
            NSNumber(value: MsgType.ChatRoomPlayerJoinGameMsg.rawValue)
        ]
    }
    
    // 处理IM消息
    public func receiveAIMMIM(message: ChatRoomMessage) {
        switch message.type {
        case MsgType.ChatRoomGameOn.rawValue:
            handleGameStart(msg: message)
        case MsgType.ChatRoomGameOff.rawValue:
            handleGameEnd(msg: message)
        default:
            break
        }
    }
}
```

### 3.3 SoulClubParty - 主控制器层

#### 3.3.1 SOCPClubPartyViewController
派对房间的主控制器，负责协调所有模块：

```objc
@implementation SOCPClubPartyViewController

- (void)registerModules {
    // 核心模块
    [self registerModuleWithClass:SOCPConfigModule.class];      // 配置
    [self registerModuleWithClass:SOCPRTCModule.class];         // RTC
    [self registerModuleWithClass:SOCPIMModule.class];          // IM
    
    // 业务模块
    [self registerModuleWithClass:SORelationCabinModule.class]; // 挚友小屋
    [self registerModuleWithClass:SOMagnateModule.class];       // 大咖模式
    [self registerModuleWithClass:SOStudyRoomModule.class];     // 自习室
    [self registerModuleWithClass:SOAIMMModule.class];          // AI剧本杀
    [self registerModuleWithClass:SOCPWereWolfModule.class];    // 狼人杀
    [self registerModuleWithClass:SOCPGiftModule.class];        // 礼物
    [self registerModuleWithClass:SOCPGroupModule.class];       // 群聊
    
    // 更多模块...
}

- (void)registerModuleWithClass:(Class)moduleClass
                     identifier:(NSString *)identifier
                          frame:(CGRect)frame {
    // 创建模块实例
    SOCRBaseSubModule *module = [[moduleClass alloc] init];
    module.roomInfo = self.roomInfo;
    module.mineJoinM = self.mineJoinM;
    module.viewController = self;
    
    // 注册到模块管理器
    [self.moduleManager registerModule:module];
    
    // 添加模块视图
    if (module.view) {
        [self addModuleView:module.view level:module.viewLevel];
    }
}

@end
```

## 4. 通信机制

### 4.1 事件系统
模块间通过事件系统进行解耦通信：

```objc
// 发送事件
[self postEvent:@"SORelationCabinModule.templateChanged"
           info:@{@"templateId": @(templateId)}];

// 订阅事件
- (NSSet<NSString *> *)eventsToSubscribe {
    return [NSSet setWithObjects:
        @"SORelationCabinModule.templateChanged",
        @"SOStudyRoomModule.refreshUI",
        nil
    ];
}

// 处理事件
- (void)handleEvent:(NSString *)event info:(id)info {
    if ([event isEqualToString:@"SORelationCabinModule.templateChanged"]) {
        [self handleTemplateChange:info];
    }
}
```

### 4.2 IM消息通信
通过IM服务实现实时消息传递：

```swift
// 发送IM消息
let message = ChatRoomMessage()
message.type = MsgType.ChatRoomGameOn.rawValue
message.content = gameInfo.toJSONString()
viewController.imService.sendMessage(message)

// 接收IM消息
func receiveMessage(_ message: ChatRoomMessage) {
    switch message.type {
    case MsgType.ChatRoomGameOn.rawValue:
        handleGameStart(message)
    default:
        break
    }
}
```

### 4.3 服务协议
通过协议定义服务接口，实现模块间服务调用：

```objc
// 获取服务
id<SOCPRTCServiceProtocol> rtcService = [self getService:@protocol(SOCPRTCServiceProtocol)];
[rtcService startAudioCapture];

// 提供服务
- (Protocol *)serviceToProvide {
    return @protocol(SORelationCabinServiceProtocol);
}
```

## 5. 模块生命周期

### 5.1 进房流程

```
1. SOCPClubPartyViewController.viewDidLoad
   ├── setup
   ├── registerPreModules (高优先级模块)
   ├── setupPreModules
   ├── registerModules (普通模块)
   └── setupSubModules
   
2. 进房准备
   ├── preEnterRoom
   ├── startIMService
   └── startRTCService
   
3. 同步进房
   ├── syncSubmodulesEnterRoom
   └── 各模块.syncEnterRoom
       ├── 加载数据
       ├── 初始化UI
       └── 订阅消息
```

### 5.2 离房流程

```
1. 用户触发离房
   ├── syncSubmodulesLeaveRoom
   └── 各模块.syncLeaveRoom
       ├── 保存状态
       ├── 清理资源
       └── 取消订阅
       
2. 停止服务
   ├── stopIMService
   ├── stopRTCService
   └── 释放资源
```

## 6. 关键特性

### 6.1 模块化设计
- **高内聚低耦合**: 每个模块独立负责特定业务
- **动态加载**: 根据房间类型和配置动态加载模块
- **热插拔**: 支持运行时添加/移除模块

### 6.2 性能优化
- **分优先级加载**: 核心模块优先，业务模块延迟
- **异步处理**: IM消息异步队列处理
- **资源复用**: 视图和资源的复用机制

### 6.3 安全机制
- **消息白名单**: IM消息类型白名单过滤
- **权限控制**: 基于角色的功能权限控制
- **数据加密**: 敏感数据传输加密

### 6.4 可扩展性
- **协议驱动**: 通过协议定义接口，易于扩展
- **事件解耦**: 事件系统实现模块间解耦
- **配置化**: 支持通过配置控制功能开关

## 7. 典型业务场景

### 7.1 挚友小屋
- 多人实时语音聊天
- 动态模板切换
- 座位管理和麦位控制
- 关系链展示

### 7.2 AI剧本杀
- 游戏状态同步
- 角色分配
- 剧情推进
- 投票系统

### 7.3 自习室
- 专注模式
- 背景音乐
- 学习时长统计
- 互动激励

## 8. 最佳实践

### 8.1 模块开发规范
1. 继承 `SOCRBaseSubModule` 基类
2. 实现必要的生命周期方法
3. 通过事件系统通信，避免直接依赖
4. 合理使用IM消息，避免频繁发送
5. 注意内存管理，及时释放资源

### 8.2 性能优化建议
1. 延迟加载非核心模块
2. 使用消息缓存减少重复处理
3. 合理使用异步处理
4. 避免主线程阻塞操作
5. 及时清理不再使用的资源

### 8.3 调试技巧
1. 使用 `SLog` 记录关键日志
2. 利用事件系统追踪模块交互
3. IM消息调试工具监控消息流
4. 性能监控工具定位瓶颈

## 9. 总结

Soul App 的群聊业务架构通过清晰的分层设计、模块化组织和完善的通信机制，实现了一个功能丰富、性能优良、易于扩展的实时群聊系统。该架构很好地平衡了功能复杂性和代码可维护性，为产品的快速迭代和功能创新提供了坚实的技术基础。

关键优势：
- **模块化**: 业务逻辑清晰分离，便于团队协作
- **可扩展**: 新功能可通过添加模块快速实现
- **高性能**: 分级加载和异步处理保证流畅体验
- **可维护**: 统一的架构规范降低维护成本
