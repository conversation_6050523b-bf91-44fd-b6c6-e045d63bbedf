---
alwaysApply: false
---
# 新增一个群聊消息类型流程规则
 新增一个群聊消息类型需要按照以下步骤进行：

## 1. 在 SOChatRoomMsgTypeDefine 类中新增消息类型

**文件位置**: `SOPods/SoulRoomCommon/SoulRoomCommon/Classes/ChtaRoomMsgType/SOChatRoomMsgTypeDefine.h`

```objectivec
// 在合适的位置添加新的消息类型常量
ChatRoomFlowCountChangeMsg = 875 + 600, // 使用递增的数字，875替换为实际的消息类型
```

**注意事项**:
- 新的消息类型后面一定要 + 600
- 消息类型数字需要递增，避免与现有类型冲突
- 添加清晰的注释说明消息用途
- 区分服务端消息（正常数字）和本地构造消息（10001开始）

## 2. 在 SOCPIMFilterPlugin 类中添加对该条消息类型的支持，以保证消息不被过滤

**文件位置**: `SOPods/SoulRoomCommon/SoulRoomCommon/Classes/PartyIMModule/SOCPIMFilterPlugin.m`

在 `allKnownMsgType` 方法的数组中添加新增的消息类型：

```objectivec
return @[
             ......
             @(ChatRoomFlowCountChangeMsg),
             ......
];
```

## 3. 如果是逻辑处理类型的消息，需要在对应的模块（module）内添加消息处理相关逻辑

需要先在对应模块的 `messagesToReceive` 方法中添加该新增消息类型，如果没有，则新增该方法：

```objectivec
- (NSSet<NSNumber *> *)messagesToReceive {
    return [NSSet setWithArray:@[
        ...
        @(ChatRoomFlowCountChangeMsg),
        ...
    ]];
}
```

```swift
    //MARK: 消息处理
    override public func messagesToReceive() -> Set<NSNumber> {
        return [
            ...
            NSNumber(value: MsgType.ChatRoomFlowCountChangeMsg.rawValue)
            ...
        ]
    }
```

然后再在 `handleMessage` 方法中，添加对应的消息的实现，如果没有，则新增该方法：
```objectivec
- (void)handleMessage:(ChatRoomMessage *)message {
    switch (message.type) {
        case ChatRoomFlowCountChangeMsg: {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self receiveChatRoomFlowCountChangeMsg];
            });
        }
            break;
    }
}

- (voic)receiveChatRoomFlowCountChangeMsg {
    // 实现方法
}
```

```swift
    override func handle(_ message: ChatRoomMessage) {
        switch message.type {
        case MsgType.ChatRoomFlowCountChangeMsg.rawValue:
            DispatchQueue.main.async {
                self.handle(chatRoomFlowCountChangeMsg: message)
            }
        default:
            break
        }
    }

    func handle(chatRoomFlowCountChangeMsg message: ChatRoomMessage) {
        // 实现方法
    }
```

**注意事项**:
- 根据消息特性选择合适的处理方式
- 如果不是公屏展示类型消息，新消息添加流程到此结束

## 4. 如果是公屏展示类型消息，需要先处理 ViewModel 相关逻辑

**文件位置**: `SOPods/SoulRoomCommon/SoulRoomCommon/Classes/Manager/Message/SmallMessagViewModel.m`

先在 `parseMessageInfo` 方法中添加对新消息的解析支持，并新增一个方法

```objectivec
- (void)parseMessageInfo:(ChatRoomMessage *)chatRoomMessage {
    switch (chatRoomMessage.type) {
        ...
        case ChatRoomFlowCountChangeMsg: {
            // 对消息进行处理
            [self parseChatRoomFlowCountChangeMsg:chatRoomMessage];
        }
        ...
    }
}
```

然后在新增的方法中对将消息转换成 ViewModel

```objectivec
- (void)parseChatRoomFlowCountChangeMsg:(ChatRoomMessage *)chatRoomMessage {
    // 进行相关逻辑处理
}
```

**注意事项**:
- 相关处理逻辑可参考 `SmallMessagViewModel` 类中其他类似方法
- 添加清晰的注释说明消息用途

## 5. 如果是公屏展示类型消息，需要创建新的 Cell 样式，或复用现有的样式

**新建Cell位置**: `SOPods/SoulRoomCommon/SoulRoomCommon/Classes/View/MessageCells/`

首先在新建 Cell 位置新增一个消息样式类，继承于 SOCRBaseAvatarMessageCell 或 SOChatRoomBaseMessageCell

**注意事项**:
- 如有图片或设计样式可参考设计
- 如无图片可根据描述进行设置，新增一个 Cell 或复用现有 Cell
- 如选择新增一个 Cell，可参考该路径下其他 Cell 的样式，使用objectivec语言新增对应的 .h 与 .m 文件
- 新的 Cell 的命名规范应与新增消息类型名称相对应，并以 SOCP 开头，Cell 结尾
- 添加清晰的注释

## 6. 如果新增了 Cell 样式，需要对该 Cell 的文件进行引入

**文件位置**: `SOPods/SoulRoomCommon/SoulRoomCommon/Classes/MessageModule/SOCPMessageHandler.m`

首先在文件的最上部引入该文件
```objectivec
#import <SoulRoomCommon/SOCPFlowCountChangeCell.h>
```

接下来，在 `displayCenter:onDisplayCell:withLayout` 方法中注册该样式

```objectivec
- (UITableViewCell<SODCMsgCellProtocol> *)dynamicCell:(UITableView *)tableView forLayout:(id<SODCMsgLayoutProtocol>)layout atIndexPath:(NSIndexPath *)indexPath {
    ...
    } else if ([cell isMemberOfClass:[SOCPFlowCountChangeCell class]]) {
        SOCPFlowCountChangeCell *customCell = (SOCPFlowCountChangeCell *)cell;
        // 进行相应逻辑处理
    }
    ...
}
```

**注意事项**:
- 如为复用现有 Cell，该步骤可跳过
- 添加清晰的注释

## 7. 如果是公屏展示类型消息，需要在 SOCPMessageHandler 新增该消息类型与对应 Cell 的映射关系

**文件位置**: `SOPods/SoulRoomCommon/SoulRoomCommon/Classes/MessageModule/SOCPMessageHandler.m`

在 `setupListConfig` 方法中添加新增消息类型与对应 Cell 样式的映射

```objectivec
- (void)setupListConfig {
    ...
    [config addDisplayCell:SOCPFlowCountChangeCell.class layout:SmallMessagViewModel.class forMsgType:@(ChatRoomFlowCountChangeMsg).stringValue];
    ...
}
```




