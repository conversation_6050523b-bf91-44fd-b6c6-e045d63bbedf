---
alwaysApply: false
---
# 新增一个群聊业务模块
 新增一个群聊业务模块需要按照以下步骤进行：

## 1. 新增该模块核心类

**文件位置**: `SOPods/SoulChatModule/SoulChatModule/Classes/`

需要在该路径下新建一个文件夹，将核心类建在该文件夹内，例如：`SOPods/SoulChatModule/SoulChatModule/Classes/AIMurderMysteryModule/SOAIMMModule.swift`

```swift
//
//  SOCustomModule.swift
//  SoulChatModule
//
//  Created by xxx on 2025/5/27.
//

import Foundation
import SoulRoomCommon
import SoulChatUIKit
import SoulCore

@objc
public protocol SOCustomModuleProtocol {
    /// 玩法模式是否打开中
    var on:Bool { get }
}

/// 新增 XXX 模块
@objcMembers
public class SOCustomModule: SOCRBaseSubModule {
    
    public static var identifier = "SOCustomModule.identifier"
    
    /// 记录模式开闭状态
    private
    var onMode:Bool = false

    /// 房间信息
    var roomInfoM: RoomInfoModel? {
        return roomInfo as? RoomInfoModel
    }
    
    /// 是否是管理员
    var isManager: Bool {
        return mineJoinInfo?.isManager ?? false
    }
    
    /// 是否是房主
    var isOwner: Bool {
        return mineJoinInfo?.isOwner ?? false
    }
    
    private
    var roomRole:String {
        if isOwner {
          return "1"
        } else if isManager {
            return "3"
        } else {
            return "2"
        }
    }
    
    /// 个人信息
    var mineJoinInfo: SOCPRoomerModel? {
        return mineJoinM as? SOCPRoomerModel
    }
    
    //MARK: 进房
    override public func syncEnterRoom() {
        super.syncEnterRoom()

    }
    
    /// 退房
    override public func syncLeaveRoom() {
        super.syncLeaveRoom()

    }
    
    override public func serviceToProvide() -> Protocol {
        return SOCustomModuleProtocol.self
    }
    
    //MARK: 事件
    override public func eventsToSubscribe() -> Set<String> {
        return [

        ]
    }

    override public func handleEvent(_ event: String, info: Any?, from _: String?, to _: String?) {
        switch event {
            
        default:
            break
        }
    }
    
    //MARK: 消息
    override public func messagesToReceive() -> Set<NSNumber> {
        return [

        ]
    }
    
    public override func handle(_ message: ChatRoomMessage) {
        super.handle(message)
        DispatchQueue.main.async {
            SOCustomModule.log("收到消息 - Type = \(message.type-600) data = \(message.data)")
            self.receiveIM(message: message)
        }
    }
    
    public
    func receiveIM(message:ChatRoomMessage) {
        switch message.type {
        
        default:
            break
        }
    }
    
    private var hostVC: SOCPClubPartyViewControllerProtocol? {
        if let vc = viewController as? SOCPClubPartyViewControllerProtocol {
            return vc
        }
        return nil
    }
}

//MARK: SOCustomModuleProtocol 协议实现

extension SOCustomModule:SOCustomModuleProtocol {
    public var on: Bool {
        return onMode
    }
}

extension SOCustomModule {
    // MARK: 关闭模式
    public
    func closeModelSuccess() {
        guard self.onMode else {
            return
        }
        self.onMode = false
        batteryMonitor(modeOn: false)

        SOCustomModule.log("模式关闭")
    }
    
    // MARK: 打开模式
    public
    func openModeSuccess() {
        guard !self.onMode else {
            return
        }
        self.onMode = true
        batteryMonitor(modeOn: true)
        
        SOCustomModule.log("模式打开")
    }
    
    private
    func batteryMonitor(modeOn:Bool) {
        let batteryM = getService(SOBatteryMonitorProtocol.self) as? SOBatteryMonitorProtocol
        let bizType = roomInfoM?.playType
        modeOn ? batteryM?.startPlay(bizType) : batteryM?.stopPlay(bizType)
    }
}

extension SOCustomModule {
    static func log(_ content: String) {
        SOCPDebugManager.shared.add(log: "SOCustomModule: \(content)")
        SLog.info(
            tag: "SOCustomModule",
            content: content
        )
    }
}
```

**注意事项**:
- 将 SOCustomModule 改为新的模块命名，新的模块命名以 SO 开头，Module 结尾，模块命名应与业务描述相符合
- 新增核心模块类使用 swift
- 添加清晰的注释说明各个方法用途

## 2. 在 VC 中添加该模块的注册实现

**文件位置**: `SOPods/SoulClubParty/SoulClubParty/Classes/ClubPartyModule/ViewController/SOCPClubPartyViewController.m`

在 `registerPreModules` 方法的数组中添加新增的消息类型：

```objectivec
- (void)registerPreModules {
    ...
    /// 注册XXX模块
    [self registerModuleWithClass:SOCustomModule.class identifier:SOCustomModule.identifier frame:CGRectZero];
    ...
}
```

## 3. 在 VC 中实现该模块的快捷调用方法

**文件位置**: `SOPods/SoulClubParty/SoulClubParty/Classes/ClubPartyModule/ViewController/SOCPClubPartyViewController.m`

```objectivec
#pragma mark - XXX模块
- (SOCRBaseSubModule<SOCustomModuleProtocol>*)customModule {
    return [self getService:@protocol(SOCustomModuleProtocol)];
}
```
