---
description: 
globs: 
alwaysApply: false
---
# 同步库定义
1. 使用 [sync_fw_define.py](mdc:sync_fw_define.py) 脚本生成库定义， 生成在 `framework_define.txt` 文件中。
2. 你需要针对生成的库定义，结合库本身的注释和你自己的理解，生成如下格式的定义, 其中 `[作用]` 改成具体的功能作用。因为库内容很多，你可以先收集所有的库， 然后一个库一个库的去更新。
```
库 `MGSwipeTableCell`: [作用]
1. 类[作用]: MGSwipeButton 继承自: UIButton
```
// [作用]
@property (nonatomic, strong, nullable) MGSwipeButtonCallback callback;
// [作用]
@property (nonatomic, assign) CGFloat buttonWidth;
// [作用]
+(nonnull instancetype) buttonWithTitle:(nonnull NSString *) title backgroundColor:(nullable UIColor *) color;
// [作用]
+(nonnull instancetype) buttonWithTitle:(nonnull NSString *) title backgroundColor:(nullable UIColor *) color padding:(NSInteger) padding;
```
```
3. 更新后，你需要把整体更新的内容同步到当前 `framework_define_temp.txt` 文件中。