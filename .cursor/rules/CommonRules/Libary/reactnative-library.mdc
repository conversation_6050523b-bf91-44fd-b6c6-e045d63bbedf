---
description: 
globs: 
alwaysApply: false
---
# 库介绍
下面枚举了 React Native 相关的库，生成 React Native 相关原生代码的时候，需要优先考虑下面库的提供的方法。

## 库 `reactnative-library`: React Native混合开发库，提供RN与原生交互的完整解决方案

### 1. 库 `SoulRNSDK`: React Native SDK核心库
1. 类: SoulRNEngine 继承自: NSObject
```
// 获取共享实例
+ (instancetype)shared;
// 运行引擎
- (void)run;
// 添加注册组件
- (void)addRegisterComponents:(NSString *)componentName;
// 添加注册页面事件
- (void)addRegisterPageEvent:(NSString *)componentName pageIndex:(NSString *)pageIndex;
// 发送显示页面事件
- (void)emitShowPageEvent:(NSString *)moduleName pageIndex:(NSString *)pageIndex;
// 发送隐藏页面事件
- (void)emitHidePageEvent:(NSString *)moduleName pageIndex:(NSString *)pageIndex;
// 发送销毁页面事件
- (void)emitDisposePageEvent:(NSString *)moduleName pageIndex:(NSString *)pageIndex;
// 添加页面参数
- (void)addPageParams:(SoulRNLoadEventParams *)params listener:(void(^)(BOOL isAlready))listener;
// 移除监听器
- (void)removeListener:(NSString *)pageName listener:(void(^)(BOOL isAlready))listener;
// 获取当前时间戳
- (NSInteger)cTimestamp;
// 是否通用模块已加载
- (BOOL)isCommonLoaded;
// 是否调试模式
- (BOOL)isDebugMode;
```
2. 类: SoulRNViewController 继承自: BaseSONavigationViewController
```
// 展示视图控制器
@property (nonatomic, weak) UIViewController *srn_presentViewController;
// 页面索引
@property (nonatomic, copy, readonly) NSString *pageIndex;
// 模块名称
@property (nonatomic, strong, readonly) NSString *moduleName;
// 设置模块名称和参数
- (void)setModuleName:(NSString *)name params:(NSDictionary *)params;
// 设置模块名称、参数和不透明度
- (void)setModuleName:(NSString *)name params:(NSDictionary *)params opaque:(BOOL)opaque;
```
3. 类: SoulRNRouterOptions 继承自: NSObject
```
// 页面ID
@property (nonatomic, strong) NSString *pageId;
// 原生路径
@property (nonatomic, strong) NSString *nativePath;
// H5路径
@property (nonatomic, strong) NSString *h5Path;
// 参数
@property (nonatomic, strong) NSDictionary *arguments;
// 页面处理器
@property (nonatomic, strong, nullable) SoulRnPageHandler pageHandler;
// 页面关闭处理器
@property (nonatomic, strong, nullable) SoulRnPageClosedHandler pageCloseHandler;
// 推送React Native路由
- (void)pushReactNativeRoute:(SoulRNRouterOptions *)options;
// 弹出React Native路由
- (void)popReactNativeRoute:(BOOL)animated;
```

### 2. 库 `SoulRnVPService`: 视频播放服务
1. 类: SoulRnVPService 继承自: NSObject
```
// 视频播放器代理
@property (nonatomic, weak) id<SOVideoPlayerDelegate> delegate;
// 当前视图ID
@property (nonatomic, assign) NSInteger curViewId;
// 开始Alpha视频播放器
- (void)startAlphaVideoPlayerWithPath:(NSString *)path inView:(UIView *)view;
// 开始命名视频播放器
- (void)startVideoPlayerWithNamed:(NSString *)name type:(NSString *)type inView:(UIView *)view;
// 开始路径视频播放器
- (void)startVideoPlayerWithPath:(NSString *)path inView:(UIView *)view;
// 启用重复播放
- (void)enableRepeat:(BOOL)repeat;
// 启用静音
- (void)enableMute:(BOOL)isMute;
// 销毁视频播放器
- (void)destoryVideoPlayer;
// 停止视频播放器
- (void)stopVideoPlayer;
// 重播
- (void)replay;
// 跳转到指定时间
- (void)seekTime:(CGFloat)time;
```
2. 类: SoulRnVPSurfaceView 继承自: UIView
```
// 视图创建事件
@property (nonatomic, strong) RCTBubblingEventBlock onViewCreated;
// 播放完成事件
@property (nonatomic, strong) RCTBubblingEventBlock onPlayCompleted;
// 进度更新事件
@property (nonatomic, strong) RCTBubblingEventBlock onProgressUpdated;
// 调用方法
- (void)call:(NSDictionary *)dictionary;
```

### 3. 库 `SoulRNPhotoUtils`: 照片工具库
1. 类: SoulRNPhotoUtils 继承自: NSObject
```
// 获取共享实例
+ (instancetype)shared;
// 从图片数据获取MIME类型
+ (NSString *)getImageMIMETypeFromImageData:(NSData *)imageData;
// 选择图片
- (void)chooseImage:(NSDictionary* )params callback:(SoulRNPhotoChooseCallback)choosed canceledCallback:(SoulRNPhotoChooseEndCallback)canceledCallback;
```

### 4. 库 `SoulRnNotification`: 通知服务
1. 类: SoulRnNotification 继承自: NSObject
```
// 获取共享实例
+ (instancetype)shareInstance;
// 发送通知
- (void)postNotificationName:(NSString* )aName object:(nullable id)anObject;
```

### 5. 库 `SoulRnIM`: 即时通讯服务
1. 类: SoulRnIM 继承自: NSObject
```
// 设置消息监听器
+ (void)setupMessageListener;
```

### 6. 库 `SoulRNDevSetting`: 开发设置
1. 类: SoulRNDevSetting 继承自: RCTEventEmitter
```
// 启用开发设置
+ (void)enableDevSetting;
// 是否启用开发设置
+ (BOOL)isEnableDevSetting;
```

### 7. 库 `SoulRnRequestParams`: 请求参数
1. 类: SoulRnRequestParams 继承自: NSObject
```
// 请求路径
@property (nonatomic, copy) NSString *path;
// 域名
@property (nonatomic, copy) NSString *domain;
// 请求方法
@property (nonatomic, copy) NSString *method;
// 请求头
@property (nonatomic, copy) NSDictionary *header;
// 请求数据
@property (nonatomic, copy) NSDictionary *data;
// 请求参数
@property (nonatomic, copy) NSDictionary *params;
```

### 8. 库 `SoulRnTextSurfaceView`: 文本表面视图
1. 类: SoulRnTextSurfaceView 继承自: UIView
```
// 参数
@property (nonatomic, copy) NSDictionary *arguments;
// 视图布局事件
@property (nonatomic, strong) RCTBubblingEventBlock onViewLayout;
```

### 9. 库 `SoulRnRtcSurfaceView`: RTC表面视图
1. 类: SoulRnRtcSurfaceView 继承自: UIView
```
// 视图创建事件
@property (nonatomic, strong) RCTBubblingEventBlock onViewCreated;
```
2. 类: SoulRnRtcEventHandler 继承自: NSObject
```
// 发送事件
- (void)sendEvent:(NSString *)method data:(id)data;
```

这个React Native库提供了完整的RN与原生交互解决方案，包括引擎管理、视图控制、视频播放、照片处理、通知服务、即时通讯、开发工具等功能模块，支持页面路由、事件通信、数据传递、组件注册等多种混合开发需求。