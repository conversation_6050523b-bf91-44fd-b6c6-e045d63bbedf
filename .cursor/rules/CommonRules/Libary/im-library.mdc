---
description: 
globs: 
alwaysApply: false
---
# 库介绍
下面枚举了 IM 相关的库，生成 IM 相关原生代码的时候，需要优先考虑下面库的提供的方法。

## 库 `im-library`: 即时通讯库，提供完整的IM消息收发和会话管理功能

### 1. 库 `SOIMSDK`: IM SDK核心库
1. 类: SOIMManager 继承自: NSObject
```
// 应用ID
@property (nonatomic, copy) NSString *appId;
// 设备ID
@property (nonatomic, copy) NSString *deviceId;
// 令牌处理器
@property (nonatomic, copy) SOIMTokenHandler tokenHandler;
// 签名处理器
@property (nonatomic, copy) SOIMSignHandler signHandler;
// 域名处理器
@property (nonatomic, copy) SOIMDomainHandler domainHandler;
// 摘要处理器
@property (nonatomic, copy) SOIMDigestHandler digestHandler;
// 日志处理器
@property (nonatomic, copy) SOIMLogHandler logHandler;
// 默认域名
@property (nonatomic, strong) SOIMDomain *defalutDomain;
// 会话扩展映射处理器
@property (nonatomic, copy) SOIMSessionExtMapFromMsgHandler sessionExtMapHandler;
// 配置协议
@property (nonatomic, readonly) id<SOIMConfigProtocol> config;
// 当前用户ID
@property (nonatomic, readonly, nullable) NSString *userId;
// 登录状态
@property (nonatomic, assign, readonly) SOIMLoginStatus loginStatus;
// 连接状态
@property (nonatomic, assign, readonly) SOIMConnectStatus connectStatus;
// 时间骰子
@property (atomic, assign, readonly) long long timeDice;
// 时间骰子是否已设置
@property (atomic, assign, readonly) BOOL timeDiceSetted;
// 会话异步队列
@property (nonatomic) dispatch_queue_t sessionAsyncQueue;
// 获取共享实例
+ (instancetype)sharedInstance;
// 初始化SDK
- (BOOL)initSDK:(void(^)(id<SOIMConfigProtocol> config))block;
// 登录
- (void)login:(NSString *)userId;
// 带令牌登录
- (void)login:(NSString *)userId token:(NSString *)token;
// 登出
- (void)logout;
// 重新连接
- (void)reConnect;
// 添加登录监听器
- (void)addLoginListener:(id<SOIMLoginListener>)listener;
// 移除登录监听器
- (void)removeLoginListener:(id<SOIMLoginListener>)listener;
// 添加连接监听器
- (void)addConnectListener:(id<SOIMConnectListener>)listener;
// 移除连接监听器
- (void)removeConnectListener:(id<SOIMConnectListener>)listener;
```
2. 类: SOIMMessage 继承自: NSObject
```
// 会话ID
@property (nonatomic, copy, readonly) NSString *sessionId;
// 子类型
@property (nonatomic, assign, readonly) NSInteger subType;
// 聊天类型
@property (nonatomic, assign) SOIMChatType chatType;
// 消息ID
@property (nonatomic, copy) NSString *msgId;
// 发送者
@property (nonatomic, copy) NSString *from;
// 接收者
@property (nonatomic, copy, nullable) NSString *to;
// 消息状态
@property (nonatomic, assign) SOIMSendStatus msgStatus;
// 时间戳
@property (nonatomic, assign) long long timeStamp;
// 是否保存到数据库
@property (nonatomic, assign) BOOL savedb;
// 文本内容
@property (nonatomic, copy, nullable) NSString *text;
// 是否已读
@property (nonatomic, assign) BOOL isReadDone;
// 显示类型
@property (nonatomic, assign) NSInteger showType;
// 是否来自同步
@property (nonatomic, assign) BOOL isFormSync;
// 接受的消息ID
@property (nonatomic, copy, nullable) NSString *acceptedMsgId;
// 消息类型
@property (nonatomic, assign) SOIMC2CMsgType type;
// 通知内容
@property (nonatomic, copy, nullable) NSString *notice;
// 阅后即焚标记
@property (nonatomic, assign) NSInteger snap;
// 是否匿名
@property (nonatomic, assign, readonly) BOOL isAnonymity;
// 扩展映射
@property (nonatomic, copy, nullable) NSDictionary<NSString*, NSString*> *extMap;
// 文本元素
@property (nonatomic, strong) SOIMTextElem *textElem;
// 图片元素
@property (nonatomic, strong) SOIMImageElem *picElem;
// 图片元素数组
@property (nonatomic, strong) NSMutableArray <SOIMImageElem *> *picElems;
// 视频元素
@property (nonatomic, strong) SOIMVideoElem *videoElem;
// 语音元素
@property (nonatomic, strong) SOIMVoiceElem *voiceElem;
// 用户表情元素
@property (nonatomic, strong) SOIMUserExpressionElem *userExpressionElem;
// 扩展元素
@property (nonatomic, strong) SOIMExtElem *extElem;
// 语音聊天元素
@property (nonatomic, strong) SOIMVoiceChatElem *voiceChatElem;
// 分享标签元素
@property (nonatomic, strong) SOIMShareTagElem *tagElem;
// 位置元素
@property (nonatomic, strong) SOIMPositionElem *positionElem;
// 音乐元素
@property (nonatomic, strong) SOIMMusicElem *musicElem;
// 通用元素
@property (nonatomic, strong) SOIMCommonElem *commonElem;
// 通话元素
@property (nonatomic, strong) SOIMMediaCallElem *callElem;
// 未读计数元素
@property (nonatomic, strong) SOIMUnReadCountElem *unReadCountElem;
// 游戏元素（已废弃）
@property (nonatomic, strong) SOIMGameElem *gameElem __deprecated_msg("已废弃");
// 猜拳骰子元素
@property (nonatomic, strong) SOIMFingerDiceElem *fingerGuessElem;
// 是否UGC内容
@property (nonatomic, assign) BOOL isUGC;
// 表情URL
@property (nonatomic, copy) NSString *expressionUrl;
// 作者用户ID加密
@property (nonatomic, copy) NSString *authorUserIdEcpt;
// 表情ID
@property (nonatomic, copy) NSString *expressionId;
// 是否是我的消息
- (BOOL)isMine;
// 消息描述
- (NSString *)descriptor;
```
3. 类: SOIMSessionModel 继承自: NSObject
```
// 会话ID
@property (nonatomic, copy) NSString *sessionId;
// 聊天类型
@property (nonatomic, assign) NSInteger chatType;
// 用户ID
@property (nonatomic, copy) NSString *userId;
// 目标用户ID
@property (nonatomic, copy) NSString *toUserId;
// 目标用户ID加密
@property (nonatomic, copy, nullable) NSString *toUserIdEcpt;
// 未读消息数
@property (nonatomic, assign) NSInteger unReadCount;
// 时间戳
@property (nonatomic, assign) long long timeStamp;
// 最后消息文本
@property (atomic, copy) NSString *lastMsgText;
// 消息状态
@property (nonatomic, assign) SOIMSendStatus msgStatus;
// 扩展信息
@property (atomic, copy) NSDictionary *extInfo;
// 隐藏来源
@property (atomic, copy) NSString *hiddenSource;
// 是否匿名会话
- (BOOL)isAnonymitySession;
```

### 2. 库 `SoulIM`: IM消息处理库
1. 类: ChatTransCenter 继承自: NSObject
```
// 聊天传输中心代理
@property (nonatomic ,weak) id <ChatTransCenterDelegate> delegate;
// 传递命令列表到IM服务
- (void)deliverCmdListToIMService:(NSArray *)messageList;
// 传递推送列表到IM服务
- (void)deliverPushListToIMService:(NSArray *)messageList;
// 传递报告列表到IM服务
- (void)deliverReportListToIMService:(NSArray *)messageList;
// 传递聊天室列表到IM服务
- (void)deliverChatRoomListToIMService:(NSArray *)messageList;
// 传递普通映射列表到IM服务
- (void)deliverNormalMapListToIMService:(NSArray *)messageList;
// 发送命令消息
- (void)sendCmdMessage:(id)message;
// 发送报告登出IM消息
- (void)sendReportLoginOutIMMessage;
// 发送关系报告消息
- (void)sendReportRelationWithMsg:(SoulRelationReportMsg *)message completion:(SendReusltCompletion)completion;
// 发送副本未读消息
- (void)sendViceUnreadWithViceIds:(NSArray <NSString *>*)viceIds;
// 发送Web IM消息
- (void)sendWebIMMessage:(WebIMMessage *)message;
// 获取房间信息请求
- (void)FechRoomInfoRequest:(NSString *)roomId userId:(NSString *)userId;
// 发送聊天室消息
- (void)sendChatRoomMessage:(ChatRoomMessage *)message;
// 同步聊天室消息
- (void)syncChatRoomMessage:(NSString *)roomId userId:(NSString *)userId;
// 发送心跳
- (void)sendPingPong:(NSString *)roomId userId:(NSString *)userId;
// 发送通知消息
- (void)sendNotifyMessage:(ChatRoomMessage *)message;
// 发送订单消息
- (void)sendOrderMessage:(ChatRoomMessage *)message;
// 清除重复数据
- (void)clearDuplicateData;
// 发送同步用户在线消息
- (void)sendSyncUserOnlineMessage:(MapIMMessage *)message;
```
2. 类: CmdIMMessage 继承自: IMCommenMessage
```
// 消息类型
@property (nonatomic ,copy) NSString *messageType;
// 扩展映射
@property (nonatomic ,copy) NSDictionary *extMap;
// 发送者UID
@property (nonatomic ,copy) NSString *fromUid;
// 接收者UID
@property (nonatomic ,copy) NSString *toUid;
// 扩展JSON
@property (nonatomic ,copy) NSString *extJson;
// 初始化带类型和扩展映射
- (instancetype)initWithType:(NSString *)messageType extMap:(NSDictionary * _Nullable)extMap toUid:(NSString *)toUid;
// 初始化带类型和扩展JSON
- (instancetype)initWithType:(NSString *)messageType extJson:(NSString *)extJson toUid:(NSString *)toUid fromUid:(NSString *)fromUid;
```
3. 类: ChatRoomMessage 继承自: IMCommenMessage
```
// 房间ID
@property (nonatomic ,copy) NSString *roomId;
// 房间信息
@property (nonatomic ,assign) ChatRoomInfo roomInfo;
// 类型
@property (nonatomic ,assign) NSInteger type;
// 房间消息
@property (nonatomic ,strong) RoomMsgMessage *roomMsgMessage;
// 房间信息消息
@property (nonatomic ,strong) RoomInfoMessage *roomInfoMessage;
// 头像
@property (nonatomic ,copy) NSString *avatar;
// 背景颜色
@property (nonatomic ,copy) NSString *bgColor;
// 昵称
@property (nonatomic ,copy) NSString *nickName;
// 用户ID
@property (nonatomic ,copy) NSString *userId;
// 数据
@property (nonatomic ,copy) NSDictionary *data;
// 目标用户ID
@property (nonatomic ,copy) NSString *toUserId;
// 创建时间
@property (nonatomic ,assign) long long createTime;
// 扩展映射
@property (nonatomic, strong) NSDictionary *extMap;
// 发送角色
@property (nonatomic ,assign) SendRole sendRole;
// 直播消息类型
@property (nonatomic, assign) SoulMessageType liveMsgType;
```
4. 类: SoulMessageUtil 继承自: NSObject
```
// 未读消息数
@property (nonatomic ,assign) NSInteger unReadCount;
// 未读Berg消息数
@property (nonatomic ,assign) NSInteger unReadBergCount;
// 聊天ID
@property (nonatomic ,copy) NSString *chatId;
// 是否游戏邀请者
@property (nonatomic ,assign) BOOL isGameInvitee;
// 是否免打扰
@property (nonatomic ,assign) BOOL isDND;
// 获取共享实例
+ (instancetype)sharedInstance;
// DES加密
+ (NSData *)desEncryptWithKey:(NSData *)data;
// DES解密
+ (NSData *)desDecryptWithKey:(NSData *)data;
// DES用户ID加密
+ (NSString *)desUserIdEncryptWithKey:(NSString *)userId;
// 获取游戏用户信息
- (NSDictionary *)getGameUserInfo;
// 保存游戏用户信息
- (void)saveGameUserInfo:(NSDictionary *)dict;
```

### 3. 库 `SOIMManager`: IM管理器扩展
1. 分类: SOIMManager (会话管理)
```
// 添加会话监听器
- (void)addConversationListener:(id<SOIMConversationListener>)listener;
// 移除会话监听器
- (void)removeConversationListener:(id<SOIMConversationListener>)listener;
// 获取会话列表
- (nullable NSArray <SOIMSessionModel *>*)getSessionList;
// 根据隐藏来源获取会话列表
- (NSArray <SOIMSessionModel *>*)getSessionListWithHiddenSource:(NSString *)hiddenSource;
// 匹配内容获取会话
- (nullable NSArray <SOIMSessionModel *>*)getSessionsMatch:(NSString *)content;
// 根据ID获取会话
- (nullable SOIMSessionModel *)getSessionWithId:(NSString *)sessionId;
// 异步获取会话
- (void)asyncGetSessionWithId:(NSString *)sessionId completion:(SOIMGetSessionResult)completion;
// 主队列异步获取会话
- (void)asyncGetSessionWithId:(NSString *)sessionId onMainQueueCompletion:(SOIMGetSessionResult)completion;
// 检查会话是否存在
- (BOOL)isExistSessionId:(NSString *)sessionId;
// 插入会话
- (void)insertSession:(SOIMSessionModel *)session;
// 更新会话
- (void)updateSession:(SOIMSessionModel *)session;
// 更新会话并控制通知
- (void)updateSession:(SOIMSessionModel *)session notifyConversationDidChanged:(BOOL)notifyConversationDidChanged;
// 如需要则更新会话
- (void)updateSessionIfNeed:(NSString *)sessionId;
// 根据键值更新会话
- (void)updateSessionWithId:(NSString *)sessionId kv:(NSDictionary <SOIMSessionKey, id>*)kv;
// 获取未读消息数
- (NSInteger)getUnreadMsgCountWithId:(NSString *)sessionId;
// 获取总未读消息数
- (NSInteger)getTotalUnreadMsgCountWithIds:(NSArray <NSString *>*)sessionIds;
// 获取所有未读消息数
- (NSInteger)getTotalUnreadMsgCount;
// 获取C2C消息未读数
- (NSInteger)getC2CMsgUnreadCountWithSessionId:(NSString *)sessionId from:(long long)from to:(long long)to;
// 删除会话
- (void)deleteSessionWithId:(NSString *)sessionId;
// 清除未读数
- (void)clearUnreadCountWithId:(NSString *)sessionId;
// 根据隐藏来源获取会话
- (void)getSessionsWithHiddenSource:(NSString *)hiddenSource completion:(SOIMReusltBlock)completion;
// 主队列根据隐藏来源获取会话
- (void)getSessionsWithHiddenSource:(NSString *)hiddenSource onMainQueueCompletion:(SOIMReusltBlock)completion;
// 根据隐藏来源获取未读数
- (void)getUnreadCountWithHiddenSource:(NSString *)hiddenSource completion:(SOIMIntResultBlock)completion;
// 主队列根据隐藏来源获取未读数
- (void)getUnreadCountWithHiddenSource:(NSString *)hiddenSource onMainQueueCompletion:(SOIMIntResultBlock)completion;
// 根据隐藏来源数组获取会话
- (void)getSessionsWithHiddenSourceArr:(NSArray<NSString *> *)hiddenSourceArr onMainQueueCompletion:(SOIMReusltBlock)completion;
// 获取所有会话
- (void)getAllSessionsOnCompletion:(SOIMReusltBlock)completion;
// 主队列获取所有会话
- (void)getAllSessionsOnMainQueueCompletion:(SOIMReusltBlock)completion;
```
2. 分类: SOIMManager (消息管理)
```
// 添加消息监听器
- (void)addMessageListener:(id<SOIMMessageListener>)listener;
// 移除消息监听器
- (void)removeMessageListener:(id<SOIMMessageListener>)listener;
// 发送消息
- (void)sendMessage:(SOIMMessage *)message;
// 发送消息带完成回调
- (void)sendMessage:(SOIMMessage *)message completion:(nullable SOIMReusltBlock)completion;
// 更新消息
- (void)updateMessage:(SOIMMessage *)message;
// 更新匿名C2C消息
- (void)updateAnonymityC2cMessageWithId:(NSString *)msgId kv:(NSDictionary <SOIMUpdateKey, id>*)kv;
// 更新C2C消息
- (void)updateC2CMessageWithId:(NSString *)msgId kv:(NSDictionary <SOIMUpdateKey, id>*)kv;
// 更新群组消息
- (void)updateGroupMessageWithId:(NSString *)msgId kv:(NSDictionary <SOIMUpdateKey, id>*)kv;
// 插入消息
- (void)insertMessage:(SOIMMessage *)message;
// 插入消息数组
- (void)insertMessages:(NSArray<SOIMMessage *> *)messages type:(SOIMChatType)type;
// 获取漫游消息
- (void)getRoamMessageWithId:(NSString *)startMsgId
              startTimestamp:(long long)startTimestamp
                    toUserId:(NSString *)toUserId
                  completion:(nullable SOIMReusltBlock)completion;
// 获取群组漫游消息
- (void)getGroupRoamMessageWith:(NSString *)groupId
             startAcceptedMsgId:(nullable NSString *)startAcceptedMsgId
                      isLoadNew:(BOOL)isLoadNew
                      isPreview:(BOOL)isPreview
                     completion:(nullable SOIMReusltBlock)completion;
// 发送副本未读消息
- (void)sendViceUnreadWithIds:(NSArray <NSString *>*)viceIds completion:(nullable SOIMReusltBlock)completion;
// 发送踢出消息
- (void)sendKickoutWithClientType:(SOIMClientType)clientType completion:(nullable SOIMReusltBlock)completion;
// 发送报告登出
- (void)sendReportLogout;
// 获取匿名C2C消息
- (nullable SOIMC2CMessage *)getAnonymityC2cMessage:(NSString *)msgId;
// 获取C2C消息
- (nullable SOIMC2CMessage *)getC2CMessage:(NSString *)msgId;
// 获取群组消息
- (nullable SOIMGroupMessage *)getGroupMessage:(NSString *)msgId;
// 异步更新群组消息
- (void)asyncUpdateGroupMessage:(NSString *)msgId withCallBack:(void(^)(SOIMMessage *message))callback;
// 根据参数获取匿名C2C消息
- (nullable NSArray *)getAnonymityC2cMessagesWithParam:(SOIMSearchParam *)param;
// 根据参数获取C2C消息
- (nullable NSArray *)getC2CMessagesWithParam:(SOIMSearchParam *)param;
// 获取所有C2C未读消息
- (NSArray *)getAllC2CUnreadMsgsWith:(NSString *)sessionId;
// 根据参数获取群组消息
- (void)getGroupMessagesWithParam:(SOIMSearchParam *)param completion:(nullable SOIMReusltBlock)completion;
// 获取最后一条匿名C2C消息
- (nullable SOIMC2CMessage *)getLastAnonymityC2cMessageWith:(NSString *)sessionId;
// 获取最后一条C2C消息
- (nullable SOIMC2CMessage *)getLastC2CMessageWith:(NSString *)sessionId;
// 获取最后一条C2C未读消息
- (nullable SOIMMessage *)getLastC2CUnreadMsgWith:(NSString *)sessionId;
// 获取最后一条群组消息
- (nullable SOIMGroupMessage *)getLastGroupMessageWith:(NSString *)sessionId;
// 清除漫游缓存
- (void)clearRoamCache;
// 清除群组缓存
- (void)clearGroupCache;
// 清除聊天室缓存
- (void)clearChatRoomCache;
// 根据类型清除缓存
- (void)clearCacheWithType:(SOIMChatType)type;
// 删除消息
- (BOOL)deleteMessage:(SOIMMessage *)msg isRmCloud:(BOOL)isRmCloud;
// 删除消息数组
- (BOOL)deleteMessages:(NSArray <SOIMMessage *>*)msgs type:(SOIMChatType)type isRmCloud:(BOOL)isRmCloud;
// 清除匿名C2C消息
- (void)clearAnonymityC2cMessageWithId:(NSString *)sessionId;
// 清除C2C消息
- (void)clearC2CMessageWithId:(NSString *)sessionId;
// 清除群组消息
- (void)clearGroupMessageWithId:(NSString *)sessionId;
// 获取所有消息数量
- (NSInteger)getAllMsgsCountWith:(NSString *)sessionId type:(SOIMChatType)type;
// 获取C2C消息日期列表
- (NSArray<NSDate *> *)getC2CMessageDateListWithParam:(SOIMSearchParam *)param;
// 获取C2C所有消息日时间戳列表
- (NSArray<NSNumber *> *)getC2CAllMessageDayTimestampListWithSessionId:(NSString *)sessionId from:(long long)from to:(long long)to;
```

这个IM库提供了完整的即时通讯解决方案，包括消息收发、会话管理、用户状态管理、聊天室功能等，支持文本、图片、语音、视频等多种消息类型，以及群聊、私聊、匿名聊天等多种聊天模式。