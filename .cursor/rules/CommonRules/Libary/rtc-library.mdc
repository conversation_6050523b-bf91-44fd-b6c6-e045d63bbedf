---
description: 
globs: 
alwaysApply: false
---
# 库介绍
下面枚举了 RTC 相关的库，生成 RTC 相关原生代码的时候，需要优先考虑下面库的提供的方法。

## 库 `rtc-library`: 实时通信库，提供音视频通话和直播功能

### 1. 库 `SoulRTCNativeBridge`: RTC原生桥接库
1. 类: SoulRTCManager 继承自: NSObject
```
// RTC服务协议
@property (nonatomic, strong, nullable) id<SoulRTCServiceProtocol> rtcService;
// 初始化带类型和令牌
- (instancetype)initWithType:(SoulRTCServiceType)type token:(nullable NSString *)agoraToken useNewAgoraService:(BOOL)useNewAgoraService;
// 初始化带类型、令牌和测试环境
- (instancetype)initWithType:(SoulRTCServiceType)type token:(nullable NSString *)agoraToken isTestEnv:(BOOL)isTestEnv useNewAgoraService:(BOOL)useNewAgoraService;
```
2. 类: Soul1V1RTCManager 继承自: NSObject
```
// 1V1 RTC服务协议
@property (nonatomic, strong) id<Soul1V1RTCServiceProtocol> rtcService;
// 初始化带类型和令牌
- (instancetype)initWithType:(Soul1V1RTCServiceType)type token:(nullable NSString *)agoraToken;
// 初始化带类型、令牌和测试环境
- (instancetype)initWithType:(Soul1V1RTCServiceType)type token:(nullable NSString *)agoraToken isTestEnv:(BOOL)isTestEnv;
```

### 2. 库 `SoulRTCSDK`: RTC SDK核心库
1. 类: SoulRTCService 继承自: NSObject
```
// 是否排除回调
@property (nonatomic, copy) BOOL (^isEcpted)(NSString *_Nullable uid);
// 是否需要客户端错误日志
@property (nonatomic, assign) BOOL needClientErrorLog;
// 应用ID
@property (nonatomic, strong) NSString *appId;
// 是否启用RTC日志
@property (nonatomic, assign) BOOL enableRTCLog;
// 视频尺寸
@property (nonatomic, assign) CGSize videoDimension;
// 上传到OSS回调
@property (nonatomic, copy) void (^upload2OSS)(NSString *path, NSString *name);
// 上传点回调
@property (nonatomic, copy) void (^uploadPoint)(NSString *eid, NSDictionary *params);
// 业务类型
@property (nonatomic, assign) NSInteger businessType;
// 是否命中聚合AB测试
@property (nonatomic, assign) BOOL isHitGatherAB;
// 获取排除ID回调
@property (nonatomic, copy) NSString *(^getEcptId)(NSString *_Nullable originId);
// 获取原始ID回调
@property (nonatomic, copy) NSString *(^getOriginId)(NSString *_Nullable ecptId);
// 离开房间失败时销毁SDK
@property (nonatomic, assign) BOOL leaveRoomFailedDestorySDK;
// 前一个RTC服务
@property (nullable, nonatomic, strong) id <SoulRTCServiceProtocol> preRTCService;
// 是否需要销毁Agora引擎
@property (nonatomic, assign) BOOL needDestroyAgoraEngine;
// 获取共享实例
+ (instancetype)sharedInstace;
// 生成指定类型的服务
- (nullable id<SoulRTCServiceProtocol>)generaWithType:(SoulRTCServiceType)type;
// 生成指定类型的服务带代理
- (nullable id<SoulRTCServiceProtocol>)generaWithType:(SoulRTCServiceType)type serviceDelegate:(nullable id <SoulRTCServiceDelegate>)delegate;
// 生成指定类型的服务带测试环境
- (nullable id<SoulRTCServiceProtocol>)generaWithType:(SoulRTCServiceType)type isTestEnv:(BOOL)isTestEnv useNewAgoraService:(BOOL)useNewAgoraService;
// 生成指定配置的服务
- (nullable id<SoulRTCServiceProtocol>)generaWithConig:(SoulRTCAppConfig *)config;
// 上传负载信息
+ (void)uploadLoadWithUserId:(NSString *)userId userName:(NSString *)userName ecptedUserId:(NSString *)ecptedUserId;
// 获取SDK版本
+ (NSString *)getSDKVersion;
```
2. 类: AgoraRtcBridge 继承自: NSObject
```
// 开始位置
@property (nonatomic, assign) NSUInteger startPos;
// 媒体播放时间改变回调
@property (nonatomic, copy) void(^mediaPlayeTimeDidChanged)(long, long, NSInteger);
// 消息接收回调
@property (nonatomic, copy) void(^messageDidReceived)(NSData *);
// 本地媒体播放器状态回调
@property (nonatomic, copy) void(^localMediaPlayerStateBlock)(AgoraMediaPlayerState);
// 媒体解密回调
@property (nonatomic, copy) BOOL (^_Nullable mediaDecrypt)(uint8_t * _Nonnull buf, uint32_t buf_size);
// 播放器音频处理回调
@property (nonatomic, copy) void (^_Nullable playerAudioHandle)(NSData *, NSInteger);
// 传输音频帧处理回调
@property (nonatomic, copy) void(^_Nullable transAudioFrameHandler)(void* _Nullable bytes,int len);
// 音频卡播放音频状态改变回调
@property (nonatomic, copy) void(^_Nullable audioCardPlayAudioStateChange)(AgoraMediaPlayerState playerState);
// 扩展房间令牌
@property (nonatomic, copy) NSString *exRoomToken;
// 领导者UID
@property (nonatomic, copy, nullable) NSString *leaderUid;
// 是否静音
@property (nonatomic, assign) BOOL isMute;
// 是否录音
@property (nonatomic, assign) BOOL isRecording;
// 当前UID
@property (nonatomic, copy) NSString *curUid;
// 音频唯一ID
@property (nonatomic, assign) NSInteger audioUniId;
// 初始化
- (instancetype)initWithAppId:(NSString *)appId curUid:(NSString *)uid config:(AgoraRtcEngineConfig *)config engineDelegate:(id<AgoraRtcEngineDelegate>)delegate;
// 销毁
- (void)destroy;
// 销毁媒体播放器视图
- (void)destroyMediaPlayerView;
// 获取引擎
- (AgoraRtcEngineKit *)getEngine;
// 重置Agora Kit
- (void)resetAgoraKitWithDestroy:(BOOL)needDestroy compelete :(void (^)(void))Blk;
// 获取媒体播放器
- (nullable id <AgoraRtcMediaPlayerProtocol>)getMediaPlayer;
// 切换歌手角色
- (void)switchSingerRole:(AgoraKTVClientRole)role;
// 设置新的主唱
- (void)setNewLeadSingerWithUid:(NSString *)uid playList:(nullable NSArray<id<AgoraKTVMusicModelProtocol>> *)list;
// 播放媒体列表
- (void)playMediaWithList:(nullable NSArray<id<AgoraKTVMusicModelProtocol>> *)list;
// 用户变声器
- (void)userVoiceChanger:(BOOL)changer audioFrameBlock:(void(^_Nullable)(void* _Nullable bytes,int len))transAudioFrameHandler;
// 暂停媒体播放器
- (void)pauseMediaPlayer;
// 开始媒体播放器
- (void)startMediaPlayer;
// 停止媒体播放器
- (void)stopMediaPlayer;
// 选择媒体音频轨道
- (void)selectMediaAudioTrack:(NSInteger) index;
// 设置KTV媒体播放器音量
- (int)setKTVMediaPlayerVolume:(int)volume;
// 调整音频混合音量
- (int)adjustAudioMixingVolume:(int)volume;
// 设置播放信号音量
- (void)setPlaybackSignalVolume:(int)volume;
// 获取播放信号音量
- (int)getPlaybackSignalVolume;
// 开始音频卡录音
- (void)startAudioCardRecordingWithFilePath:(NSString *)filePath;
// 停止音频卡录音
- (void)stopAudioCardRecording;
// 开始音频卡播放
- (void)startAudioCardPlaybackWithFilePath:(NSString *)filePath;
// 暂停音频卡播放
- (void)pauseAudioCardPlayback;
// 恢复音频卡播放
- (void)resumeAudioCardPlayback;
// 停止音频卡播放
- (void)stopAudioCardPlayback;
```

### 3. 库 `SoulAgoraService`: Agora服务库
1. 类: SoulAgoraService 继承自: NSObject
```
// 初始化带类型
- (instancetype)init:(SoulRTCServiceType)type;
// 初始化带配置
- (instancetype)initWithConfig:(SoulRTCAppConfig *)config;
// RTC引擎音量指示报告
- (void)rtcExEngine:(AgoraRtcEngineKit * _Nonnull)engine reportAudioVolumeIndicationOfSpeakers:(NSArray<AgoraRtcAudioVolumeInfo *> * _Nonnull)speakers totalVolume:(NSInteger)totalVolume;
// RTC引擎加入频道
- (void)rtcExEngine:(AgoraRtcEngineKit *)engine didJoinChannel:(NSString *)channel withUid:(NSUInteger)uid elapsed:(NSInteger)elapsed;
```
2. 类: SoulAgoraNewService 继承自: NSObject
```
// 初始化带类型
- (instancetype)init:(SoulRTCServiceType)type;
// 初始化带配置
- (instancetype)initWithConfig:(SoulRTCAppConfig *)config;
// RTC引擎音量指示报告
- (void)rtcExEngine:(AgoraRtcEngineKit * _Nonnull)engine reportAudioVolumeIndicationOfSpeakers:(NSArray<AgoraRtcAudioVolumeInfo *> * _Nonnull)speakers totalVolume:(NSInteger)totalVolume;
// RTC引擎加入频道
- (void)rtcExEngine:(AgoraRtcEngineKit *)engine didJoinChannel:(NSString *)channel withUid:(NSUInteger)uid elapsed:(NSInteger)elapsed;
```

### 4. 库 `Soul1V1RTCSDK`: 1V1 RTC SDK库
1. 类: Soul1V1AgoraService 继承自: NSObject
```
// 初始化带类型
- (instancetype)init:(Soul1V1RTCServiceType)type;
// 初始化带配置
- (instancetype)initWithConfig:(Soul1V1RTCAppConfig *)config;
// RTC引擎音量指示报告
- (void)rtcExEngine:(AgoraRtcEngineKit * _Nonnull)engine reportAudioVolumeIndicationOfSpeakers:(NSArray<AgoraRtcAudioVolumeInfo *> * _Nonnull)speakers totalVolume:(NSInteger)totalVolume;
// RTC引擎加入频道
- (void)rtcExEngine:(AgoraRtcEngineKit *)engine didJoinChannel:(NSString *)channel withUid:(NSUInteger)uid elapsed:(NSInteger)elapsed;
```
2. 类: Soul1V1TencentService 继承自: NSObject
```
// 初始化带类型
- (instancetype)init:(Soul1V1RTCServiceType)type;
// 初始化带类型和测试环境
- (instancetype)init:(Soul1V1RTCServiceType)type isTestEnv:(BOOL)isTestEnv;
// 初始化带配置
- (instancetype)initWithConfig:(Soul1V1RTCAppConfig *)config;
```

### 5. 库 `ZegoTokenObtain`: Zego令牌获取库
1. 类: ZegoTokenObtain 继承自: NSObject
```
// 保持秒数
@property (nonatomic, assign) NSUInteger secondKeep;
// 获取助手实例
+ (instancetype)helper;
// 获取令牌并保持
- (void)getTokenAndkeepSuccess:(void(^)(NSString *))success Fail:(void(^)(void))fail;
// 清除存储
- (void)clearStore;
```

这个RTC库提供了完整的实时音视频通信解决方案，包括多种RTC服务提供商（Agora、腾讯、火山引擎等）的支持，音视频通话、直播、KTV、游戏等多种场景的功能，支持音频处理、视频渲染、媒体播放、录音录像等多种实时通信需求。

