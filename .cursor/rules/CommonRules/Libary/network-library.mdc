---
description: 
globs: 
alwaysApply: false
---
# 库介绍
下面枚举了网络相关的库，生成网络相关原生代码的时候，需要优先考虑下面库的提供的方法。

## 库 `network-library`: 网络通信库，提供完整的网络请求和状态监控功能

### 1. 库 `SoulNetworkStatus`: 网络状态监控库
1. 类: SONetworkStatusManager 继承自: NSObject
```
// 状态自动更新间隔
@property (nonatomic, assign) NSTimeInterval statusAutoUpdateInterval;
// 当前网络状态
@property (nonatomic, assign, readonly) SONetworkStatus currentStatus;
// 当前状态字符串
@property (nonatomic, strong, readonly) NSString *currentStatusString;
// 状态更新回调
- (void)statusManagerStatusDidUpdate:(SONetworkStatusManager *)manager;
// 获取共享实例
+ (SONetworkStatusManager *)sharedInstance;
// 注册代理
- (void)registDelegate:(id<SONetworkStatusManagerProtocol>)delegate;
// 开始监控
- (void)startMonitoring;
// 停止监控
- (void)stopMonitoring;
// 更新网络状态
- (void)updateNetworkStatus;
```
2. 类: SONetworkStatusDelegateItem 继承自: NSObject
```
@property (nonatomic, weak) id delegate;
```

### 2. 库 `SoulNetworking`: 网络请求库
1. 分类: NSString
```
- (NSString *)so_desEncryptWithKey:(NSString *)keyString;
- (NSString *)desEncryptWithKey:(NSString *)key;
- (NSData *)desEncryptWithDataKey:(NSData *)key;
- (NSData *)desEncryptOrDecrypt:(CCOperation)option data:(NSData *)data dataKey:(NSData *)key mode:(int)mode;
```
2. 类: SoulResponseCommonModel 继承自: NSObject
```
@property (nonatomic, assign) NSInteger code;
@property (nonatomic, copy, nullable) NSString *message;
@property (nonatomic, strong, nullable) id data;
@property (nonatomic, strong) NSDictionary *ext;
@property (nonatomic, assign) BOOL success;
@property (nonatomic, assign, readonly) BOOL codeSuccess;
```
3. 分类: SoulNetworkRequest
```
@property (nonatomic, strong, nullable) dispatch_queue_t callbackQueue;
@property (nonatomic, assign) BOOL needStopDNSParse __attribute__((deprecated("delete")));
@property (nonatomic, assign) BOOL hasBiBikAdd;
@property (nonatomic, copy) NSString *originPath;
```
4. 类: SoulNetworkManager 继承自: NSObject
```
+ (NSString *)version;
+ (void)setNetworkConfig:(void (^)(SoulNetworkConfig *config))configBlk;
+ (void)request:(SoulNetworkRequest *)request
        success:(SoulResponseCommonBlock)success
           fail:(SoulResponseErrorBlock)fail;
+ (void)request:(SoulNetworkRequest *)request
   cacheHandler:(SoulResponseCommonBlock)cacheHandler
        success:(SoulResponseCommonBlock)success
           fail:(SoulResponseErrorBlock)fail;
+ (void)cancleRequest:(SoulNetworkRequest *)request;
+ (void)pauseRequest:(SoulNetworkRequest *)request;
+ (void)resumeRequest:(SoulNetworkRequest *)request;
+ (void)sendRequest:(SoulNetworkRequest *)request
            success:(SoulNetworkServiceSuccessHandler)success
               fail:(SoulNetworkServiceFailedHandler)fail;
+ (void)sendRequest:(SoulNetworkRequest *)request
       cacheHandler:(SoulNetworkServiceSuccessHandler)cacheHandler
            success:(SoulNetworkServiceSuccessHandler)success
               fail:(SoulNetworkServiceFailedHandler)fail;
+ (void)request:(SoulNetworkRequest *)request
        success:(SoulResponseCommonBlock)success
        failure:(SoulResponseFailureBlock)failure __attribute__((deprecated("Use request:success:fail:")));
+ (void)request:(SoulNetworkRequest *)request
      successed:(SoulResponseCacheCommonBlock)successed __attribute__((deprecated("Use request:cacheHandler:success:fail:")));
```
5. 类: SONetworkingUtils 继承自: NSObject
```
+ (void)addTwowayAuthenticationPKCS12:(nullable NSData *)p12 keyPassword:(nullable NSString *)password forManager:(AFURLSessionManager *)manager;
+ (NSDictionary *)netStaticBaseHeader;
+ (void)fillRequestSign:(SoulNetworkRequest *)request netConfig:(SoulNetworkConfig *)netConfig;
+ (void)smartFillParamPageId:(SoulNetworkRequest *)request netConfig:(SoulNetworkConfig *)netConfig;
```
6. 类: SoulDNSManager 继承自: NSObject
```
@property (atomic, copy, readonly) NSString *so_carrierName;
+ (instancetype)manager;
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;
- (id)copy NS_UNAVAILABLE;
- (id)mutableCopy NS_UNAVAILABLE;
- (void)resolveDomainArray:(NSArray<NSString *> *)domainArray;
- (nullable NSString *)getIpByUrl:(NSString *)urlString __attribute__((deprecated("use SDns")));
```
7. 类: SoulNetworkManagerQueryStringPair 继承自: NSObject
```
@property (readwrite, nonatomic, strong) id field;
@property (readwrite, nonatomic, strong) id value;
- (instancetype)initWithField:(id)field value:(id)value;
- (NSString *)URLEncodedStringValue;
```
8. 类: SoulDNSModel 继承自: NSObject
```
@property (nonatomic, copy) NSString *domain;
@property (nonatomic, copy) NSArray<SoulDNSIPModel *> *ipModels;
@property (nonatomic, copy) NSString *domain;
@property (nonatomic, copy) NSString *ip;
@property (nonatomic, copy) NSString *port;
@property (nonatomic, assign) BOOL isIPV6;
```

### 3. 库 `SoulAPI`: API管理库
1. 类: SOServerTime 继承自: NSObject
```
@property (nonatomic, assign) long long timeDiffer;
+ (SOServerTime *)sharedInstance;
- (void)requestServerTimeWithSuccess:(void(^)(long long serverTime))success
                              failed:(SoulResponseErrorBlock)failed;
```
2. 类: SOHTTPManager 继承自: AFHTTPSessionManager
```
+ (instancetype)shareInstance;
```
3. 分类: SoulAPIManager
```
- (void)request:(SoulNetworkRequest *)request
        success:(SoulResponseCommonBlock _Nullable)success
        failure:(SoulResponseFailureBlock _Nullable)failure;
- (void)postWithUrl:(NSString *)path
            baseUrl:(NSString *)baseUrl
               body:(NSData *)body
            success:(SoulAPIResponseCommonBlock _Nullable)success
            failure:(SoulAPIResponseCommonFailureBlock _Nullable)failure
             finish:(SoulAPIFinishBlock _Nullable)finish;
- (void)requestGet:(NSString *)path
           baseUrl:(NSString *)baseUrl
             token:(NSString *)token
        parameters:(NSDictionary *_Nullable)parameters
           success:(SoulAPIResponseCommonBlock _Nullable)success
           failure:(SoulAPIResponseCommonFailureBlock _Nullable)failure
            finish:(SoulAPIFinishBlock _Nullable)finish;
- (void)requestPost:(NSString *)path
            baseUrl:(NSString *)baseUrl
              token:(NSString *)token
         parameters:(NSDictionary *_Nullable)parameters
            success:(SoulAPIResponseCommonBlock _Nullable)success
            failure:(SoulAPIResponseCommonFailureBlock _Nullable)failure
             finish:(SoulAPIFinishBlock _Nullable)finish;
- (void)requestGET:(NSString *)path
           baseUrl:(NSString *)baseUrl
        parameters:(NSDictionary *_Nullable)parameters
           success:(SoulAPIResponseCommonBlock _Nullable)success
           failure:(SoulAPIResponseCommonFailureBlock _Nullable)failure;
- (void)requestGet:(NSString *)path
           baseUrl:(NSString *)baseUrl
        parameters:(NSDictionary *_Nullable)parameters
           success:(SoulAPIResponseCommonBlock _Nullable)success
           failure:(SoulAPIResponseCommonFailureBlock _Nullable)failure
            finish:(SoulAPIFinishBlock _Nullable)finish;
- (void)requestPOST:(NSString *)path
            baseUrl:(NSString *)baseUrl
         parameters:(NSDictionary *_Nullable)parameters
            success:(SoulAPIResponseCommonBlock _Nullable)success
            failure:(SoulAPIResponseCommonFailureBlock _Nullable)failure;
- (void)requestPost:(NSString *)path
            baseUrl:(NSString *)baseUrl
         parameters:(NSDictionary *_Nullable)parameters
            success:(SoulAPIResponseCommonBlock _Nullable)success
            failure:(SoulAPIResponseCommonFailureBlock _Nullable)failure
             finish:(SoulAPIFinishBlock _Nullable)finish;
- (void)requestPUT:(NSString *)path
           baseUrl:(NSString *)baseUrl
        parameters:(NSDictionary *_Nullable)parameters
           success:(SoulAPIResponseCommonBlock _Nullable)success
           failure:(SoulAPIResponseCommonFailureBlock _Nullable)failure;
- (void)requestPUT:(NSString *)path
           baseUrl:(NSString *)baseUrl
        parameters:(NSDictionary *_Nullable)parameters
           success:(SoulAPIResponseCommonBlock _Nullable)success
           failure:(SoulAPIResponseCommonFailureBlock _Nullable)failure
            finish:(SoulAPIFinishBlock _Nullable)finish;
- (void)requestDELETE:(NSString *)path
              baseUrl:(NSString *)baseUrl
           parameters:(NSDictionary *_Nullable)parameters
              success:(SoulAPIResponseCommonBlock _Nullable)success
              failure:(SoulAPIResponseCommonFailureBlock _Nullable)failure;
- (void)requestDelete:(NSString *)path
              baseUrl:(NSString *)baseUrl
           parameters:(NSDictionary *_Nullable)parameters
              success:(SoulAPIResponseCommonBlock _Nullable)success
              failure:(SoulAPIResponseCommonFailureBlock _Nullable)failure
               finish:(SoulAPIFinishBlock _Nullable)finish;
```
4. 分类: SoulNetworkRequest
```
@property (nonatomic, assign) BOOL autoRetry;
+ (SoulNetworkRequest *)apiGetRequestWithPath:(NSString *)path
                                      baseUrl:(NSString *)baseUrl
                                   parameters:(NSDictionary * _Nullable)parameters;
+ (SoulNetworkRequest *)apiPostRequestWithPath:(NSString *)path
                                       baseUrl:(NSString *)baseUrl
                                    parameters:(NSDictionary * _Nullable)parameters;
+ (SoulNetworkRequest *)apiProtobufGetRequestWithPath:(NSString *)path
                                              baseUrl:(NSString *)baseUrl
                                           parameters:(NSDictionary * _Nullable)parameters
                                        protobufClass:(Class)protobufClass;
+ (SoulNetworkRequest *)apiProtobufPostRequestWithPath:(NSString *)path
                                               baseUrl:(NSString *)baseUrl
                                            parameters:(NSDictionary * _Nullable)parameters
                                         protobufClass:(Class)protobufClass;
```
5. 类: SoulAPIManager 继承自: NSObject
```
@property (nonatomic, assign) SoulAPIManagerAutoRetryMask autoRetryEnableMask;
- (void)setNetworkConfig:(void (^)(SoulNetworkConfig *config))configBlk;
- (void)request:(SoulNetworkRequest *)request
        success:(SoulResponseCommonBlock)success
           fail:(SoulResponseErrorBlock)fail;
- (void)request:(SoulNetworkRequest *)request
      successed:(SoulResponseCacheCommonBlock)successed
        failure:(SoulResponseErrorBlock)failure;
- (void)request:(SoulNetworkRequest *)request
   cacheHandler:(SoulResponseCommonBlock)cacheHandler
        success:(SoulResponseCommonBlock)success
           fail:(SoulResponseErrorBlock)fail;
- (void)requestGET:(NSString *)path
           baseUrl:(NSString *)baseUrl
        parameters:(NSDictionary *_Nullable)parameters
           success:(SoulResponseCommonBlock)success
              fail:(SoulResponseErrorBlock)fail;
- (void)requestPOST:(NSString *)path
            baseUrl:(NSString *)baseUrl
         parameters:(NSDictionary *_Nullable)parameters
            success:(SoulResponseCommonBlock)success
               fail:(SoulResponseErrorBlock)fail;
- (void)requestPUT:(NSString *)path
           baseUrl:(NSString *)baseUrl
        parameters:(NSDictionary *_Nullable)parameters
           success:(SoulResponseCommonBlock)success
              fail:(SoulResponseErrorBlock)fail;
- (void)requestDELETE:(NSString *)path
              baseUrl:(NSString *)baseUrl
           parameters:(NSDictionary *_Nullable)parameters
              success:(SoulResponseCommonBlock)success
                 fail:(SoulResponseErrorBlock)fail;
- (SoulNetworkRequest *)requestSSEGET:(NSString *)path
                              baseUrl:(NSString *)baseUrl
                           parameters:(NSDictionary *_Nullable)parameters
                          dataReceive:(SoulSSEDataReceiveBlock)dataReceiver
                              success:(SoulSSESuccessBlock)success
                                 fail:(SoulSSEFailureBlock)fail;
- (SoulNetworkRequest *)requestSSEPOST:(NSString *)path
                               baseUrl:(NSString *)baseUrl
                            parameters:(NSDictionary *_Nullable)parameters
                           dataReceive:(SoulSSEDataReceiveBlock)dataReceiver
                               success:(SoulSSESuccessBlock)success
                                  fail:(SoulSSEFailureBlock)fail;
- (void)requestSSE:(SoulNetworkRequest *)request
       dataReceive:(SoulSSEDataReceiveBlock)dataReceiver
           success:(SoulSSESuccessBlock)success
              fail:(SoulSSEFailureBlock)fail;
- (void)requestGET:(NSString *)path
           baseUrl:(NSString *)baseUrl
        parameters:(NSDictionary *_Nullable)parameters
     protobufClass:(Class)protobufClass
           success:(SoulNetworkServiceSuccessHandler)success
              fail:(SoulNetworkServiceFailedHandler)fail;
- (void)requestPOST:(NSString *)path
            baseUrl:(NSString *)baseUrl
         parameters:(NSDictionary *_Nullable)parameters
      protobufClass:(Class)protobufClass
            success:(SoulNetworkServiceSuccessHandler)success
               fail:(SoulNetworkServiceFailedHandler)fail;
```
6. 类: HttpDnsHelper 继承自: NSObject
```
+ (void)setIsAdminConfEnable:(BOOL)enable;
+ (BOOL)getIsAdminConfEnable;
+ (BOOL)getAdminVersion;
+ (void)setAdminVersion:(BOOL)adminVersion;
+ (SoulGreyEnv)SOGreyEnv;
+ (SoulAppEnv)getGreyEnv;
+ (void)setGreyEnv:(SoulAppEnv)isGrey;
+ (NSString *)curEnvDomain:(SoulDomainType)domainType;
+ (SoulDomainType)debug_judgeDomainType:(NSString *)domainString;
```
7. 分类: HttpDnsHelper
```
+ (NSString *)getCurrentEnvironmentalDomain:(NSURL *  _Nullable)url __attribute__((deprecated("使用 [HttpDnsHelper curEnvDomain:] 方法替代")));
+ (NSString *)getOfflinePackageBaseUrl __attribute__((deprecated("使用 [HttpDnsHelper curEnvDomain:]")));
```
8. 分类: HttpDnsHelper
```
+ (NSString *)getH5BaseUrl;
+ (NSString *)getH5ShareUrl;
+ (NSString *)getH5GameBaseUrl;
```
9. 分类: SoulAPIManager
```
@property (nonatomic, strong, readonly) NSString* postBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.post.envDomain")));
@property (nonatomic, strong, readonly) NSString* postNewBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.newPost.envDomain")));
@property (nonatomic, strong, readonly) NSString* chatBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.chat.envDomain")));
@property (nonatomic, strong, readonly) NSString* userBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.user.envDomain")));
@property (nonatomic, strong, readonly) NSString* accountBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.account.envDomain")));
@property (nonatomic, strong, readonly) NSString* payBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.pay.envDomain")));
@property (nonatomic, strong, readonly) NSString* guestBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.guest.envDomain")));
@property (nonatomic, strong, readonly) NSString* wplBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.WPL.envDomain")));
@property (nonatomic, strong, readonly) NSString* ADQBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.ADQ.envDomain")));
@property (nonatomic, strong, readonly) NSString* ADRBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.ADR.envDomain")));
@property (nonatomic, strong, readonly) NSString* ADEBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.ADE.envDomain")));
@property (nonatomic, strong, readonly) NSString* groupChatDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.groupChat.envDomain")));
@property (nonatomic, strong, readonly) NSString* h5BaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.H5.envDomain")));
@property (nonatomic, strong, readonly) NSString* petBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.pet.envDomain")));
@property (nonatomic, strong, readonly) NSString* clubPartyBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.clubPatry.envDomain")));
@property (nonatomic, strong, readonly) NSString* videoPartyBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.videoParty.envDomain")));
@property (nonatomic, strong, readonly) NSString* commercialBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.commercial.envDomain")));
@property (nonatomic, strong, readonly) NSString* searchBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.search.envDomain")));
@property (nonatomic, strong, readonly) NSString* activityBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.activity.envDomain")));
@property (nonatomic, strong, readonly) NSString* reportBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.report.envDomain")));
@property (nonatomic, strong, readonly) NSString* discernBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.discern.envDomain")));
@property (nonatomic, strong, readonly) NSString* routeServiceBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.routeService.envDomain")));
@property (nonatomic, strong, readonly) NSString* buzzServiceBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.buzzService.envDomain")));
@property (nonatomic, strong, readonly) NSString* increaseServiceBaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.increaseService.envDomain")));
@property (nonatomic, strong, readonly) NSString* SKABaseDomain __attribute__((deprecated("废弃，Swift中使用 SoulDomainType.SKA.envDomain")));
```

## 库 `SoulOssUploader`:
1. 类: SoulOssUploadManager 继承自: NSObject
```
+ (void)uploadImage:(UIImage *)image source:(SoulOssUploadSource)source progress:(SoulOssUploadProgressHandler)progress completion:(SoulOssUploadCompletionHandler)completion;
+ (void)uploadImage:(UIImage *)image quality:(SoulOssUploadImageQuality)quality source:(SoulOssUploadSource)source progress:(SoulOssUploadProgressHandler)progress completion:(SoulOssUploadCompletionHandler)completion;
+ (void)uploadGIF:(NSData *)gifData source:(SoulOssUploadSource)source progress:(SoulOssUploadProgressHandler)progress completion:(SoulOssUploadCompletionHandler)completion;
+ (void)uploadAudio:(id)audioPath source:(SoulOssUploadSource)source progress:(SoulOssUploadProgressHandler)progress completion:(SoulOssUploadCompletionHandler)completion;
+ (void)uploadVideo:(id)videoPath source:(SoulOssUploadSource)source progress:(SoulOssUploadProgressHandler)progress completion:(SoulOssUploadCompletionHandler)completion;
```
2. 类: SoulOssRetryPolicy 继承自: NSObject
```
+ (NSInteger)retryMaxCount;
+ (BOOL)canRetryWithCode:(NSInteger)code;
+ (NSTimeInterval)retryDelayDuration;
```
3. 类: SouluploadMediaAsset 继承自: NSObject
```
+ (instancetype)sharedInstance;
- (void)uploadMediaAsset:(SouluploadMediaAsset *)mediaAsset progressBlock:(UploadProgressHandler _Nullable)progressBlock complete:(void (^)(NSString *url, NSError *error))complete;
- (void)uploadImage:(id)image type:(SoulImageZipType)type isGif:(BOOL)isGif source:(SoulMediaSource)source complete:(void (^)(NSString *url, NSError *error))complete;
- (void)uploadImage:(id)image type:(SoulImageZipType)type isGif:(BOOL)isGif source:(SoulMediaSource)source progressBlock:(UploadProgressHandler _Nullable)progressBlock complete:(void (^)(NSString *url, NSError *error))complete;
- (void)uploadImageWithURL:(NSURL *)url source:(SoulMediaSource)source progressBlock:(UploadProgressHandler)progressBlock complete:(void (^)(NSString *url, NSError *error))complete;
- (void)uploadAudio:(id)locationPath source:(SoulMediaSource)source complete:(void (^)(NSString *url, NSError *error))complete;
- (void)uploadAudio:(id)locationPath source:(SoulMediaSource)source progressBlock:(UploadProgressHandler _Nullable)progressBlock complete:(void (^)(NSString * _Nullable url, NSError * _Nullable error))complete;
- (void)uploadVideo:(id)locationPath source:(SoulMediaSource)source complete:(void (^)(NSString *url, NSError *error))complete;
- (void)uploadVideo:(id)locationPath source:(SoulMediaSource)source progressBlock:(UploadProgressHandler _Nullable)progressBlock complete:(void (^)(NSString *url, NSError *error))complete;
- (void)uploadLog:(id)locationPath source:(SoulMediaSource)source complete:(void (^)(NSString *url, NSError *error))complete;
- (void)uploadLog:(id)locationPath source:(SoulMediaSource)source progressBlock:(UploadProgressHandler _Nullable)progressBlock complete:(void (^)(NSString * _Nullable url, NSError * _Nullable error))complete;
- (void)uploadVideo:(PHAsset *)asset fileName:(NSString *)name source:(SoulMediaSource)source progressBlock:(UploadProgressHandler _Nullable)progressBlock complete:(void (^)(NSString *url, NSError *error))complete;
```
4. 类: SoulOssTokenFetcher 继承自: NSObject
```
+ (void)fetchTokenWithFileName:(NSString *)fileName
                          type:(NSString *)type
                        userID:(NSString *)userID
                        source:(SoulOssUploadSource)source
                    completion:(void (^)(SoulOssUploadData * _Nullable data, NSError * _Nullable error))completion;
```
5. 类: SoulOssUploadData 继承自: NSObject
```
@property (nonatomic, copy) NSString *uploadSignUrl;
@property (nonatomic, copy) NSString *key;
@property (nonatomic, copy) NSString *fileUrl;
@property (nonatomic, copy) NSString *bucketName;
```
6. 类: SoulOssFileUploader 继承自: NSObject
```
+ (void)uploadFile:(NSURL *)fileURL 
              info:(SoulOssUploadData *)info 
          progress:(void (^)(float percent, CGFloat totalBytes))progress
          complete:(void (^)(NSString *url, NSError *error))complete;
+ (void)uploadData:(NSData *)fileData
              info:(SoulOssUploadData *)info
          progress:(void (^)(float percent, CGFloat totalBytes))progress
          complete:(void (^)(NSString *url, NSError *error))complete;
```
7. 类: SODocRequest 继承自: NSObject
```
+ (instancetype)sharedInstance;
- (void)getAliTokenWithFileName:(NSString *)fileName
                           type:(NSString *)type
                         userID:(NSString *_Nullable)userID
                         source:(SoulMediaSource)source
                      onSuccess:(ResponseModelSuccessHandler)succeedHandler
                      onFailure:(FailureHandler)failureHandler
                       onFinish:(FinishHandler)finishHandler;
```


## 库 `SoulOssUrlMaker`:
1. 类: SoulGetMediaAddressManager 继承自: NSObject
```
@property (nonatomic, copy) NSString *ossCDNDomain;
@property (nonatomic, assign) CGFloat stairRatio;
@property (nonatomic, assign) BOOL enableAvif;
+ (instancetype)shareInstance;
- (NSURL *)getImageUrlWithPath:(NSString *)path size:(CGSize)size;
- (NSURL *)getImageUrlWithPath:(NSString *)path size:(CGSize)size type:(SoulMediaAddressImageType)type;
- (NSString *)getImageUrlStrWithPath:(NSString *)path size:(CGSize)size;
- (NSString *)getImageUrlStrWithPath:(NSString *)path size:(CGSize)size type:(SoulMediaAddressImageType)type;
- (NSURL *)getHeadsImageUrlWithPath:(NSString *)path size:(CGSize)size;
- (NSURL *)getHeadsImageUrlWithPath:(NSString *)path size:(CGSize)size type:(SoulMediaAddressImageType)type;
- (NSString *)getHeadsImageUrlStrWithPath:(NSString *)path size:(CGSize)size;
- (NSString *)getOSSImageFullUrl:(NSString *)path imageType:(SoulMediaAddressImageType)type appendBlock:(void (^)(NSMutableString *str))appendBlock;
- (NSString *)ossRoundStrWithRadius:(CGFloat)radius;
- (NSString *)ossResizeStrWithSize:(CGSize)size stairType:(SoulMediaAddressStairType)stairType;
- (NSString *)typeStrWithType:(SoulMediaAddressImageType)imageType urlStr:(NSString *)urlStr;
- (NSString *)qualityStrWithQuality:(NSInteger)quality;
- (NSString *)getMediaUrlWithUrlStr:(NSString *)urlStr;
- (NSInteger)roundValue:(NSInteger)inputValue withType:(SoulMediaAddressStairType)stairType;
```
2. 类: SoulGetVideoSnapshotManager 继承自: NSObject
```
+ (NSString *)getVideoSnapshotWithUrl:(NSString *)urlStr time:(NSInteger)time size:(CGSize)size;
```