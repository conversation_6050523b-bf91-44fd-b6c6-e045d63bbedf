---
description: 
globs: 
alwaysApply: false
---
# 库介绍
下面枚举了 UI Kit 相关的库，生成 UI Kit 相关原生代码的时候，需要优先考虑下面库的提供的方法。

## 库 `kit-library`: UI工具包库，提供丰富的UI组件和工具类

### 1. 库 `SORemoteResKit`: 远程资源管理库
1. 类: SORemoteResManager 继承自: NSObject
```
// 代理对象
@property (nonatomic, weak) id<SORemoteResManagerDelegate> delegate;
// 资源配置
@property (nonatomic, strong, readonly) SORemoteResConfig *resConfig;
// 获取共享管理器
+ (SORemoteResManager *)sharedManager;
// 添加资源配置
- (BOOL)addResConfigWithPlistName:(NSString *)plistName
                           bundle:(NSBundle *_Nullable)bundle
                     withDownload:(BOOL)isDownload;
// 添加资源配置并覆盖
- (BOOL)addResConfigWithPlistName:(NSString *)plistName
                           bundle:(NSBundle *_Nullable)bundle
                     withDownload:(BOOL)isDownload
                         coverAdd:(BOOL)coverAdd;
// 添加资源配置从Plist文件
- (BOOL)addResConfigWithPlist:(NSString *)plistPath
                 withDownload:(BOOL)isDownload
                     coverAdd:(BOOL)coverAdd;
// 添加资源配置从包名和配置字典
- (BOOL)addResConfigWithPackName:(NSString *)packName
                         confDic:(NSDictionary<NSString *, NSString *> *)confDic
                    withDownload:(BOOL)isDownload
                        coverAdd:(BOOL)coverAdd;
// 更新包状态
- (void)updatePackStatus:(NSString *)packName;
// 下载所有资源
- (BOOL)downloadAll;
// 下载指定包
- (BOOL)downloadWithPackName:(NSString *)packName;
// 重连下载
- (void)reconnectDownloads;
// 处理后台URL会话事件
- (BOOL)handleEventsForBackgroundURLSession:(NSString *)identifier completionHandler:(void (^)(void))completionHandler;
// 移除所有资源
- (void)removeAll;
// 移除指定包
- (void)removeWithPackName:(NSString *)packName;
// 移除指定资源
- (void)removeWithResKey:(NSString *)resKey;
// 清理过期资源
- (void)cleanExpiredRes:(BOOL)force;
// 检查所有资源是否有效
- (BOOL)isAllResourceValid;
// 根据资源键获取资源项
- (SORemoteResItem *)resItemWithResKey:(NSString *)resKey;
// 根据包名获取资源包
- (SORemotePackage *)resPackWithPackName:(NSString *)packName;
// 检查包是否存在
- (BOOL)isPackExist:(NSString *)packName;
// 注册包有效回调
- (void)registPack:(NSString *)packName forValidCallback:(SORemoteResPackBlock)callback;
// 检查资源是否存在
- (BOOL)isResExist:(NSString *)resKey;
// 获取资源路径
- (NSString *_Nullable)resPathWithResKey:(NSString *)resKey;
// 请求资源
- (void)requestResWithKey:(NSString *)resKey callback:(_Nullable SORemoteResBlock)callback;
// 请求资源带优先级
- (void)requestResWithKey:(NSString *)resKey
      cacheNoConfigResKey:(BOOL)isCache
                 priority:(SODownloadPriority)priority
                  preempt:(BOOL)preempt
                 callback:(_Nullable SORemoteResBlock)callback;
// 执行并清除补丁请求
- (void)performAndClearPatchRequest;
// 观察资源下载进度
- (void)observeResKey:(NSString *)resKey withDownloadProgress:(SORemoteResDownloadProgressBlock)downloadCallback;
// 请求临时资源
- (void)requestTempResWithURL:(NSURL *)resURL callback:(_Nullable SORemoteResBlock)callback;
// 请求临时资源带优先级
- (void)requestTempResWithURL:(NSURL *)resURL
                       priority:(SODownloadPriority)priority
                        preempt:(BOOL)preempt
                       callback:(_Nullable SORemoteResBlock)callback;
```
2. 类: SORemoteResItem 继承自: NSObject
```
// 引用包
@property (nonatomic, weak) SORemotePackage *refPack;
// 配置值字符串
@property (nonatomic, strong) NSString *confValString;
// 配置类型
@property (nonatomic, assign) SORemoteResItemConfType confType;
// 包内名称
@property (nonatomic, strong) NSString *inPackName;
// 资源名称
@property (nonatomic, strong) NSString *resName;
// 资源状态
@property (nonatomic, assign) SORemoteResItemStatus status;
// 状态字符串
@property (nonatomic, strong, readonly) NSString *statusStr;
// 资源URL字符串
@property (nonatomic, strong) NSString *resUrlStr;
// 资源URL签名
@property (nonatomic, strong) NSString *resUrlSign;
// 资源保存路径
@property (nonatomic, strong) NSString *resSavedPath;
// 期望资源大小
@property (nonatomic, assign) NSInteger expectedResSize;
// 期望资源签名
@property (nonatomic, strong) NSString *expectedResSign;
// 最后磁盘检查时间
@property (nonatomic, strong) NSDate *lastDiskCheckTime;
// 有效通知对象数组
@property (atomic, strong) SOSafeMutableArray *validNotiObjArray;
// 下载进度通知对象数组
@property (atomic, strong) SOSafeMutableArray *dpNotiObjArray;
// 下载优先级
@property (nonatomic, assign) SODownloadPriority downloadPriority;
// 抢占下载
@property (nonatomic, assign) BOOL preemptDownload;
```
3. 分类: UIImageView
```
// 设置远程资源图片
- (void)soSetImageWithResKey:(NSString *)resKey;
// 设置远程资源图片带占位图
- (void)soSetImageWithResKey:(NSString *)resKey placeholderImage:(nullable UIImage *)placeholderImage;
// 取消远程资源首次有效响应
- (void)soCancelRemoteResFirstValidResponse;
```
4. 分类: UIButton
```
// 设置远程资源图片
- (void)soSetImageWithResKey:(NSString *)resKey forState:(UIControlState)state;
// 设置远程资源图片带占位图
- (void)soSetImageWithResKey:(NSString *)resKey forState:(UIControlState)state placeholderImage:(nullable UIImage *)placeholderImage;
// 设置远程资源背景图片
- (void)soSetBackgroundImageWithResKey:(NSString *)resKey forState:(UIControlState)state;
// 设置远程资源背景图片带占位图
- (void)soSetBackgroundImageWithResKey:(NSString *)resKey forState:(UIControlState)state placeholderImage:(nullable UIImage *)placeholderImage;
// 取消远程资源首次有效响应
- (void)soCancelRemoteResFirstValidResponse;
```

### 2. 库 `SOAlertKit`: 弹窗组件库
1. 类: SOAlertHands 继承自: NSObject
```
// 键盘是否开启
@property (nonatomic, assign) BOOL isKeyboardOn;
// 获取共享实例
+ (instancetype _Nullable)sharedHands;
// 获取容器宽度
+ (CGFloat)containerWidth;
// 创建弹窗
- (UIView<SOAlertViewControlProtocol> *)createAlertWithTopImage:(id _Nullable)topImage
                                                 topImageHeight:(CGFloat)topImageHeight
                                                          title:(NSString * _Nullable)title
                                                        content:(id _Nullable)content
                                                   alertButtons:(NSArray<SOAlertButton *> * _Nullable)alertButtons
                                                   bottomButton:(SOAlertButton * _Nullable)bottomButton;
// 创建元素
- (SOAlertElement<SOAlertElement> *)createElementWithClass:(Class)ElementClass content:(id)content;
// 创建弹窗带数据
- (SOAlertElement<SOAlertViewControlProtocol> *)createAlertWithData:(NSArray<NSArray<UIView<SOAlertElement> *> *> *)alertData;
// 创建弹窗带数据和内边距
- (UIView<SOAlertViewControlProtocol> *)createAlertWithData:(NSArray<NSArray<UIView<SOAlertElement> *> *> *)alertData
                                                 insetArray:(NSArray<NSNumber *> *)insetArray;
// 添加弹窗
- (void)addAlert:(id<SOAlertViewControlProtocol>)alert;
// 添加弹窗带顺序
- (void)addAlert:(id<SOAlertViewControlProtocol>)alert order:(SOAlertGlobalTaskOrder)order;
// 添加弹窗任务
- (void)addAlertTask:(Operation)operation order:(SOAlertGlobalTaskOrder)order;
// 隐藏弹窗
- (void)hideAlert:(id<SOAlertViewControlProtocol>)alert;
// 隐藏当前弹窗
- (void)hideCurrentAlert;
// 建议顶部图片框架
- (CGRect)suggestTopImageFrameWithData:(id)data;
```
2. 类: SOAlertButton 继承自: SOAlertElement
```
// 按钮
@property (nonatomic, strong) UIButton *button;
// 按钮动作
@property (nonatomic, copy) SOButtonAction soAction;
// 按钮样式
@property (nonatomic, assign) SOALertButtonStyle soButtonStyle;
// 自动关闭弹窗
@property (nonatomic, assign) BOOL autoCloseAlert;
// 图片视图
@property (nonatomic, strong) SOWebImageView *imageView;
// 添加动作（已废弃）
- (void)addAction:(SOButtonAction)action __deprecated_msg("已废弃，请使用setSoAction:");
```

### 3. 库 `SoulDownloadKit`: 下载管理库
1. 类: SODownloaderManager 继承自: NSObject
```
// 默认下载路径
@property (nonatomic, copy, readonly) NSString *defaultDownloadPath;
// 下载数量
@property (nonatomic, assign, readonly) NSUInteger downloadCount;
// 当前下载数量
@property (nonatomic, assign, readonly) NSUInteger currentDownloadsCount;
// 最大并发数量
@property (nonatomic, assign, readonly) NSInteger maxConcurrentCount;
// 最小文件大小处理成功
@property (nonatomic, assign) unsigned long long minFileSizeTreatSuccess;
// 后台完成处理器
@property (copy, nonatomic, nullable) void (^backgroundCompletionHandler)(void);
// 回调队列
@property (strong, nonatomic) dispatch_queue_t callbackQueue;
// 自动重试
@property (nonatomic, assign) BOOL autoRetry;
// 获取共享实例
+ (instancetype)sharedInstance;
// 初始化带标识符
- (instancetype)initWithIdentifier:(NSString *)identifier NS_DESIGNATED_INITIALIZER;
// 初始化带标识符和自动重连
- (instancetype)initWithIdentifier:(NSString *)identifier autoReconnect:(BOOL)autoReconnect NS_DESIGNATED_INITIALIZER;
// 是否使用后台下载
+ (BOOL)useBgDownload;
// 设置使用后台下载
+ (void)setUseBackgroundDownload:(BOOL)useBgDownload;
// 设置默认下载路径
- (BOOL)setDefaultDownloadPath:(NSString *)pathToDL error:(NSError *__autoreleasing *)error;
// 设置最大并发下载数
- (void)setMaxConcurrentDownloads:(NSInteger)max;
// 更新管理器标签
- (void)updateManagerTag:(NSString *)managerTag;
// 开始下载
- (void)startDownload:(SODownloadModel *)download;
// 暂停下载
- (void)suspendDownload:(SODownloadModel *)download;
// 暂停所有下载
- (void)suspendAllDownloads;
// 恢复下载
- (void)resumeDownload:(SODownloadModel *)download;
// 恢复所有下载
- (void)resumeAllDownloads;
// 取消下载
- (void)cancelDownload:(SODownloadModel *)download;
// 取消所有下载
- (void)cancelAllDownloads;
// 限制蜂窝网络访问
- (void)restrictCellularAccessForDownloads:(NSArray <SODownloadModel *>*)downloads;
// 允许蜂窝网络访问
- (void)allowCellularAccessForDownloads:(NSArray <SODownloadModel *>*)downloads;
// 尝试重连下载
- (void)tryReconnectDownloads:(SODownloadTaskReconnectBlock _Nullable)reconnectBlock;
// 处理后台URL会话事件
- (BOOL)handleEventsForBackgroundURLSession:(NSString *)identifier completionHandler:(void (^)(void))completionHandler;
// 根据URL获取下载模型
- (SODownloadModel * _Nullable)downloadModelWithURL:(NSURL *)url;
// 开始下载带URL和自定义路径
- (SODownloadModel * _Nullable)startDownloadWithURL:(NSURL * _Nullable)url customPath:(NSString * _Nullable)customPathOrNil;
// 开始下载带URL
- (SODownloadModel * _Nullable)startDownloadWithURL:(NSURL * _Nullable)url;
// 枚举临时目录
- (void)enumTemporaryDir:(SODownloaderManagerTempPathEnumBlock)enumBlock;
```
2. 类: SODownloadModel 继承自: NSObject
```
// 数据任务
@property (nonatomic, strong, readonly) NSURLSessionTask *dataTask;
// 下载URL
@property (nonatomic, copy, readonly) NSURL *downloadURL;
// 文件路径
@property (nonatomic, copy, readonly, getter=pathToFile) NSString *pathToFile;
// 下载目录路径
@property (nonatomic, copy, readonly) NSString *pathToDownloadDirectory;
// 文件名
@property (nonatomic, copy, getter=fileName) NSString *fileName;
// 下载状态
@property (nonatomic, assign, readonly) SODownloadState state;
// 期望数据长度
@property (nonatomic, assign, readonly) uint64_t expectedDataLength;
// 已接收数据长度
@property (nonatomic, assign, readonly) uint64_t receivedDataLength;
// 下载进度
@property (nonatomic, assign, readonly, getter = progress) float progress;
// 允许蜂窝网络访问
@property (assign, nonatomic, readonly) BOOL allowCellularAccess;
// 缓存策略
@property (nonatomic, assign) NSURLRequestCachePolicy cachePolicy;
// 标识符
@property (copy, nonatomic, readonly) NSString *identifier;
// 忽略之前文件
@property (nonatomic, assign) BOOL ignorePreviousFile;
// 下载优先级
@property (nonatomic, assign) NSInteger downloadPriority;
// 抢占下载
@property (nonatomic, assign) BOOL preemptDownload;
// 用户给定文件大小
@property (nonatomic, assign) uint64_t fileSizeGivenByUser;
// 当前重试次数
@property (nonatomic, assign) NSInteger curRetryCount;
// 添加回调
@property (nonatomic, copy) SODownloadModelCommonBlock addCallback;
// 接收首次响应回调
@property (nonatomic, copy) SODownloadModelCommonBlock receiveFirstResponseCallback;
// 进度回调
@property (nonatomic, copy) SODownloadModelCommonBlock progressCallback;
// 成功回调
@property (nonatomic, copy) SODownloadModelCommonBlock successCallback;
// 失败回调
@property (nonatomic, copy) SODownloadModelCommonBlock failedCallback;
// 完成回调
@property (nonatomic, copy) SODownloadModelCommonBlock completeCallback;
// 暂停回调
@property (nonatomic, copy) SODownloadModelCommonBlock suspendCallback;
// 恢复回调
@property (nonatomic, copy) SODownloadModelCommonBlock resumeCallback;
// 取消回调
@property (nonatomic, copy) SODownloadModelCommonBlock cancelCallback;
// 相同项目数组
@property (nonatomic, strong) NSMutableArray<SODownloadModel *> *sameItems;
// 引用主项目
@property (nonatomic, weak) SODownloadModel *refMainItem;
// 添加时间
@property (nonatomic, strong) NSDate *addedTime;
// 开始时间
@property (nonatomic, strong) NSDate *startTime;
// 完成时间
@property (nonatomic, strong) NSDate *finishTime;
// 完成状态
@property (nonatomic, assign) SODownloadState finishState;
// 下载时间
@property (nonatomic, assign) NSTimeInterval downloadTime;
// 实际下载时间
@property (nonatomic, assign) NSTimeInterval realDownloadTime;
// 下载时间比率
@property (nonatomic, assign) double downloadTimeRatio;
// 初始化
- (instancetype)initWithURL:(NSURL *)url
        allowCellularAccess:(BOOL)allowCellularAccess
               downloadPath:(NSString *)pathToDLOrNil;
// 移除文件
- (BOOL)removeFileWithError:(NSError *__autoreleasing *)error;
// 更新下载路径
- (void)updateDownloadPath:(NSString *)downloadPath;
// 绑定下载任务并设置描述
- (void)bindDownloadTaskAndSetDescription:(NSURLSessionDownloadTask *)downloadTask;
// 复制数据任务
- (void)copyDataTask:(SODownloadModel *)downloadModel;
// 处理添加
- (instancetype)handleAdded:(SODownloadModelCommonBlock)callback;
// 处理接收首次响应
- (instancetype)handleReceiveFirstResponse:(SODownloadModelCommonBlock)callback;
// 处理进度
- (instancetype)handleProgress:(SODownloadModelCommonBlock)callback;
// 处理成功
- (instancetype)handleSuccess:(SODownloadModelCommonBlock)callback;
// 处理失败
- (instancetype)handleFailed:(SODownloadModelCommonBlock)callback;
// 处理完成
- (instancetype)handleCompleted:(SODownloadModelCommonBlock)callback;
// 处理暂停
- (instancetype)handleSuspended:(SODownloadModelCommonBlock)callback;
// 处理恢复
- (instancetype)handleResumed:(SODownloadModelCommonBlock)callback;
// 处理取消
- (instancetype)handleCanceled:(SODownloadModelCommonBlock)callback;
// 智能设置完成时间
- (void)smartSetFinishTime:(NSDate *)finishTime;
```

### 4. 库 `SoulSearchKit`: 搜索组件库
1. 类: SOSearchBar 继承自: SOSearchComponentBaseView
```
// 代理对象
@property (nonatomic, weak) id<SOSearchBarDelegate> delegate;
// 视图样式
@property (nonatomic, assign) SOSearchBarStyle viewStyle;
// 文本数量单位
@property (nonatomic, assign) SOSearchBarTextNumUnit textUnit;
// 最大搜索文本数量
@property (nonatomic, assign) NSInteger maxSearchTextNum;
// 搜索字符串
@property (nonatomic, strong) NSString *searchString;
// 占位符字符串
@property (nonatomic, strong) NSString *placeholderSting;
// 自定义操作视图宽度
@property (nonatomic, assign) CGFloat customOpViewWidth;
// 是否解析标记字符串
@property (nonatomic, assign) BOOL isParseMarkedString;
// 启用大屏幕调整
@property (nonatomic, assign) BOOL enableLargeScreenAdjust;
// 内容输入框
@property (nonatomic, strong, readonly) UITextField *contentField;
// 自定义视图容器
@property (nonatomic, strong, readonly) UIView *customViewContainer;
// 搜索栏是否应该开始编辑
- (BOOL)soSearchBarShouldBeginEditing:(SOSearchBar *)searchBar;
// 搜索栏开始编辑
- (void)soSearchBarDidBeginEditing:(SOSearchBar *)searchBar;
// 搜索栏结束编辑
- (void)soSearchBarDidEndEditing:(SOSearchBar *)searchBar;
// 搜索栏是否应该返回
- (BOOL)soSearchBarShouldReturn:(SOSearchBar *)searchBar;
// 搜索栏文本改变
- (void)soSearchBar:(SOSearchBar *)searchBar textDidChange:(NSString *)searchText;
// 搜索栏需要开始搜索
- (void)soSearchBar:(SOSearchBar *)searchBar needStartSearch:(NSString *)searchText;
// 搜索栏取消按钮点击
- (void)soSearchBarCancelButtonClicked:(SOSearchBar *)searchBar;
// 搜索栏文本数量达到上限
- (void)soSearchBarTextNumToCeil:(SOSearchBar *)searchBar;
// 搜索栏空删除回退
- (void)soSearchBarEmptyDeleteBack:(SOSearchBar *)searchBar;
// 搜索栏文本改变带标记字符串
- (void)soSearchBar:(SOSearchBar *)searchBar
      textDidChange:(NSString *)searchText
       markedString:(NSString *)markedString;
```

### 5. 库 `SoulCalendarKit`: 日历组件库
1. 类: SoulCalendar 继承自: NSObject
```
// 共享实例
@property (nonatomic, class, readonly, strong) SoulCalendar * _Nonnull sharedInstance;
// 事件存储
@property (nonatomic, readonly, strong) EKEventStore * _Nonnull store;
// 事件日历
@property (nonatomic, strong) EKCalendar * _Nullable eventCalendar;
// 获取共享实例
+ (SoulCalendar * _Nonnull)sharedInstance SWIFT_WARN_UNUSED_RESULT;
// 请求访问权限
- (void)requestAccessTo:(enum SoulEntityType)entityType completion:(EKEventStoreRequestAccessCompletionHandler _Nonnull)completion;
// 添加事件数组
- (NSArray<NSString *> * _Nullable)addWithEvents:(NSArray<SoulCalendarEvent *> * _Nonnull)events error:(NSError * _Nullable * _Nullable)error;
// 添加单个事件
- (NSString * _Nullable)addWithEvent:(SoulCalendarEvent * _Nonnull)event error:(NSError * _Nullable * _Nullable)error;
```

### 6. 库 `SOReleasePostEventKit`: 发布事件监控库
1. 类: SOPublishEventPerformanceMonitoringSession 继承自: NSObject
```
// 共享实例
@property (nonatomic, class, readonly, strong) SOPublishEventPerformanceMonitoringSession * _Nonnull shared;
// 获取共享实例
+ (SOPublishEventPerformanceMonitoringSession * _Nonnull)shared SWIFT_WARN_UNUSED_RESULT;
// 开始监控
- (void)start;
// 执行核心发布器
- (void)performCorePublisherWithScene:(NSInteger)scene info:(NSDictionary<NSString *, id> * _Nullable)info;
// 执行文本编辑
- (void)performTextEditWithScene:(NSInteger)scene info:(NSDictionary<NSString *, id> * _Nullable)info;
// 执行图片编辑
- (void)performImageEditWithScene:(NSInteger)scene info:(NSDictionary<NSString *, id> * _Nullable)info;
// 执行视频编辑
- (void)performVideoEditWithScene:(NSInteger)scene info:(NSDictionary<NSString *, id> * _Nullable)info;
// 执行捕获编辑
- (void)performCaptureEditWithScene:(NSInteger)scene info:(NSDictionary<NSString *, id> * _Nullable)info;
// 执行图片模板编辑
- (void)performImageTemplateEditWithScene:(NSInteger)scene info:(NSDictionary<NSString *, id> * _Nullable)info;
// 创建错误
+ (NSError * _Nonnull)errorWithReason:(enum SOPublishFailureReason)reason module:(NSString * _Nonnull)module_ message:(NSString * _Nonnull)message SWIFT_WARN_UNUSED_RESULT;
// 创建错误带描述
+ (NSError * _Nonnull)errorWithReason:(enum SOPublishFailureReason)reason module:(NSString * _Nonnull)module_ message:(NSString * _Nonnull)message description:(NSString * _Nullable)description SWIFT_WARN_UNUSED_RESULT;
// 成功回调
+ (void)successWithInfo:(NSDictionary<NSString *, id> * _Nullable)info;
// 失败回调
+ (void)failureWithReason:(NSInteger)reason message:(NSString * _Nullable)message extra:(NSString * _Nullable)extra info:(NSDictionary<NSString *, id> * _Nullable)info;
// 取消回调
+ (void)cancelWithInfo:(NSDictionary<NSString *, id> * _Nullable)info;
```

这个UI Kit库提供了完整的UI组件解决方案，包括远程资源管理、弹窗组件、下载管理、搜索组件、日历组件和发布事件监控等功能模块，支持资源缓存、异步下载、用户交互、数据展示等多种UI开发需求。