---
description: 
globs: 
alwaysApply: false
---
# 库介绍
下面枚举了 Flutter 相关的库，生成 Flutter 相关原生代码的时候，需要优先考虑下面库的提供的方法。

## 库 `flutter-library`: Flutter混合开发框架库，提供Flutter与原生交互的完整解决方案

### 1. 库 `SOFlutterSDK`: Flutter SDK核心库
1. 类: SOFlutterPlatformView 继承自: NSObject
```
// 初始化平台视图
- (instancetype)initWithFrame:(CGRect)frame
               viewIdentifier:(int64_t)viewId
                    arguments:(id _Nullable)args
              binaryMessenger:(NSObject<FlutterBinaryMessenger>*)messenger;
// 绑定通道提供者
- (void)bindChannelProvides:(NSArray<SOPlatformViewChannelProvide *> *)provides;
```
2. 类: SOFlutterViewController 继承自: FBFlutterViewContainer
```
// 展示视图控制器
@property (nonatomic, weak) UIViewController *sof_presentViewController;
// 设置页面名称和参数
- (void)setName:(NSString *)name params:(NSDictionary *)params;
// 页面消失回调
- (void)didDismissToParentViewController;
// 绑定通道提供者
- (void)bindChannelProvides:(NSArray<SOFlutterChannelProvide *> *)provides;
```
3. 类: SOFlutterEngine 继承自: NSObject
```
// 引擎销毁开始回调
- (void)onEngineDestoryStart;
// 引擎销毁结束回调
- (void)onEngineDestoryEnd;
// 引擎重置结束回调
- (void)onEngineResetEnd;
// 获取共享实例
+ (instancetype)shared;
// 运行引擎
- (void)run;
// 清理引擎
- (void)clear;
// 重置引擎
- (void)resetEngine;
// 检查引擎是否已准备
- (BOOL)isAlready;
// 获取Flutter引擎
- (FlutterEngine *)engine;
// 添加监听器
- (void)addListener:(void(^)(BOOL isAlready))listener;
// 添加引擎代理
- (void)addEngineDelegate:(id<SOFlutterEngineProtocol>)delegate;
// 检查是否将要重置引擎
- (void)checkWillResetEngine;
// 关闭重置引擎AB测试
- (BOOL)closeResetEngineAB;
// 记录Flutter页面追踪
- (void)recordFlutterTrackPage:(NSString *)flutterPageName;
// 获取Flutter追踪页面列表
- (NSArray<NSString *> *)flutterTrackPages;
```
4. 类: SOFRouter 继承自: NSObject
```
// 导航控制器
@property (nonatomic, weak) UINavigationController *navigationController;
// 当前视图控制器
@property (nonatomic, weak) UIViewController<FBFlutterContainer> *currentViewController;
// 注册处理器
- (void)registerHandler:(NSString*)handlerName handler:(CustomHandler)handler;
// 移除处理器
- (void)removeHandler:(NSString*)handlerName;
// 销毁路由器
- (void)destory;
// 推送Flutter路由
- (void)pushFlutterRoute:(NSString *)flutterPage arguments:(nullable NSDictionary *)arguments;
// 推送Flutter路由带处理器
- (void)pushFlutterRoute:(FlutterBoostRouteOptions *)options handler:(PageHandler)handler closed:(PageClosedHandler)closedHanlder;
// 推送Flutter路由带原生路径
- (void)pushFlutterRoute:(NSString *)flutterPage nativePath:(NSString *)nativePath arguments:(nullable NSDictionary *)arguments;
// 推送Flutter路由带结果回调
- (void)pushFlutterRoute:(NSString *)flutterPage nativePath:(NSString *)nativePath arguments:(nullable NSDictionary *)arguments result:(nullable void(^)(NSDictionary *))result;
// 获取默认Flutter视图控制器
- (void)defaultFlutterViewController:(FlutterBoostRouteOptions *)options completion:(void(^)(UIViewController *flutterVC))block;
```

### 2. 库 `SOFNetworkPlugin`: Flutter网络插件库
1. 类: SOFHttpPlugin 继承自: SOFChannelNative
```
// 根据类型获取域名
+ (NSString *)domainWithType:(NSString *)type;
```
2. 类: SOFDownloadPlugin 继承自: SOFChannelNative
```
// 获取下载插件实例
+ (SOFDownloadPlugin* )instance;
```
3. 类: SOFRequest 继承自: NSObject
```
// 发起网络请求
+ (void)startRequestWithURLPath:(NSString *)URLPath
                        baseUrl:(NSString *)baseUrl
                         method:(NSString *)method
                         header:(NSDictionary *)header
                          param:(NSDictionary *)param
                        success:(void(^)(NSDictionary *))success
                        failure:(void(^)(NSInteger code, NSString *msg))failure;
```
4. 类: SOFRequestParams 继承自: NSObject
```
// 请求路径
@property (nonatomic, copy) NSString *path;
// 请求域名
@property (nonatomic, copy) NSString *domain;
// 请求方法
@property (nonatomic, copy) NSString *method;
// 请求头
@property (nonatomic, copy) NSDictionary *header;
// 请求数据
@property (nonatomic, copy) NSDictionary *data;
// 请求参数
@property (nonatomic, copy) NSDictionary *params;
```

### 3. 库 `SOFUIPlugins`: Flutter UI插件库
1. 类: SOFTextPresenter 继承自: NSObject
```
// 初始化文本展示器
- (instancetype)initArgs:(NSDictionary *)args;
// 销毁资源
- (void)dispose;
// 获取表情边界
+ (CGRect)getEmojiBoundsWithFont:(nullable UIFont*)font emojiBounds:(CGRect)emojiBounds;
// 获取字体
+ (UIFont *)getFont:(NSDictionary *)args;
```
2. 类: SoLottiePlatformView 继承自: SOFlutterPlatformView
```
// Lottie动画视图
@property (nonatomic, strong) LOTAnimationView *animationView;
```
3. 类: SoPAGPlatformView 继承自: SOFlutterPlatformView
```
// PAG动画视图
@property (nonatomic, strong) PAGView *animationView;
```
4. 类: SOFlutterPagePlugin 继承自: SOFChannelNative
```
// 获取页面插件实例
+ (SOFlutterPagePlugin* )instance;
// 添加页面处理器
- (void)addPageHandler:(PageHandler)handler uniqueId:(NSString* )uniqueId;
// 添加页面关闭处理器
- (void)addPageClosedHandler:(PageClosedHandler)handler uniqueId:(NSString* )uniqueId;
```

### 4. 库 `SOFMediaPlugin`: Flutter媒体插件库
1. 类: SOVideoPlayerPlugin 继承自: SOFChannelNative
```
// 方法处理器
@property (nonatomic, strong, readonly) VideoPlayerMethodHandler *methodHandler;
// 获取视频播放器插件实例
+ (SOVideoPlayerPlugin* )instance;
```
2. 类: SOFMediaPlugin 继承自: SOFChannelNative
```
// 媒体插件代理
@property (nonatomic, weak) id<SOFMediaPluginDelegate> delegate;
// 获取媒体插件实例
+ (SOFMediaPlugin* )instance;
// 选择音频连麦
- (void)selectAudioLinkup;
// 选择视频连麦
- (void)selectVideoLinkup:(SOVPAvatarResource * _Nonnull)avatar;
// 关闭连麦
- (void)closeLinkup;
```
3. 类: SOFAvatarPlugin 继承自: SOFChannelNative
```
// 获取头像插件实例
+ (SOFAvatarPlugin* )instance;
```
4. 类: SOFImagePlugin 继承自: SOFChannelNative
```
```
5. 类: SOFAudioPlugin 继承自: SOFChannelNative
```
// 获取音频插件实例
+ (SOFAudioPlugin* )instance;
```
6. 类: VideoPlayerService 继承自: NSObject
```
// 视频播放器代理
@property (nonatomic, weak) id<SOVideoPlayerDelegate> delegate;
// 当前视图ID
@property (nonatomic, assign) NSInteger curViewId;
// 开始Alpha视频播放
- (void)startAlphaVideoPlayerWithPath:(NSString *)path inView:(UIView *)view;
// 开始命名视频播放
- (void)startVideoPlayerWithNamed:(NSString *)name type:(NSString *)type inView:(UIView *)view;
// 开始路径视频播放
- (void)startVideoPlayerWithPath:(NSString *)path inView:(UIView *)view;
// 启用重复播放
- (void)enableRepeat:(BOOL)repeat;
// 启用静音
- (void)enableMute:(BOOL)isMute;
// 销毁视频播放器
- (void)destoryVideoPlayer;
// 停止视频播放
- (void)stopVideoPlayer;
// 重播
- (void)replay;
// 跳转到指定时间
- (void)seekTime:(CGFloat)time;
```

### 5. 库 `SOFMetaPlugin`: Flutter元宇宙插件库
1. 类: SOFMetaPlugin 继承自: SOFChannelNative
```
// 元宇宙插件代理
@property (nonatomic, weak) id<SOFMetaPluginDelegate> delegate;
// 显示键盘
- (void)showKeyboard;
// 隐藏键盘
- (void)hideKeyboard;
// 获取元宇宙插件实例
+ (SOFMetaPlugin* )instance;
// 更新下载进度
- (void)updateDownloadProcess:(NSInteger)process;
// 移除介绍页面
- (void)removeInstroducePage;
```
2. 类: SOFKeyboardController 继承自: NSObject
```
// 目标用户ID加密
@property (nonatomic, strong) NSString *toUserIdEcpt;
// 面板用户ID加密
@property (nonatomic, strong) NSString *boardUserIdEcpt;
// 父消息ID
@property (nonatomic, strong) NSNumber *parentMsgId;
// 模块类型
@property (nonatomic, strong) NSString *moduleType;
// 消息类型
@property (nonatomic, strong) NSNumber *messageType;
// 关闭键盘回调
@property (nonatomic, copy) void(^closeKeyboard)(void);
// 显示键盘
- (void)showKeyboard;
```
3. 类: SOMetaBubblingInputBar 继承自: SOComponentBaseView
```
// 输入栏代理
@property (nonatomic, weak) NSObject<SOMetaBubblingInputBarDelegate> *delegate;
// 最大文本数量
@property (nonatomic, assign) NSInteger maxTextNum;
// 标签文本数量
@property (nonatomic, assign) NSInteger tagTextNum;
// 文本视图最小宽度
@property (nonatomic, assign) NSInteger textViewMinWidth;
// 文本视图最小高度
@property (nonatomic, assign) NSInteger textViewMinHeight;
// 文本视图最大宽度
@property (nonatomic, assign) NSInteger textViewMaxWidth;
// 文本视图最大高度
@property (nonatomic, assign) NSInteger textViewMaxHeight;
// 文本属性字典
@property (nonatomic, strong) NSDictionary *textAttrDic;
// 内容背景视图
@property (nonatomic, strong, readonly) UIView *contentBgView;
// 内容文本视图
@property (nonatomic, strong, readonly) SOInputBarTextView *contentTextView;
// 是否显示表情面板
@property (nonatomic, assign) BOOL showEmojiBoard;
// 输入栏文本数量达到上限回调
- (void)inputBarTextNumToCeil:(SOMetaBubblingInputBar *)inputBar;
// 输入栏输入回车回调
- (BOOL)inputBarInputReturn:(SOMetaBubblingInputBar *)inputBar;
// 输入栏删除回退回调
- (BOOL)inputBarInputDeleteBackward:(SOMetaBubblingInputBar *)inputBar;
// 输入栏视图框架更新回调
- (void)inputBarViewFrameUpdated:(SOMetaBubblingInputBar *)richBoard;
// 设置约束
- (void)setupConstraints;
// 更新视图显示
- (void)updateViewDisplay;
```

### 6. 库 `SOFChatPlugin`: Flutter聊天插件库
1. 类: SOFGiftPlugin 继承自: SOFChannelNative
```
// 礼物插件代理
@property (nonatomic, weak) id<SOFGiftPluginDelegate> delegate;
// 获取礼物插件实例
+ (SOFGiftPlugin* )instance;
// 关闭礼物弹窗
- (void)closeGiftPopView;
// 发送礼物
- (void)sendGift:(id)giftPanelInfo giftInfo:(id)giftInfo fromBag:(BOOL)fromBag receiveGiftUserIds:(NSArray *)receiveGiftUserIds;
```
2. 类: SOFAllaySorrowPlugin 继承自: SOFChannelNative
```
// 获取悲伤插件实例
+ (SOFAllaySorrowPlugin* )instance;
// 接收退出消息
- (void)didReceiveExitMessage:(NSString* )roomId
                      message:(NSString* )message;
// 接收消息
- (void)didReceiveMessage:(NSString* )qId
                    event:(NSInteger)event;
// 接收带持续时间的消息
- (void)didReceiveMessage:(NSString* )qId
                    event:(NSInteger)event
              addDuration:(NSInteger)addDuration;
```

### 7. 库 `SOFUtilPlugins`: Flutter工具插件库
1. 类: SOFRefreshPagePlugin 继承自: SOFChannelNative
```
// 获取刷新页面插件实例
+ (SOFRefreshPagePlugin* )instance;
```
2. 类: SOFDnsPlugin 继承自: SOFChannelNative
```
// DNS映射表
@property (nonatomic, strong) NSDictionary *dnsMap;
// 获取DNS插件实例
+ (SOFDnsPlugin* )instance;
```
3. 类: SOFSharePlugin 继承自: SOFChannelNative
```
// 获取分享插件实例
+ (SOFSharePlugin* )instance;
```
4. 类: SOFMessagePlugin 继承自: SOFChannelNative
```
// 获取消息插件实例
+ (SOFMessagePlugin* )instance;
// 插入消息
+ (void)insertMessages:(NSArray*)messages;
// 查询总未读消息数
+ (void)queryTotalUnreadMessages:(void (^)(NSInteger count))responseCallback;
// 释放气泡
+ (void)releaseBubble:(NSDictionary*)bubble bubblingType:(NSUInteger)bubblingType isClockInAlertShowed:(BOOL)isClockInAlertShowed;
// 创建图片路径
- (NSString *)createPathWithImg:(UIImage *)image;
```
5. 类: SOFIMPlugin 继承自: SOFChannelNative
```
// 获取IM插件实例
+ (SOFIMPlugin* )instance;
```

### 8. 库 `SOFRTCPlugin`: Flutter RTC插件库
1. 类: RTCPlugin 继承自: SOFChannelNative
```
// 方法处理器
@property (nonatomic, strong, readonly) RTCMethodHandler *methodHandler;
// 获取RTC插件实例
+ (RTCPlugin* )instance;
// 开始预览
- (void)startPreview:(UIView * _Nonnull)renderView;
// 停止预览
- (void)stopPreview;
// 是否显示马赛克
- (BOOL)isShowingMosaic;
// 是否已加载头像或普通项目
- (BOOL)hasLoadAvatarOrNormalItem;
// 销毁画布
- (void)destroyCanvas:(NSString *)userId;
// 设置远程画布
- (void)setRemoteCanvas:(UIView *)view userId:(NSInteger)userId;
// 设置媒体画布
- (void)setMediaCanvas:(UIView *)view;
// 销毁媒体画布
- (void)destroyMediaCancas;
```
2. 类: RTCService 继承自: NSObject
```
// RTC令牌
@property (nonatomic, strong, nullable) NSString *rtcToken;
// RTC服务
@property (nonatomic, readonly) id<SoulRTCServiceProtocol> service;
// 创建服务实例
+ (instancetype)service:(SoulRTCServiceType)type serviceDelegate:(nullable id <SoulRTCServiceDelegate>)serviceDelegate;
```
3. 类: MediaService 继承自: NSObject
```
// 媒体服务
@property (nonatomic, strong, readonly) XYMediaService *service;
// 头像加载器代理
@property (nonatomic, weak) id <SOVPAvatarLoaderDelegate> delegate;
// 发送像素缓冲区回调
@property (nonatomic, copy) void(^sendPixelBuffer)(CVPixelBufferRef pixelBuffer, CMTime currentTime);
// 开始预览
- (void)startPreview;
// 停止预览
- (void)stopPreview;
// 暂停预览
- (void)pausePreview;
// 恢复预览
- (void)resumePreview;
// 加载头像
- (void)loadAvatar:(id)data;
// 是否已加载头像或普通项目
- (BOOL)hasLoadAvatarOrNormalItem;
// 销毁引擎
- (void)destoryEngine;
```

这个Flutter库提供了完整的Flutter与原生交互解决方案，包括核心SDK、网络插件、UI插件、媒体插件、元宇宙插件、聊天插件、工具插件和RTC插件等功能模块，支持Flutter页面路由、原生组件嵌入、媒体播放、实时通信等功能。