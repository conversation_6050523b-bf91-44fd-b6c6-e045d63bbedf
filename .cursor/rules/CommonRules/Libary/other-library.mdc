---
description: 
globs: 
alwaysApply: false
---
# 库介绍
下面枚举了基础逻辑相关的库，生成原生代码的时候，需要优先考虑下面库的提供的方法。

## 库 `other-library`: 其他工具库，提供应用辅助、安全转换、AB测试等功能

### 1. 库 `SoulAppAssistant`: 应用辅助工具库
1. 类: SOAAManager 继承自: NSObject
```
// 应用密钥（已废弃）
@property (nonatomic, copy) NSString *appKey __attribute__((deprecated("此属性已被弃用")));
// 项目ID
@property (nonatomic, copy) NSString *pId;
// 自动停靠
@property (nonatomic, assign) BOOL autoDock;
// 数据数组
@property (nonatomic,strong) NSMutableArray *dataArray;
// H5门户回调
@property (nonatomic, copy) SOAAH5DoorBlock h5DoorBlock;
// WebP处理回调
@property (nonatomic, copy) SOAAWebpHandleBlock webpHandleBlock;
// 大图检测大小
@property (nonatomic, assign) int64_t bigImageDetectionSize;
// 启动类名
@property (nonatomic, copy) NSString *startClass;
// 视图控制器分析器黑名单
@property (nonatomic, copy) NSArray *vcProfilerBlackList;
// 键值回调字典
@property (nonatomic, strong) NSMutableDictionary *keyBlockDic;
// 支持的界面方向
@property (assign, nonatomic) UIInterfaceOrientationMask supportedInterfaceOrientations;
// 获取共享实例
+ (nonnull SOAAManager *)shareInstance;
// 安装助手
- (void)install;
// 带项目ID安装
- (void)installWithPid:(NSString *)pId;
// 带起始位置安装
- (void)installWithStartingPosition:(CGPoint) position;
// 带自定义回调安装
- (void)installWithCustomBlock:(void(^)(void))customBlock;
// 切换浏览器
- (void)toggleExplorer;
// 添加插件
- (void)addPluginWithTitle:(NSString *)title icon:(NSString *)iconName desc:(NSString *)desc pluginName:(NSString *)entryName atModule:(NSString *)moduleName;
// 添加插件带处理回调
- (void)addPluginWithTitle:(NSString *)title icon:(NSString *)iconName desc:(NSString *)desc pluginName:(NSString *)entryName atModule:(NSString *)moduleName handle:(void(^)(NSDictionary *itemData))handleBlock;
// 移除插件
- (void)removePluginWithPluginType:(SOAAManagerPluginType)pluginType;
// 移除指定插件
- (void)removePluginWithPluginName:(NSString *)pluginName atModule:(NSString *)moduleName;
// 添加启动插件
- (void)addStartPlugin:(NSString *)pluginName;
// 添加H5门户回调
- (void)addH5DoorBlock:(SOAAH5DoorBlock)block;
// 添加ANR回调
- (void)addANRBlock:(void(^)(NSDictionary *anrDic))block;
// 添加性能回调
- (void)addPerformanceBlock:(void(^)(NSDictionary *performanceDic))block;
// 添加WebP处理回调
- (void)addWebpHandleBlock:(SOAAWebpHandleBlock)block;
// 是否显示助手
- (BOOL)isShowSOAA;
// 显示助手
- (void)showSOAA;
// 隐藏助手
- (void)hiddenSOAA;
// 隐藏主窗口
- (void)hiddenHomeWindow;
```

### 2. 库 `SoulSafeTransitionLock`: 安全转换锁库
1. 分类: UINavigationController
```
// 转换锁启用状态
@property (nonatomic, assign) BOOL so_transLockEnable;
// 是否正在转换
@property (nonatomic, assign) BOOL so_isTransitioning;
// 最后设置转换时间
@property (nonatomic, strong) NSDate *so_lastSetTransitioningTime;
// 全局设置转换锁启用
+ (void)so_globalSetTransLockEnable:(BOOL)enable;
// 全局禁用转换锁三秒
+ (void)so_globalDisableTransLockForThreeSeconds;
```

### 3. 库 `SoulABStrategy`: AB测试策略库
1. 类: ABTestStrategyLoader 继承自: NSObject
```
// 多策略字典
@property (nonatomic, strong, readonly) NSDictionary *multiStrategyDictionary;
// AB组标记
@property (nonatomic, strong, readonly) NSString *abGroupMark;
// 从服务器请求数据
- (void)requestDataFromServer;
// 从服务器请求数据并立即启用
- (void)requestDataFromServerEnableImmediately:(BOOL)enable finish:(void(^)(NSDictionary<NSString *,ABTestMultiStrategy *> *dic))finishBlock;
// 从磁盘加载多测试配置
- (NSDictionary *)loadMultiTestConfigrationFromDisk;
```
2. 分类: ABTestMultiStrategy (发布相关策略)
```
// 发布弹窗草稿提醒多策略
+ (BOOL)multiStrategyForPublishPopDraftAlert;
// 发布弹窗图片可编辑提醒多策略
+ (BOOL)multiStrategyForPublishPopImageEditableAlert;
// 发布完成弹窗通知提醒多策略
+ (BOOL)multiStrategyForPublishFinishPopNotificationAlert;
// 发布主页引导AI诊断多策略
+ (BOOL)multiStrategyForPublishHomepageGuideAIDiagnosis;
// 发布主页引导社交卡片多策略
+ (BOOL)multiStrategyForPublishHomepageGuideSocialCard;
// 发布相册优化多策略
+ (BOOL)multiStrategyForPublishPhotoAlbumOptimize;
// 发布图片编辑器POI标记启用多策略
+ (BOOL)multiStrategyForPublishImageEditorPOIMarkerEnable;
// 发布启用照片转视频多策略
+ (BOOL)multiStrategyForPublishEnablePhoto2Video;
// 发布位置选择优化多策略
+ (BOOL)multiStrategyForPublishLocationSelectOptimize;
// 发布AIGC文本布局多策略
+ (BOOL)multiStrategyForPublishAIGCTextLayout;
// 发布AIGC组合多策略
+ (BOOL)multiStrategyForPublishAIGCCombine;
// 发布标签栏模板启用多策略
+ (BOOL)multiStrategyForPublishTabBarTemplateEnable;
// 发布OSS上传优化多策略
+ (BOOL)multiStrategyForPublishOSSUploadOptimize;
// 发布AIGC标签栏启用多策略
+ (BOOL)multiStrategyForPublishAIGCTabbarEnable;
// 发布标签搜索推荐多策略
+ (BOOL)multiStrategyForPublishTagSearchRecommend;
// 发布锚点启用多策略
+ (BOOL)multiStrategyForPublishAnchorEnable;
// 发布标题启用多策略
+ (NSString *)multiStrategyForPublishTitleEnable;
// 发布标签数量限制启用多策略
+ (BOOL)multiStrategyForPublishTagsCountLimitEnable;
// 发布推荐标签优化多策略
+ (BOOL)multiStrategyForPublishRecommandTagOptimize;
// 发布图片上传优化多策略
+ (BOOL)multiStrategyForPublishImageUploadOptimize;
```
3. 分类: ABTestMultiStrategy (音视频匹配策略)
```
// 音频匹配新报告URL
+ (BOOL)audioMatchNewReportUrl;
// 音频匹配功能优化2
+ (BOOL)audioMatchFuncOpt2;
// 音频匹配记录
+ (BOOL)audioMatchRecond;
// 音频匹配通话中报告优化
+ (BOOL)audioMatchInCallReportOpt;
// 音频降噪取消多策略
+ (BOOL)multiStrategyForAudioNoiseCancel;
// 音频匹配优化多策略
+ (BOOL)multiStrategyForAudioMatchingOPT;
// 音频匹配卡片优化多策略
+ (BOOL)multiStrategyForAudioMatchingCardOPT;
// 音频匹配催促停留多策略
+ (BOOL)multiStrategyForAudioMatchingUrgeStay;
// 音频匹配卡片多策略
+ (BOOL)multiStrategyForAudioMatchCard;
// 音频匹配同城卡片多策略
+ (BOOL)multiStrategyForAudioMatchSamecityCard;
// 音频匹配使用卡片优化多策略
+ (BOOL)multiStrategyForAMUseCardOpt;
// 音频匹配画中画多策略
+ (BOOL)multiStrategyForAudioMatchPIP;
// 视频匹配超级加速多策略
+ (BOOL)multiStrategyForVideoMatchSuperSpeedUp;
// 视频匹配挂断IM多策略
+ (BOOL)multiStrategyForVideoMatchHangUpIM;
```
4. 分类: ABTestMultiStrategy (聊天相关策略)
```
// 私聊IM业务多策略
+ (BOOL)multiStrategyForPrivateIMBusiness;
// 私聊同步礼物限制多策略
+ (BOOL)multiStrategyForPrivateSyncGiftLimit;
// 私聊卡片优化多策略
+ (BOOL)multiStrategyForPrivateChatCardOpt;
// 聊天列表勋章显示多策略
+ (BOOL)multiStrategyForChatListMedalShow;
// 聊天蒙版猜测按钮优化多策略
+ (BOOL)multiStrategyForChatMaskGuessBtnOpt;
// 会话限制多策略
+ (BOOL)multiStrategyForSessionLimit;
// 聊天到达推荐会话多策略
+ (BOOL)multiStrategyForChatReachRecommendSession;
// 聊天列表信号提示多策略
+ (BOOL)multiStrategyForChatListSignalTip;
// 元宇宙滚动到目标消息多策略
+ (BOOL)multiStrategyForMetaScrollToDstMsg;
// 聊天视频压缩多策略
+ (BOOL)multiStrategyForChatVideoCompress;
```

### 4. 库 `SOABSafeMutableDictionary`: 安全可变字典
1. 类: SOABSafeMutableDictionary 继承自: NSObject
```
// 根据键获取对象
- (id)objectForKey:(id)key;
// 设置键值对
- (void)setObject:(id)object forKey:(id)key;
// 移除指定键的对象
- (void)removeObjectForKey:(id)key;
```

这个其他工具库提供了应用开发中的辅助功能，包括应用调试助手、安全转换控制、AB测试策略管理等，支持插件系统、性能监控、策略配置、安全操作等多种开发和运营需求。
