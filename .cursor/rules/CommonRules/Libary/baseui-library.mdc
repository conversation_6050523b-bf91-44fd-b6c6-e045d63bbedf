---
description: 
globs: 
alwaysApply: false
---
# 库介绍
下面枚举了基础 UI 相关的库，生成 UI 相关原生代码的时候，需要优先考虑下面库的提供的方法。

## 库 `baseui-library`: 基础UI组件库，提供丰富的UI控件和工具类

### 1. 库 `SoulEmptyView`: 空状态视图管理库
1. 分类: UIScrollView 继承自: UIScrollView
```
// 空状态点击回调
@property (nonatomic, copy) SOEmptyDataSetClickBlock soEmptyTapClock;
// 空状态显示图片
@property (nonatomic, strong) UIImage *soEmptyImage;
// 空状态垂直偏移量
@property (nonatomic, assign) CGFloat soEmptyVerticalOffset;
// 空状态垂直间距
@property (nonatomic, assign) CGFloat soEmptyVerticalSpace;
// 空状态文本内容
@property (nonatomic, copy) NSString *soEmptyText;
// 空状态富文本内容
@property (nonatomic, copy) NSAttributedString *soEmptyAttributeText;
// 是否允许触摸
@property (nonatomic, assign) BOOL soShouldAllowTouch;
// 是否允许滚动
@property (nonatomic, assign) BOOL soShouldAllowScroll;
// 自定义空状态视图
@property (nonatomic, strong) UIView *soCustomEmptyView;
// 是否应该显示空状态
@property (nonatomic, assign) BOOL soEmptyDataShouldDisplay;
// 设置空状态文本和点击回调
- (void)so_setupEmptyDataText:(NSString *)text tapBlock:(SOEmptyDataSetClickBlock)tapClick;
// 设置空状态文本、图片和点击回调
- (void)so_setupEmptyDataText:(NSString *)text emptyImage:(UIImage *)image tapBlock:(SOEmptyDataSetClickBlock)tapClick;
// 设置空状态文本、图片、偏移量、间距和点击回调
- (void)so_setupEmptyDataText:(NSString *)text emptyImage:(UIImage *)image verticalOffset:(CGFloat)offset verticalSpace:(CGFloat)space tapBlock:(SOEmptyDataSetClickBlock)tapClick;
// 设置空状态富文本和点击回调
- (void)so_setupEmptyDataAttributeText:(NSAttributedString *)attributeText tapBlock:(SOEmptyDataSetClickBlock)tapClick;
// 设置空状态富文本、图片和点击回调
- (void)so_setupEmptyDataAttributeText:(NSAttributedString *)attributeText emptyImage:(UIImage *)image tapBlock:(SOEmptyDataSetClickBlock)tapClick;
// 设置空状态富文本、图片、偏移量、间距和点击回调
- (void)so_setupEmptyDataAttributeText:(NSAttributedString *)attributeText emptyImage:(UIImage *)image verticalOffset:(CGFloat)offset verticalSpace:(CGFloat)space tapBlock:(SOEmptyDataSetClickBlock)tapClick;
// 显示空状态
- (void)so_showEmptyData;
// 隐藏空状态
- (void)so_hideEmptyData;
```

### 2. 库 `SoulWebView`: Web视图管理库
1. 类: SoulWebTracker 继承自: NSObject
```
// 初始化Web追踪器
- (instancetype)initWithModule:(NSString *)module report:(Report)report;
// 追踪事件
- (void)track:(NSString *)type;
// 追踪带值的事件
- (void)track:(NSString *)type value:(NSString *)value;
```
2. 类: SWebEngine 继承自: NSObject
```
// 是否启用预加载
@property (nonatomic, readwrite, assign) BOOL preloadEnable;
// 获取共享实例
+ (instancetype)shared;
// 预取URL
+ (void)prefetch:(NSString *)url;
// 强制预取URL
+ (void)prefetch:(NSString *)url force:(NSNumber * __nullable)force;
```
3. 类: SoulWKWebView 继承自: WKWebView
```
// WebView代理
@property (nonatomic, weak, nullable) id <SoulWKWebViewDelegate> delegate;
// 是否检查白屏
@property (nonatomic, assign) BOOL isCheckWhiteScreen;
// 白屏检查延迟时间
@property (nonatomic, assign) float checkWhiteScreenDelayInSeconds;
// 缓存配置
@property (nonatomic, strong, nullable) SoulWebCacheConfiguration *cacheConfiguration;
// 加载URL字符串
- (void)loadURLString:(nonnull NSString *)URLString;
// 初始化WebView
- (instancetype _Nonnull )initWithFrame:(CGRect)frame configuration:(WKWebViewConfiguration *_Nullable)configuration tracker:(SoulWebTracker *_Nullable)tracker;
```

### 3. 库 `SoulToast`: 提示信息显示库
1. 分类: MBProgressHUD 继承自: MBProgressHUD
```
// 显示文本提示
+ (void)showText:(NSString *)showText toView:(UIView * _Nullable)view;
// 显示错误提示
+ (void)showError:(NSString *)error toView:(UIView * _Nullable)view;
// 显示成功提示
+ (void)showSuccess:(NSString *)success toView:(UIView * _Nullable)view;
// 显示消息提示
+ (MBProgressHUD *)showMessage:(NSString *)message toView:(UIView * _Nullable)view;
// 显示带持续时间的消息提示
+ (MBProgressHUD *)showMessage:(NSString *)message toView:(UIView * _Nullable)view duration:(NSTimeInterval)duration;
// 检查视图上是否有HUD
+ (BOOL)isHudViewOn:(UIView *)view;
```

### 4. 库 `SOVideoPlayer`: 视频播放器库
1. 类: SOVideoPlayer 继承自: NSObject
```
// 播放器代理
@property (nonatomic,weak) id<SOVideoPlayerDelegate> delegate;
// 是否预解码视频
@property (nonatomic, assign) BOOL shouldPreDecodeVideo;
// 场景类型
@property (nonatomic, copy) NSString *sceneType;
// 播放状态
@property (nonatomic, assign) SOVideoPlayerStatus playStatus;
// 获取默认播放器
+ (instancetype)defaultPlayer;
// 初始化指定类型播放器
- (instancetype)initWithType:(SOVideoCorePlayerType)type;
// 设置封面图片
- (void)setCoverImage:(UIImage *)coverImage;
// 准备播放器
- (void)preparePlayerWithUrl:(NSString *)url startTime:(CMTime)startTime;
// 获取总时长
- (CMTime)totalDuration;
// 截图
- (void)snapshot:(void(^)(UIImage *))completion;
```

### 5. 库 `SoulFontKit`: 字体管理库
1. 分类: UILabel 继承自: UILabel
```
// 动态字体
@property (nonatomic, strong) UIFont *soFont;
// 字体扩展信息
@property (nonatomic, strong) SOFontExt *soFontExt;
```
2. 类: SOFontManager 继承自: NSObject
```
// 字体管理器代理
@property (nonatomic, weak) id<SOFontManagerDelegate> delegate;
// 字体配置
@property (nonatomic, strong) SOFontConfig *config;
// 获取共享管理器
+ (instancetype)sharedManager;
// 更新字体大小类型
- (void)updateFontSizeType:(SOFontSizeType)sizeType;
```

### 6. 库 `SoulProgressView`: 进度视图库
1. 类: SoulCircleProgressView 继承自: UIView
```
// 进度颜色
@property (nonatomic, strong) UIColor *progressColor;
// 进度背景颜色
@property (nonatomic, strong) UIColor *progressBackgroundColor;
// 进度线宽
@property (nonatomic, assign) CGFloat progressWidth;
// 进度百分比
@property (nonatomic, assign) float percent;
// 是否顺时针
@property (nonatomic, assign) BOOL clockwise;
// 中心标签
@property (nonatomic, strong) UILabel *centerLabel;
// 中心标签点击回调
- (void)theCallbackOfClickCenterLabel;
```

### 7. 库 `SOAlertView`: 弹窗组件库
1. 类: SOAlertAction 继承自: NSObject
```
// 标题
@property (nonatomic, copy) NSString *title;
// 普通状态图片
@property (nonatomic, copy) UIImage *imageNormal;
// 选中状态图片
@property (nonatomic, copy) UIImage *imageSelect;
// 富文本标题
@property (nonatomic, strong) NSAttributedString *attribteTitleString;
// 动作回调
@property (nonatomic, copy) SOAlertActionBlock actionBlock;
// 创建带标题和动作的实例
+ (instancetype)actionWithTitle:(NSString *)title action:(nullable SOAlertActionBlock)action;
// 创建带图片状态的动作
+ (instancetype)actionWithTitle:(NSString *)title imageNormal:(UIImage *)imageNormal imageSelect:(UIImage *)imageSelect action:(nullable SOAlertStateChangeActionBlock)action;
```

### 8. 库 `SoulLoading`: 加载视图库
1. 类: SOLoadingView 继承自: UIView
```
// 加载视图样式
@property (nonatomic, assign) SOLoadingItemViewStyle viewStyle;
// 加载信息
@property (nonatomic, strong) NSString *loadingInfo;
// 开始加载
- (void)startLoading;
// 停止加载
- (void)stopLoading;
```
2. 分类: UIView 继承自: UIView
```
// 加载视图
@property (nonatomic, strong) SOLoadingView *soLoadingView;
// 线性加载视图
@property (nonatomic, strong) SOLoadingLine *soLineLoadingView;
// 开始加载
- (SOLoadingView *)soStartLoading;
// 带框架的开始加载
- (SOLoadingView *)soStartLoadingWithFrame:(CGRect)frame;
// 带信息的开始加载
- (SOLoadingView *)soStartLoadingWithInfo:(NSString *)loadingInfo;
// 停止加载
- (void)soStopLoading;
// 设置线性加载进度
- (SOLoadingLine *)soSetLineLoadingProgress:(float)lineLoadingProgress;
// 隐藏加载线
- (SOLoadingLine *)soHideLoadingLine;
```

### 9. 库 `SoulHeadPortrait`: 头像组件库
1. 分类: SOHeadPortraitView 继承自: UIView
```
// 是否显示CP标记
@property (nonatomic, assign) BOOL isCPMarkShow;
// CP标记URL
@property (nonatomic, strong) NSString * _Nullable cpMarkUrlStr;
// 设置头像背景
- (void)setHeadBgWithName:(NSString *)name size:(NSInteger)size;
// 设置带占位图的头像背景
- (void)setHeadBgWithName:(NSString *)name size:(NSInteger)size placeholderImage:(UIImage * _Nullable)placeholderImage;
// 设置头像
- (void)setHeadWithName:(NSString *)name size:(NSInteger)size;
// 设置带占位图的头像
- (void)setHeadWithName:(NSString *)name size:(NSInteger)size placeholderImage:(UIImage * _Nullable)placeholderImage;
// 设置头像框架
- (void)setHeadFrameWithName:(NSString *)name size:(NSInteger)size type:(SoulMediaAddressImageType)type;
// 设置带占位图的头像框架
- (void)setHeadFrameWithName:(NSString *)name size:(NSInteger)size type:(SoulMediaAddressImageType)type placeholderImage:(UIImage * _Nullable)placeholderImage;
// 获取头像框架URL字符串
- (NSString *)headFrameUrlStrWithName:(NSString *)name size:(NSInteger)size type:(SoulMediaAddressImageType)type;
```

### 10. 库 `SoulAudioPlayer`: 音频播放器库
1. 类: SoulAudioPlayer 继承自: NSObject
```
// 播放器类型
@property (nonatomic,assign) SoulAudioPlayerType playerType;
// 场景类型
@property (nonatomic, copy) NSString *sceneType;
// 初始化指定类型播放器
- (instancetype)initWithType:(SoulAudioPlayerType)type;
```
2. 类: SoulAudioPlayerManager 继承自: NSObject
```
// 获取共享实例
+ (SoulAudioPlayerManager *)sharedInstance;
```

### 11. 库 `SoSwiftLottieBridge`: Lottie动画桥接库
1. 类: SOAnimationView 继承自: UIView
```
// 兼容动画对象
@property (nonatomic, strong) SOCompatibleAnimation * _Nullable soCompatibleAnimation;
// 循环动画次数
@property (nonatomic) CGFloat loopAnimationCount;
// 内容模式
@property (nonatomic) UIViewContentMode contentMode;
// 当前进度
@property (nonatomic) CGFloat currentProgress;
// 动画速度
@property (nonatomic) CGFloat animationSpeed;
// 是否正在播放动画
@property (nonatomic, readonly) BOOL isAnimationPlaying;
// 播放动画
- (void)play;
// 带完成回调的播放
- (void)playWithCompletion:(void (^ _Nullable)(BOOL))completion;
// 停止动画
- (void)stop;
// 暂停动画
- (void)pause;
```

### 12. 库 `SoulEmoji`: 表情符号库
1. 类: SOEmojiModel 继承自: NSObject
```
// 表情ID
@property (nonatomic, assign) NSInteger emojiId;
// 表情名称
@property (nonatomic, copy) NSString* emojiName;
// 表情图片URL
@property (nonatomic, copy) NSString* emojiImgUrl;
// 表情资源URL
@property (nonatomic, copy) NSString* emojiResourceUrl;
// 表情类型
@property (nonatomic, assign) NSInteger type;
// 获取Soul表情正则表达式
- (NSString*)getSoulEmojiRegular;
```
2. 类: SOChatInputTextView 继承自: UITextView
```
// 输入视图代理
@property (nonatomic,weak) id<SOChatInputViewDelegate>sodelegate;
// 占位符文本
@property (nonatomic, copy) NSString *placeHolder;
// 黑色占位符文本
@property (nonatomic, copy) NSString *placeHolderBlack;
// 是否显示礼物
@property (nonatomic) BOOL shouldShowGift;
// 是否匿名
@property (nonatomic) BOOL isAnonymous;
// 自定义文本容器内边距
@property (nonatomic, assign) UIEdgeInsets customTextContainerInset;
// 触摸开始事件
- (void)SOTextViewtouchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event;
// 触摸移动事件
- (void)SOTextViewtouchesMoved:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event;
// 触摸取消事件
- (void)SOTextViewtouchesCancelled:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event;
// 触摸结束事件
- (void)SOTextViewtouchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event;
// 礼物按钮点击回调
- (void)inputTextView:(SOChatInputTextView*)inputView didGiftBtnClick:(UIButton*)sender;
```

### 13. 库 `SOWebImage`: 图片加载库
1. 分类: UIImage 继承自: UIImage
```
// 加载SVG图片
+(UIImage *)SVGImageNamed:(NSString *)name;
// 从指定Bundle加载SVG图片
+(UIImage *)SVGImageNamed:(NSString *)name inBundle:(NSBundle * _Nullable)bundle;
// 加载指定尺寸的SVG图片
+(UIImage *)SVGImageNamed:(NSString *)name size:(CGSize)size;
// 从指定Bundle加载指定尺寸的SVG图片
+(UIImage *)SVGImageNamed:(NSString *)name size:(CGSize)size inBundle:(NSBundle * _Nullable)bundle;
// 加载带色调的SVG图片
+(UIImage *)SVGImageNamed:(NSString *)name tintColor:(UIColor *)tintColor;
// 从指定Bundle加载带色调的SVG图片
+(UIImage *)SVGImageNamed:(NSString *)name tintColor:(UIColor *)tintColor inBundle:(NSBundle *)bundle;
// 加载指定尺寸和色调的SVG图片
+(UIImage *)SVGImageNamed:(NSString *)name size:(CGSize)size tintColor:(UIColor *)tintColor;
// 从指定Bundle加载指定尺寸和色调的SVG图片
+(UIImage *)SVGImageNamed:(NSString *)name size:(CGSize)size tintColor:(UIColor *)tintColor inBundle:(NSBundle * _Nullable)bundle;
```

### 14. 库 `SoulNetErrorUI`: 网络错误UI库
1. 类: SONetworkError 继承自: UIView
```
// 是否夜间模式
@property (nonatomic) BOOL isNightMode;
```
2. 类: SoulNewNetworkError 继承自: UIView
```
// 是否夜间模式
@property (nonatomic) BOOL isNightMode;
```
3. 类: SONetworkErrorPromptView 继承自: UIView
```
// 初始化带Y坐标的实例
-(instancetype)initWithY:(CGFloat)y;
// 更新提示标签
-(void)updateNoticeLabel:(NSString *)noticeLabel;
// 显示网络错误提示视图
-(void)showNetworkErrorPromptView;
// 隐藏网络错误提示视图
-(void)hiddenNetworkErrorPromptView;
```

### 15. 库 `SoulInputBoard`: 输入面板库
1. 类: SORichInputBoard 继承自: SOComponentBaseView
```
// 输入面板代理
@property (nonatomic, weak) id<SORichInputBoardDelegate> rbDelegate;
// 动画同步参考视图
@property (nonatomic, weak) UIView *animateSyncRefView;
// 透明背景视图
@property (nonatomic, strong) UIView *transBgView;
// 主背景视图
@property (nonatomic, strong) UIView *mainBgView;
// 输入栏
@property (nonatomic, strong) SOInputBar *inputBar;
// 操作栏
@property (nonatomic, strong) SOOperationBar *operationBar;
// 面板视图数组
@property (nonatomic, strong) NSArray<UIView *> *boardViews;
// 底部视图
@property (nonatomic, strong) UIView *bottomView;
// 底部间距
@property (nonatomic, assign) CGFloat bottomSpace;
// 是否显示操作栏
@property (nonatomic, assign) BOOL showOpBar;
// 是否仅在展开时显示操作栏
@property (nonatomic, assign) BOOL showOpBarOnlyIfExpended;
// 面板是否展开
@property (nonatomic, assign, readonly) BOOL isBoardExpanded;
// 设置视图
- (void)setupViews;
// 设置约束
- (void)setupConstraints;
// 取消选择所有按钮
- (void)unselectedAllButtons;
// 获取指定索引的操作按钮
- (UIButton *)opButtonAtIndex:(NSInteger)index;
// 显示系统键盘
- (void)showSystemKeyBoard;
// 显示指定索引的面板
- (BOOL)showBoardAtIndex:(NSInteger)index;
```

这个基础UI库提供了完整的UI组件解决方案，包括空状态管理、Web视图、提示信息、视频播放、字体管理、进度显示、弹窗、加载状态、头像显示、音频播放、动画效果、表情符号、图片加载、网络错误UI和输入面板等功能模块。
