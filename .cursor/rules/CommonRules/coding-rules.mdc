---
description: 
globs: 
alwaysApply: false
---
你是一位专业 iOS 开发人员。遵循以下代码规则：

1、写在前面的思考
1.1 一些思路：摘自阿里云iOS编程规范
- 需求是暂时的，只有变化才是永恒的，面向变化编程，而不是面向需求编程。
- 不要因过分追求技巧，而降低程序的可读性。
- 简洁的代码可以让bug无处藏身。要写出明显没有bug的代码，而不是没有明显bug的代码。
- 先把眼前的问题解决掉，解决好，再考虑将来的扩展问题。
1.2 关于不同语言的代码规范
结合不同语言的语法特性，其代码规范间可能确实存在差异。但基础的「规范范畴」和「规范思路」其实都是相似的。本篇以Object-C为基础进行规范的举例和思路说明，不再针对其他语言（Swift，SwiftUI）进行详细的特性规范展开。当我们将基础的规范思路都“融入基因”之后，即便一些我们没有约定的规范场景，你会发现大家的写法也会是相似的。
1.3 关于打破规范
规则有它的作用范围：
- 一方面我们认知到：有一些极端的场景，我们确需要打破规范
- 另一方面我们也需要知道：需要打破规范的场景非常之少，避免将「规范可以被打破」作为借口，频繁地为了「一时的便利」和「懒于思考」而打破规范。
1.4 标记
该篇存在大量的示例代码，为方便区分「正例代码」和「反例代码」，我们使用一些明显的颜色符号进行标记：
- 正例代码：✅ 正例说明
- 反例代码：❌ 反例说明
2、规范
2.1 命名规范
2.1.1 通用性要求
- 保持含义清晰，尽量做到不需要注释也能了解其作用；若做不到，就加注释；尽量使用全称而不是缩写。
- 避免使用容易和系统冲突的关键字进行命名，如：new，id等，可考虑调整: new => create, id => xxxId
- 一些概念明确的类命名，加上对应的概念后缀
  1. 服务接口数据结构定义 XXXModel  XXXResp
  2. 接口请求包装类 XXXRequest
  3. Router跳转 XXXRouter
  4. protocol定义类 XXX protocol
  5. ViewModel命名 XXXViewModel
2.1.2 大驼峰命名
- 符合 类型 概念的命名一般使用大驼峰命名，比如：类类型，结构体类型，枚举类型，类型别名
- 符合 公开常量 概念的命名一般使用大驼峰命名，比如：枚举值，头文件中的宏定义，头文件中的类型常量
- 工程目录 统一使用大驼峰命名
A. 「类名」使用大驼峰命名
✅ 类定义使用大驼峰
@interface SOMainSquareViewController
@end
B. 「枚举」使用大驼峰命名
✅ 枚举名使用大驼峰命名
typedef NS_ENUM(NSUInteger, SOMetaUserInfoSlideDirection) {
    SOMetaUserInfoSlideDirectionNone,
    SOMetaUserInfoSlideDirectionUp,
    SOMetaUserInfoSlideDirectionDown,
};
C. 「类型别名」使用大驼峰命名
✅ block类型别名举例
typedef void(^SOSquareEvaluateScoreSubmitBlock)(BOOL success);
D. 「枚举值」使用大驼峰命名
✅ 枚举值使用大驼峰命名
typedef NS_ENUM(NSUInteger, SOMetaUserInfoSlideDirection) {
    SOMetaUserInfoSlideDirectionNone,
    SOMetaUserInfoSlideDirectionUp,
    SOMetaUserInfoSlideDirectionDown,
};
E. 「公开常量」 使用大驼峰命名
// SoulRouterDefinition.h
✅ 头文件中的定义一般认为「提供给外部使用」（公开常量概念）
✅ 其常量定义建议大驼峰
#define SORouterHttpScheme  @"http"
#define SORouterHttpsScheme @"https"
#define SORouterInnerScheme @"soul"

// SoulEventClient+Deprecated.h
✅ 使用 extern 定义常量也一样，只要符合「提供给外部使用」（公开常量概念）的定义，
✅ 建议使用大驼峰
extern NSString *const SoulEventTypeCommon;
extern NSString *const SoulEventTypeClick;
extern NSString *const SoulEventTypeExposure;
extern NSString *const SoulEventTypeIndicators;
extern NSString *const SoulEventTypeTypePV;
// SoulEventClient+Deprecated.m
NSString * const SoulEventTypeCommon = @"com";
NSString * const SoulEventTypeClick = @"clk";
NSString * const SoulEventTypeExposure = @"exp";
NSString * const SoulEventTypeIndicators = @"pef";
NSString * const SoulEventTypeTypePV = @"pv";
F. 「文件夹」使用大驼峰命名
✅ 工程目录中的文件夹名称使用大驼峰
- SOAppService
--- AppSculptorHandler
----- AppSculptorHandler.h
----- AppSculptorHandler.m
--- AppShell
----- AppShell.h
----- AppShell.m
2.1.3 小驼峰命名
【速记说明】
- 符合 变量概念 的命名一般使用小驼峰命名，如：property属性名，临时变量
- 符合 局部/临时概念 的常量 一般使用小驼峰命名，如：临时变量，.m文件中定义的局部常量
- 方法名 一般使用小驼峰命名，如：类方法，实例方法，C方法等
A. 「property属性名」使用小驼峰命名
✅ property属性名举例，使用小驼峰
@property (nonatomic, strong) AppUrlSoulRouterBridge *sourRouterBridge;
@property (nonatomic, strong) AppJumpRedirectService *jumpRedirectService;
@property (nonatomic, strong) AppShortLinkService *shortLinkService;
B. 「临时变量」使用小驼峰命名
✅ 临时变量使用小驼峰命名
✅ 符合「临时」「变量」概念的命名常常都使用小驼峰
+ (NSString *)__cacheStrFromType:(SDImageCacheType)cacheType {
    NSDictionary *tmpMap = @{
        @(SDImageCacheTypeNone): @"none",
        @(SDImageCacheTypeDisk): @"disk",
        @(SDImageCacheTypeMemory): @"memory",
    };
    NSString *tmpStr = tmpMap[@(cacheType)] ?: @"";
    return tmpStr;
}
C. 「局部常量」使用小驼峰命名
✅ 局部宏常量，使用小驼峰命名，
✅ 因概念局部，也常常在前面添加__标记
#define __timerBaseInterval         (1)     /* 定时器基础间隔 */
#define __commonUpdateInterval      (5)     /* 公共信息更新周期 */
#define __pingSendInterval          (5)     /* ping发送间隔 */
#define __pangCheckInterval         (5)     /* pang消息检查间隔 */
#define __eventPatchHandleInterval  (10)    /* 事件补丁处理(失败埋点的判定&处理)周期 */

#define __dbName            @"SoulRTEvent.db"       /* 数据库名称 */
#define __tableName         @"RTEventTable"         /* 表名 */
D. 「方法名」使用小驼峰命名
✅ 针对C方法，使用小驼峰命名
static void __rangersCrashCallback(char **dynamiKey,
                                   char **dynamiData,
                                   uint64_t crashTime,
                                   uint64_t faultAddress,
                                   thread_t currentThread,
                                   thread_t crashThread) {
    ...                                   
}

✅ 针对OC方法，每一段都使用小驼峰命名
- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    ...
}
2.1.4 【0722Review 废弃】私有概念命名
2.1.5 衔接下划线
A. 资源命名使用衔接下划线明确名字空间
- 资源命名使用全小写字母，通过下划线(_)进行概念组分隔
- 资源命名应有效指明资源所属的模块
❌  反例：close，资源起名过短，且关键字的查找命中范围极大，使得：
- 该类资源的添加原因/应用范畴极不明确
- 资源很容易因重名而造成展示异常
- 进行资源清理时，对于资源是否在用的判定成本极高，甚至无法判定
[图片]
[图片]
✅  正例：应用_模块_类型_名称_备注(如日夜间)
✅  应用_模块_类型_名称_备注(如日夜间)
soul_searchkit_icon_clear
soul_searchkit_icon_clear_night
soul_searchkit_icon_search
soul_searchkit_icon_search_night
B. 做概念分隔用的衔接下划线
在应用中，有时候我们为了明确的概念分隔，也会使用衔接下划线
✅ 进行枚举定义时，有时为了弱化前缀阅读，让差异更清晰，可使用衔接下划线
✅ 衔接线后的驼峰格式可保持与衔接线左侧的一致
/* 分享渠道 */
typedef NS_ENUM(NSInteger, SOShareWay) {
    SOShareWay_Weixin = 0,  // 微信
    SOShareWay_QQ,          // QQ
    SOShareWay_Sina,        // 新浪
    SOShareWay_Soul,        // 站内分享
    SOShareWay_WechatQuan,  // 微信朋友圈
    ...
};

✅ 提供系统类扩展方法时，有些三方库会使用模块名衔接下划线，以指明对应方法非系统方法，比如YYModel
@interface NSObject (YYModel)
+ (nullable instancetype)yy_modelWithJSON:(id)json;
+ (nullable instancetype)yy_modelWithDictionary:(NSDictionary *)dictionary;
- (BOOL)yy_modelSetWithJSON:(id)json;
...
@end
2.1.6 场景-回调命名
- 回调命名都建议保留回调的发起者的实例：比如tableView的代理会传入一个tableView实例，事件响应会传入一个sender，通知会传入一个noti。
- 针对事件/通知回调，建议命名以on开头
A 代理回调命名
- 代理回调方法起名时，建议附带上发起代理的对象实例：代理方法在操作时，有可能需要访问代理实例的一些状态信息，将实例代理出去，提高代理接口的扩展性，一定程度上避免接口的频繁修改。
- 尽量标注 required 和 optional：但如果你对于代理方法是否必须根本没有思考的话，不标注则更好（默认时optional的）
✅ 系统代理举例：会将tableView实例代理出来
@protocol UITableViewDataSource    
@required   
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section;   
@optional  
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView;
@optional 

✅ Soul的一些举例：
@class SORemoteResManager;
@protocol SORemoteResManagerDelegate <NSObject>
@optional
- (void)remoteResManager:(SORemoteResManager *)manager postError:(NSError *)error;
- (void)remoteResManager:(SORemoteResManager *)manager didAddNewConfPack:(SORemotePackage *)package;
- (void)remoteResManager:(SORemoteResManager *)manager markPackageValid:(SORemotePackage *)package;
- (void)remoteResManager:(SORemoteResManager *)manager markResValid:(SORemoteResItem *)resItem;
- (void)remoteResManager:(SORemoteResManager *)manager removedPackage:(SORemotePackage *)package allRemoved:(BOOL)allRemoved;
- (void)remoteResManager:(SORemoteResManager *)manager removedRes:(SORemoteResItem *)resItem;
- (void)remoteResManagerDidCleanedExpiredRes:(SORemoteResManager *)manager;
@end
B 事件回调命名
✅ 事件回调场景举例
[shareButton addTarget:self action:@selector(onShareButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
✅ 事件回调的命名建议以on开头
✅ 事件回到方法建议保留sender参数
- (void)onShareButtonClicked:(UIButton *)sender {
    ...
}
C 通知回调命名
[[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onLoginSuccess:) name:@"kRootLoginSuccess" object:nil];
✅ 通知回调的命名建议以on开头
✅ 通知回到方法建议保留noti参数
- (void)onLoginSuccess:(NSNotification *)noti {
    ...
}
2.1.7 场景-单例/共享实例命名
- 单例命名以shared开头，如：shared, sharedInstance, sharedConfig
- 共享实例（提供共享实例，但不限制创建多个实例），以default开头，如：default, defaultInstance, defaultConfig
- 单例/共享实例的返回值统一使用instancetype
A. 单例命名以shared开头
❌ 单例方法命名以shared开头
❌ 单例方法的返回类型使用instancetype
@implementation XXXConfig {
+ (XXXConfig)config;
@end

==> 调整为

✅ 单例方法命名以shared开头，可直接使用 shared，也可以使用sharedXXX，比如该场景也可以是sharedConfig
✅ 单例方法的返回类型使用instancetype
@implementation XXXConfig {
+ (instancetype)shared;
@end
B. 共享实例以default开头
✅ 以MMKV举例，我们常常使用 [MMKV defaultMMKV]，但时机上我们也可以构建自己的MMKV对象，即其提供的是一个共享实例方法
✅ 共享实例方法的命名以default开头
✅ 共享实例方法的返回类型使用instancetype
@interface MMKV : NSObject
+ (nullable instancetype)defaultMMKV;
@end
2.1.8 场景-初始化命名
- 初始化方法一般以 initWith 开头
- 初始化方法的返回值建议写 instancetype（而不是写死类名）
❌ 初始化方法的返回值不建议直接写死类名（SOViewController）
@implementation SOViewController
- (SOViewController)initWithCoder:(NSCoder *)coder {
    ...
}
@end

==> 调整为

✅ 初始化方法的返回值统一使用instancetype
✅ 初始化方法一般建议以initWith开头
@implementation SOViewController
- (instancetype)initWithCoder:(NSCoder *)coder {
    ...
}
@end
2.1.9 其他场景
A. 避免类名与模块名完全相同
主要为了规避「苹果模块名/类型相同时，Swift的版本差异兼容性bug」
❌ 模块名：SOBasePopView
❌ 含模块同名类，存在不同Swift版本的编译兼容问题（苹果版本兼容边缘bug）
@interface SOBasePopView : UIView
@end

==> 调整为

✅ 模块名：SOBasePopView ==> SoulBasePopView
✅ 保障模块名和类名不一致即可（不过实际操作中这类调整的成本其实非常高，常常使用源码的方式规避问题）
@interface SOBasePopView : UIView
@end
B. 系统扩展方法命名
为系统类扩展方法时，扩展方法应该添加明显的前缀，以与系统方法区分开
✅ 远程化模块扩展系统类UIButton的方法，扩展方法统一添加so前缀
@interface UIButton (SoRemoteRes)
- (void)soSetImageWithResKey:(NSString *)resKey forState:(UIControlState)state;
- (void)soSetImageWithResKey:(NSString *)resKey forState:(UIControlState)state placeholderImage:(nullable UIImage *)placeholderImage;
- (void)soSetBackgroundImageWithResKey:(NSString *)resKey forState:(UIControlState)state;
- (void)soSetBackgroundImageWithResKey:(NSString *)resKey forState:(UIControlState)state placeholderImage:(nullable UIImage *)placeholderImage;
- (void)soCancelRemoteResFirstValidResponse;
@end
附：RN命名规范
https://github.com/airbnb/javascript
附：Flutter命名规范
Flutter开发规范
2.2 注释规范
优秀的代码大部分是可以自描述的，我们完全可以用代码本身来表达它到底在干什么，而不需要注释的辅助。但并不是说一定不能写注释，有以下三种情况比较适合写注释：
- 公共接口(注释要告诉阅读代码的人，当前类能实现什么功能)。
- 涉及到比较深层专业知识的代码(注释要体现出实现原理和思想)。
- 容易产生歧义的代码(但是严格来说，容易让人产生歧义的代码是不允许存在的)。
除了上述这三种情况，如果别人只能依靠注释才能读懂你的代码的时候，就要反思代码出现了什么问题。
最后，对于注释的内容，相对于“做了什么”，更应该说明“为什么这么做”。
2.2.1 通用性要求
- 使用中文注释：属于基于实际情况的通融性约定，国内大部分技术团队的英文能力并不算优秀，使用中文注释能有效降低交流歧义，降低代码阅读成本。
2.2.2 使用注释快捷键
Xcode的注释快捷键，可以帮助我们快速生成注释，我们一般建议和系统行为尽量保持一致：
- 行注释： //
- 方法注释：///
✅ 1. 选中行(光标所在行) + Command + / : 双斜杠方式单行加注释/去注释
// 被注释区域
//- (void)remoteResManager:(SORemoteResManager *)manager postError:(NSError *)error;

✅ 2. 选中部分目标 + Command + / : 选中区域注释/去注释
非选中区域/* 选中区域 */
- (void)remoteResManager:(SORemoteResManager *)manager /*postError:(NSError *)error*/;

✅ 3. 方法顶部 + Option + Command + / : 添加方法注释
/// <#Description#>
/// @param manager <#manager description#>
/// @param error <#error description#>
- (void)remoteResManager:(SORemoteResManager *)manager postError:(NSError *)error;

✅ 4. 属性顶部 + Option + Command + / : 添加属性注释
/// <#Description#>
@property (nonatomic, weak) id<SORemoteResManagerDelegate> delegate;
2.2.3 属性注释
A. 尾部//注释
针对属性的注释形式比较一致，内容比较简单时，可以考虑使用尾部//注释：
- 单屏可以阅读到更多的信息
- 属性结构更清晰
✅ 埋点库公共参数注释示意
@interface SoulEventCommon : NSObject<NSCoding>

#pragma mark - 文档定义

@property (nonatomic, strong) NSString *sdkv;   // SDK 版本，如：1.0.0
@property (nonatomic, strong) NSString *uid;    // 用户上传时的账号id，如：c2VXYTZjYUJFNkxWVlgvZUh1NEExdz09
@property (nonatomic, strong) NSString *apv;    // APP的版本号，如: 4.90.0
@property (nonatomic, strong) NSString *apc;    // APP的版本code值，如：2875
@property (nonatomic, strong) NSString *did;    // iOS传IDFA
@property (nonatomic, strong) NSString *os;     // 操作系统，如：iOS
@property (nonatomic, strong) NSString *osv;    // 操作系统版本号，如：16.0
@property (nonatomic, strong) NSString *aid;    // app_id，如：1000007
@property (nonatomic, strong) NSString *rtm;    // 客户端上报的十三位毫秒时间戳，如：1557997347383
@property (nonatomic, strong) NSString *dvb;    // 客户端品牌，如：iPhone
@property (nonatomic, strong) NSString *dvt;    // 客户端型号，如：iPhone SE
@property (nonatomic, strong) NSString *scr;    // 屏幕分辨率，宽*高（像素）,如：1080*1920
@property (nonatomic, strong) NSString *chf;    // APP下载的渠道来源，如：applestore
@property (nonatomic, strong) NSString *bat;    // 电量百分比，如：75
@property (nonatomic, strong) NSString *ntyp;   // 网络类型，如：WIFI
@property (nonatomic, strong) NSString *ip;     // ip地址，如：**************
@property (nonatomic, strong) NSString *ua;     // userAgent

#pragma mark - iOS 单独补充

@property (nonatomic, strong) NSString *sdid;   // 设备ID
@property (nonatomic, strong) NSString *idfa;   // IDFA

@end
B. 顶部 /// 注释
当属性的说明较为复杂时，建议使用顶部注释，比如：
- 某个模块的对外配置属性，常常期望进行一些场景说明和举例
- 一些为了兼容问题而添加的补丁属性，期望讲清楚问题场景和补丁逻辑
✅ 苹果注释举例
@interface UIButton : UIControl <NSCoding>
...
/// Enables this button's built-in pointer interaction.
@property (nonatomic, readwrite, assign, getter = isPointerInteractionEnabled) BOOL pointerInteractionEnabled API_AVAILABLE(ios(13.4)) API_UNAVAILABLE(watchos, tvos);

/// An optional menu for the button to display. The button will automatically enable or disable its contextMenuInteraction when a non-nil or nil menu is set. Defaults to nil.
@property (nonatomic, readwrite, copy, nullable) UIMenu *menu API_AVAILABLE(ios(14.0), tvos(17.0)) API_UNAVAILABLE(watchos);
...
@end
2.2.4 方法注释
一个函数(方法)必须有一个字符串文档来解释，除非它：
- 非公开，私有函数。
- 很短。
- 显而易见。
而其余的，包括公开接口，重要的方法，分类，以及协议，都应该伴随文档(注释)：
- 以/开始
- 第二行是总结性的语句
- 第三行永远是空行
- 在与第二行开头对齐的位置写剩下的注释。

1. 针对头文件的方法，都需要有注释：
  1. 它让我们对模块接口的设置进行强行思考，如果一个方法完成的任务过多，它将会是难以精确总结的；
  2. 作为公开的方法，是可能被其他伙伴使用的，我们要尽量保证使用者仅从方法的定义和注释说明就可以了解方法的作用（而不是一定要阅读方法实现源码）。
2. 针对复杂的私有方法，也需要注释：没有人能保证一个私有方法自己永远熟悉且维护，给他人（也可能是未来的自己）一些快速学习的可能性。
3. 注释要准确：注释就有误导性，它就没有意义了。
4. 使用快捷键添加：建议使用 Option + Command + / 快捷添加注释模板，然后填充模板
✅ 头文件的方法，期望都需要注释，举例：
✅ 远程化模块的头文件对外方法注释
@interface SORemoteResManager : NSObject

/// 添加一个远程资源包下载配置
/// @param plistName plist文件名（认为后缀一定是plist）
/// @param bundle 文件所在bundle，nil表示为mainBundle
/// @param isDownload 配置完是否立刻下载
- (BOOL)addResConfigWithPlistName:(NSString *)plistName
                           bundle:(NSBundle *_Nullable)bundle
                     withDownload:(BOOL)isDownload;

/// 添加一个远程资源包下载配置
/// @param plistName plist文件名（认为后缀一定是plist）
/// @param bundle 文件所在bundle，nil表示为mainBundle
/// @param isDownload 配置完是否立刻下载
/// @param coverAdd 是否进行覆盖配置（若对应的包名已存在，YES: 覆盖配置; NO:不进行配置）
- (BOOL)addResConfigWithPlistName:(NSString *)plistName
                           bundle:(NSBundle *_Nullable)bundle
                     withDownload:(BOOL)isDownload
                         coverAdd:(BOOL)coverAdd;

/// 添加一个远程资源包下载配置
/// @param plistPath 配置的plist文件路径
/// @param isDownload 配置完是否立刻下载
/// @param coverAdd 是否进行覆盖配置（若对应的包名已存在，YES: 覆盖配置; NO:不进行配置）
- (BOOL)addResConfigWithPlist:(NSString *)plistPath
                 withDownload:(BOOL)isDownload
                     coverAdd:(BOOL)coverAdd;
                     
@end

✅ 比较关键/复杂的私有方法也建议注释
✅ 下载库私有方法注释举例
@implementation SODownloaderManager
/// 将下载Item加入容器（含重复下载项处理）
/// - Parameter downloadItem: 新增的下载任务
/// - 返回值：是否新加入下载管理字典
- (BOOL)p_addDownloadToContainer:(SODownloadModel *)downloadItem {
    ...
}
@end
2.2.5 逻辑注释
一些逻辑如果「是补丁逻辑」（非正常逻辑），「专业性较强」，或「是复杂逻辑」，则应该补充注释说明
✅ 某个特定场景的边缘场景兼容往往是难以快速理解的，此时可以给出必要的注释说明，让阅读者：
✅ 1) 了解它做的是这件事情，判断是否需要细致关注
✅ 2) 关注逻辑细节时，可以被快速有效地引导
- (NSError *)checkFile:(SORemoteResItem *)resItem {
    NSError *error = nil;
    uint64_t fileSize = [self fileSizeAtPath:resItem.resSavedPath];
    /* 逻辑说明：
     * 1 文件大小为0，认为一定是错误的
     * 2 媒资资源大小与预期大小不一致，说明资源出错
     * 2.1 只针对媒资配置的资源才会进行资源大小比对
     * 2.2 若「expectedResSize」为0，表示资源预期大小未正确配置，忽略
     * 2.3 资源大小与预期大小不一致，说明资源出错
     */
    if (0 == fileSize || (SORemoteResItemConfTypeSoulResource == resItem.confType && resItem.expectedResSize != 0 && fileSize != resItem.expectedResSize)) {
        NSString *fileSizeErrStr =
            [NSString stringWithFormat:@"已下载文件异常(文件存在，大小为 %llu B，期望为 %llu B), 资源值:%@, 资源URL:%@, 本地文件路径:%@",
                                       fileSize,
                                       (uint64_t)resItem.expectedResSize,
                                       resItem.confValString,
                                       resItem.resUrlStr,
                                       resItem.resSavedPath];

        error = [self errorWithStr:fileSizeErrStr];
    }
    return error;
}
2.2.6 TODO
- 常用于开发过程中：标记一些未完成的或完成的不尽如人意的地方
- 如果开发完成还存在TODO，需要二次Review下其合理性
✅ 用TODO来给自己做标记
- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions { 
    // TODO:增加初始化 
    return YES; 
}  
2.2.7 mark概念分组
- 当一个文件存在的属性/方法数量过多时，建议使用 #pragma mark 进行概念分组，以提高引导可读性
- 它的难点在于对属性/方法概念的整合和总结
✅ 用#pragma mark 进行概念分组
@implementation SODownloaderManager
#pragma mark Getter & Setter - Local
...
#pragma mark - Notification（Net state change）
...
#pragma mark - Flow - 下载添加 & 启动
...
#pragma mark - Flow - 下载进度处理
...
@end
注：比较理想的状态下，我们单文件中的 #pragma mark 一般是可以约定成几个比较固定的「大概念」的，且它主要适用于「单层次的分组」。如果一个文件包含的内容过多（比如私聊文件10000+行代码），用再多 #pragma mark 也难以有效提高代码的可读性了，它需要架构优化和文件拆解了。（当然，基于现状&风险，是否处理是另一回事）
2.3 代码格式化规范
2.3.1 空格规范
A. 逗号(,)分号(;)左边不要加空格，右边要
✅ 分号(;)左边不要加空格，右边要
for (int i = 0; i < count; i++) {}

✅ 逗号(,)左边不要加空格，右边要
static uint32_t awColorHexs[__AWTagColorNum] = {
    0xFFFFFF, 0x181A20, 0x22262F, 0x393C47, 0x81808A,
    0x757583, 0xAFADBB, 0xEDEDED, 0x8352EA, 0xFF5255,
    0x696FFF, 0xDB74FF
};
B. oc函数定义的加号(-)与括号(()间有且只有一个空格
✅ oc函数定义的加号(-)与括号(()间有且只有一个空格
+ (uint32_t)soLightColorHex:(NSInteger)sTag
- (void)soSetImageWithResKey:(NSString *)resKey forState:(UIControlState)state;
C. 比较运算符(<,>,<=,>=,==,!=)操作运算符(+,-,/,*)赋值运算符(=,+=,-=,*=,/=)两边都需要添加空格
✅ 比较运算符(<,>,<=,>=,==,!=)两边都需要添加空格
BOOL result = (a < b);
BOOL result = (a > b);
BOOL result = (a <= b);
BOOL result = (a >= b);
BOOL result = (a == b);
BOOL result = (a != b);

✅ 操作运算符(+,-,/,*)两边都需要添加空格
int result = (a + b);
int result = (a - b);
int result = (a * b);
int result = (a / b);

✅ 赋值运算符(=,+=,-=,*=,/=)两边都需要添加空格
int result = ...;
int result += 2;
int result -= 2;
int result *= 2;
int result /= 2;
D. 取反符号(!)按位取反(~)右边不要添加空格
✅ 取反符号(!)右边不要添加空格
if (!itemExist) { ... }
BOOL result = !itemExist;

✅ 按位取反(~)右边不要添加空格
int result = ~tmpNum;
E. 小括号(())开口方向不要添加空格
✅ 小括号(())开口方向不要添加空格
if (itemExist) { ... }
F. 函数定义，逻辑关键词(if,while,for,do,else if)与右边的内容间(一般是{)需要添加一个空格
✅ 函数定义与右边的内容间(一般是{)需要添加一个空格
+ (UIImage *)soImageWithResKey:(NSString *)resKey {
}

✅ 逻辑关键词(if,while,for,do,else if)与右边的内容间(一般是{)需要添加一个空格
if (completeCallback) {
    completeCallback(cachedImage);
}
G. 定义指针变量时，*与变量名链接时不留空格
✅ *与变量名直接链接，中间不留空格
UIView *tmpView = [[UIView alloc] init];

✅ const将 * 与变量名隔开了，* 和 const间有空格
static NSString * const SoulSharedUserIdKey = @"sharedUserID";
H. 字典结构中的冒号，通常左边不留空格，右边留空格
✅ 字典结构中的冒号，通常左边不留空格，右边留空格
NSDictionary *errStrMap = @{
    @(1): @"错误1",
    @(2): @"错误2",
        ...
    @(n): @"错误n",
};
I. 代码缩进使用4个空格（不使用Tab）
可在Xcode设置里面将Tab设置成4个空格（一般是默认设置）
[图片]
2.3.2 空行规范
A. 函数实现/类/Protocol/类型定义等明显的代码组间要添加1行空行
✅ Protocol与类间要添加1行空行
@class SORemoteResManager;
@protocol SORemoteResManagerDelegate <NSObject>
@end

@interface SORemoteResManager : NSObject
@end

✅ 函数实现间需要添加1行空行
- (SORemoteResConfig *)resConfig {
}

- (SORemoteResCleaner *)resCleaner {
}

- (SORemoteSafeMutableArray *)resKeysForRequest {
}
B. pragma mark后面添加1行空行
✅ pragma mark后面添加1行空行
#pragma mark - Image Parser

+ (UIImage *)smartImageFromFilePath:(NSString *)filePath
{
...
C. 代码中不应存在连续2行空行
D. 代码中每20行代码，应能找到至少1行空行
✅ 示例：域名类型枚举
✅ 说明：即便是确为同概念的的代码拟写，也应该适当补充空行，给予阅读的视觉参照。
typedef NS_ENUM(NSInteger, SoulDomainType) {
    SoulDomainTypeUnknown = -1,         // 未知类型
    
    SoulDomainTypePost,                 // UGC旧
    SoulDomainTypeNewPost,              // UGC新
    SoulDomainTypeChat,                 // CHAT
    SoulDomainTypeUser,                 // 用户域名
    SoulDomainTypeAccount,              // 账户域名
    SoulDomainTypePay,                  // 支付
    SoulDomainTypeGuest,                // 访客
    SoulDomainTypeWPL,                  // 狼人杀
    SoulDomainTypeADQ,                  // 广告 ADQ
    SoulDomainTypeADR,                  // 广告 ADR
    
    SoulDomainTypeADE,                  // 广告 ADE（未搜索到使用场景，可能已经废弃）
    SoulDomainTypeGroupChat,            // 群聊
    SoulDomainTypeH5,                   // H5
    SoulDomainTypePet,                  // 宠物
    SoulDomainTypeClubPatry,            // 派对
    SoulDomainTypeVideoParty,           // 视频派对
    SoulDomainTypeVideoPartyPlay,       // 视频派对匹配
    SoulDomainTypeCommercial,           // 商业化
    SoulDomainTypeSearch,               // 搜索
    SoulDomainTypeActivity,             // 活动
    
    SoulDomainTypeReport,               // 上报
    SoulDomainTypeDiscern,              // 识别
    SoulDomainTypeRouteService,         // 路由服务
    SoulDomainTypeBuzzService,          // Buzz服务（未搜索到使用场景，可能已经废弃）
    SoulDomainTypeIncreaseService,      // 增长服务
    SoulDomainTypeOssService,           // 阿里云OSS服务
    SoulDomainTypeSKA,                  // SKA（似和广告归因相关）
    SoulDomainTypeOfflinePackage,       // 线下包(未搜索到使用场景）
    
    SoulDomainTypeDeepLink,             // 深度链接
    SoulDomainTypeChatRemark,           // 聊天备注
    SoulDomainTypeSlFun,                // sl.fun短链系统
};
2.3.3 括号规范
A. 多项式运算较为复杂时，要加入必要的括号说明运算顺序，增强可读性
✅ 加入必要的括号说明运算顺序
double result = (a * ((b + c) / d - 3000)) + (20 * (60 * 60));
B. 宏定义的数学运算要用扩号包起来
✅ 为了保障宏替换后数值的运算顺序
#define __secondPerHour        (60 * 60)
2.3.4 换行规范
A. 函数定义开始的大括号({)不要另行换行
✅ 函数定义开始的大括号({)不要另行换行
- (instancetype)init {
    ...
}
B. 逻辑定义开始的大括号({)不要另行换行
✅ 逻辑定义开始的大括号({)不要另行换行
- (void)addFormData:(SoulNetworkUploadFormData *)formData {
    if (!_uploadFormDatas) {
        _uploadFormDatas = [NSMutableArray array];
    }
    [_uploadFormDatas addObject:formData];
}
C. 函数参数达到3个，参数考虑进行换行
✅ 函数参数过多时，考虑进行换行
+ (BOOL)eventNetImageLoadSuccess:(NSString *)bussinessTag
                     imageUrlStr:(NSString *)imageUrlStr
                       cacheType:(SDImageCacheType)cacheType
                   loadStartTime:(NSDate *)loadStartTime
                       extParams:(NSDictionary<NSString *, NSString *> *)extParams;
D. 函数参数超过8个，需要考虑参数之间是否有关联关系，尽量将关联参数组装为结构体
- (void)validateOrderById:(NSString *)orderId
                 prepayId:(NSString *)prepayId
                  receipt:(NSString *)receipt
                productId:(NSString *)productId
        transactionTimeMs:(NSString *)transactionTimeMs
            transactionId:(NSString *)transactionId
    originalTransactionId:(NSString *)originalTransactionId
                 quantity:(NSString *)quantity
              countryCode:(NSString *)countryCode
              countryName:(NSString *)countryName
                onSuccess:(SoulAPIResponseCommonBlock)succeedHandler
                onFailure:(SoulResponseFailureBlock)failureHandler;

//可修改为 
@interface SOTransactionInfo : NSObject
@property (nonatomic,strong) NSString *prepayId;
@property (nonatomic,strong) NSString *receipt;
@property (nonatomic,strong) NSString *productId;
@property (nonatomic,strong) NSString *quantity;
@property (nonatomic,strong) NSString *countryCode;
@property (nonatomic,strong) NSString *countryName;
@property (nonatomic,strong) NSString *transactionTimeMs;
@property (nonatomic,strong) NSString *transactionId;
@property (nonatomic,strong) NSString *originalTransactionId;
@end

- (void)validateOrderByById:(NSString *)orderId 
            transactionInfo:(SOTransactionInfo *)transactionInfo
                onSuccess:(SoulAPIResponseCommonBlock)succeedHandler
                onFailure:(SoulResponseFailureBlock)failureHandler;
E.字符串文本过长时，可以考虑人为的换行处理
❌ 文本太长了，一些没有自动折行的场景可能会屏蔽信息
✅ 也看场景，有些内容细节不重要的文本，也不一定需要人工的换行处理
NSString *tmpStr = @"短篇小说是一种文学作品，一般以万言左右为界限。虽然小说本质上篇幅区别并不十分重要，但典型的短篇小说仍发展出较长篇小说不同的特点。如：短篇小说长于描绘极具戏剧性的情节；往往有强有力的开头和结尾；人物关系通常并不复杂；时空跨度一般不大。通常，任何现代的小说家都会写短篇，这些短篇亦时常是对未来长篇创作的准备和练习；但是也有若干仅以短篇或中短篇小说传世的大师，例如：蒲松龄、鲁迅、契诃夫、博尔赫斯、巴别尔、胡安·鲁尔福等；另外还有一些小说家虽然也写长篇，但被冠以“短篇大师”之名，或者认为其短篇的艺术成就超过长篇，如莫泊桑、卡夫卡等，近代华文小说家张爱玲、白先勇亦都以短篇小说脍炙人口。短篇的优点在于对读者来说易于欣赏了解。小说长篇中篇短篇各有所长，如较巨大的编剧架构短篇自然不是适宜的创作手法，但如单以小说而不加以其他近代的宣传影音辅助，短篇中篇实则为最易让读者留下印象或反复阅读的篇幅之一。此外创作应以故事型态适切性及作者发挥为主，不拘泥于篇幅长短。";

==>

✅ 直接地字串链接，等价于将文本写在同一组双引导中
✅ 这边着重介绍一种文本换行的方法，是否使用看具体场景需求
NSString *tmpStr = 
    @"短篇小说是一种文学作品，一般以万言左右为界限。虽然小说本质上篇幅区别并不十分重要，"
    "但典型的短篇小说仍发展出较长篇小说不同的特点。如：短篇小说长于描绘极具戏剧性的情节；"
    "往往有强有力的开头和结尾；人物关系通常并不复杂；时空跨度一般不大。通常，任何现代的"
    "小说家都会写短篇，这些短篇亦时常是对未来长篇创作的准备和练习；但是也有若干仅以短篇或"
    "中短篇小说传世的大师，例如：蒲松龄、鲁迅、契诃夫、博尔赫斯、巴别尔、胡安·鲁尔福等；"
    "另外还有一些小说家虽然也写长篇，但被冠以“短篇大师”之名，或者认为其短篇的艺术成就超过"
    "长篇，如莫泊桑、卡夫卡等，近代华文小说家张爱玲、白先勇亦都以短篇小说脍炙人口。短篇的"
    "优点在于对读者来说易于欣赏了解。小说长篇中篇短篇各有所长，如较巨大的编剧架构短篇自然"
    "不是适宜的创作手法，但如单以小说而不加以其他近代的宣传影音辅助，短篇中篇实则为最易让"
    "读者留下印象或反复阅读的篇幅之一。此外创作应以故事型态适切性及作者发挥为主，不拘泥于"
    "篇幅长短。";
F. 含操作符的换行，操作符随着一起换行
❌ 我们将 || 作为外层逻辑的关键框架标记，
❌ || 在代码尾部，当内层逻辑块过长时，很容易将 || 藏起来
❌ 导致「突出细节，隐藏框架」，不利于代码的阅读理解
if ((1 == tmpType) ||
     (3 == tmpType) ||
     (4 == tmpType) ||
     (6 == tmpType)) { ... }
    
==>

✅ 将|| 放置到头部，清晰地呈现操作
if ((1 == tmpType)
    || (3 == tmpType)
    || (4 == tmpType)
    || (6 == tmpType)) { ... }
2.3.5 代码长度
A. 单行代码长度尽量限制在140个字符内
Xcode设置方法：
[图片]
B. 函数长度尽量限制在超过50行内
- 阈值标准：一屏幕大概能展示50行代码
- 难点：在于对函数承担任务的合理拆离
- 现实期望：函数超过50行，想一想。
C. 回调函数的长度限制在30行内
回调函数是一类特殊的函数，期望长度限制在30行内，内容过多时进行子函数的封装
说明：
1. 回调函数的命名往往是回调发生的时机，而非代理方会做的事情，内容过多时常常伴随着函数意义的不明确
2. 事件触发是比较关键的时机，不少热修的处理会以此作为切入点，过长/过复杂的函数让热修的风险增高（甚至无法热修）
D. 单文件长度尽量限制在1500行内
- 阈值标准：30屏代码
- 难点：在于对模块的架构设计和合理的概念拆离
- 现实期望：函数超过1500行，想一想。
2.4 结构性规范
2.4.1 多模块顺序：系统/三方/二方/Local
很多场景涉及到多模块时，我们都期望有一定的「概念聚合」和「书写顺序」:
- 参考排序思路：亲疏（熟悉程度），一般本地的代码肯定更“熟悉”，三方的是“没有血缘关系的”
- 参考排序思路：修改难易度，本地代码肯定容易调整，三方代码就需要提Issue了，系统的别想改
A. 头文件的的引入建议保持 系统/三方/二方/模块内/自己 的顺序，
// AppUrlHandler.m
✅ 头文件引入逻辑顺序举例：系统/三方/二方/模块内/自己

// 系统
#import <Foundation/Foundation.h>

// 三方
#import <BDASignalSDK/BDASignalManager.h>
#import <BDASignalSDK/BDASignalUtility.h>

// 二方
#import <SoulAppDecoupleDelegate/SoulAppDecoupleDelegate.h>
#import <SoulABStrategy/SoulABStrategy.h>
#import <SoulADManager/SAADManagerConfig.h>
#import <SoulBussinessKit/SoulBussinessKit-Swift.h>
#import <SoulCoreBase/SoulCoreBase.h>
#import <SoulRouter/SoulRouter.h>
#import <SOTPPCenter/SOTPPCenter.h>
#import <SoulSocialPrivate/SoulOpenApi.h>
#import <SoulUtils/SoulURLUtils.h>
#import <SoulUtils/SOValue.h>
#import <SoulCore/SoulAppUtil.h>
#import <SoulShareSDK/SoulShareSDK-Swift.h>

#import <SOFrameRemain/APPDeeplinkSkipManager.h>
#import <SoulProtocols/SoulWidgetServiceProtocol.h>
#import <SoulProtocols/SoulShareExtensionServiceProtocol.h>

// 本地
#import "SODeepLinkReportRequest.h"

// 自己
#import "AppUrlHandler.h"
B. pod模块依赖关系的书写：自己/二方/三方
✅ Pod依赖引入逻辑顺序举例：自己/二方/三方
target 'SoulEventBridge_Example' do
  // 自己
  pod 'SoulEventBridge', :path => '../' # 当前模块

  // 二方
  pod 'AFNetworking' # 使用component.json管理
  pod 'SDWebImage' # 使用component.json管理
  pod 'SoulMobEvent' # 使用component.json管理

  // 三方
  pod 'FMDB', '2.7.5', :source => '***********************:front/CocoapodsThirdSDKRepo.git'
  pod 'MMKV', '1.2.11', :source => '***********************:front/CocoapodsThirdSDKRepo.git'
  pod 'MMKVCore', '1.2.11', :source => '***********************:front/CocoapodsThirdSDKRepo.git'
  pod 'Masonry', '1.1.0', :source => '***********************:front/CocoapodsThirdSDKRepo.git'
  pod 'YYCategories', '1.0.4', :source => '***********************:front/CocoapodsThirdSDKRepo.git'
  pod 'YYModel', '1.0.4', :source => '***********************:front/CocoapodsThirdSDKRepo.git'
end
2.4.1 类结构顺序：生命周期/方法重写/通知/代理/事件/Get&Set/私有方法/公有方法(对外接口)
当一个文件的代码内容非常复杂时，参考使用「生命周期/方法重写/通知/代理/事件/Get&Set/私有方法/公有方法(对外接口)」的顺序，排序原因参考：
- 生命周期：每个类都包含且需要关注的内容，排在首位。
- 方法重写：生命周期也可以理解为更底层的方法重写，将方法重写紧靠生命周期
- 通知/代理/事件：三者本质都是回调，按照“亲疏”关系排序
- Get&Set：和类添加的属性相关，已经倾向于到达「业务层」，但还比较「模板化」
- 私有方法/公有方法：都属于具体的业务实现，但作为模块，公有方法的「实现思路」对使用者更为重要，将其放置到尾部，占取一个“顶点”，让业务方更容易查找。
A. 自定义View的结构参考
✅ 自定义View的结构参考

#import <Masonry/Masonry.h>
#import "XXXView.h"

@interface XXXView()
@property (nonatomic, strong) UIView *demoSubViewA;
@property (nonatomic, strong) UIView *demoSubViewB;
@end

@implementation XXXView

- (void)dealloc {
    // 如果视图有响应通知，记得在dealloc中取消通知的监听
}
- (id)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupViews];
        [self setupConstraints];
    }
    return self;
}
- (void)setupViews {
    // 视图的基础配置已经在懒加载Get方法中设置，
    //将视图的添加关系放置在一起，可以有效地呈现视图的结构信息
    [self addSubview:self.demoSubViewA];
    [self addSubview:self.demoSubViewB];
}
- (void)setupConstraints {
    // 一般情况下，视图的布局建议使用Masonry配置
    [self.demoSubViewA mas_makeConstraints:^(MASConstraintMaker *make) {
        ...
    }];
    [self.demoSubViewB mas_makeConstraints:^(MASConstraintMaker *make) {
        ...
    }];
}
// 方法重写，如layoutSubviews, drawRect等
#pragma mark - Rewrite

// 通知监听&处理
#pragma mark - Notifications

// 各种代理处理，每个代理理一个分组
#pragma mark - YYYDelegate

// 事件，如按钮点击的响应等
#pragma mark - Events

// Get/Set方法，一般情况下，属性建议使用懒加载的形式
#pragma mark - Getter & Setter

- (UIView *)demoSubViewA {
    if (!_demoSubViewA) {
        _demoSubViewA = [[UIView alloc] init];
        // 视图的确定配置也卸载Get方法中，提高单子视图的内聚性
        _demoSubViewA.backgroundColor = [UIColor redColor];
    }
    return _demoSubViewA;
}
- (UIView *)demoSubViewB {
    if (!_demoSubViewB) {
        _demoSubViewB = [[UIView alloc] init];
    }
    return _demoSubViewB;
}

// Misc意为杂项方法，这边一般是私有的方法
#pragma mark - Misc

// 用户接口，即公有方法
#pragma mark - User Interface

@end
2.4.3 嵌套规范：超过3层思考，超过5层优化
- 逻辑嵌套的层次不宜过多：可以用结构化的知识去理解，当我们第一步期望理解第一二层的框架的时候，表达者直接就将所有三四五层的细节完整告知，我们显然不会舒适。
- 3层思考，5层优化：逻辑嵌套超过三层，我们要想一想写法是否合适了；超过5层的逻辑嵌套，认知范围内很少有合理的场景了
A. 问题：过多的逻辑嵌套，视觉匹配括号的成本变高
 ❌ 纯看括号或许我们还能匹配，但：
 ❌ 1. 实际场景中括号间是有大量代码的，干扰信息众多，且使得括号常常不在同屏
 ❌ 2. 实际场景中，一些回括号可能写在一起，对视觉缩进匹配增加难度
 ❌ 3. 极端情况下，一些代码文件括号的缩进未必正确
    {
        {
            { 
            {
            }               
                {
                }
            }
        }
    }    
}
B. 问题：过多的逻辑嵌套，内层嵌套的代码缩进过大，留给代码的理想视区变短
                {
                    ❌ 最内层嵌套的缩进很大，其代码的视区会明显缩短
                    ❌ 比如该场景缩短20个字符
                }       
}}}}
C. 问题：过多的逻辑嵌套，往往伴随着过长的函数
嵌套过多的场景，往往伴随着超长的函数，往往伴随着违背函数的单一性原则（只做一件事情）。
D. 方案：提前返回去嵌套
- 思路：找到最外层逻辑不含子逻辑的分支，提前处理
- 思路：将层次结构转化为流程结构
- 常见场景：输入检查/异常处理
// ✅ 通过提前返回的方式保障更少的嵌套
// ✅ 通过封装细节的方式保障更少的嵌套，保障主流程的清晰
- (void)p_addDownload:(SODownloadModel *)downloadItem startImmediately:(BOOL)startImmediately {

    NSError *error = nil;
    
    /* 0 小文件下载分队列逻辑 */
    if ([self p_needMoveToSmallFileQueue:downloadItem]) {
        downloadItem.inSmallFileQueue = YES;
        [self.smallFileManager startDownload:downloadItem];
        return;
    }
    
    /* 1 参数检查 - 下载URL */
    if (!downloadItem.downloadURL) {
        [self p_setItemStateFailed:downloadItem];
        [self p_callbackErrorComplete:downloadItem error:soDownloadError(@"未输入下载URL")];
        return;
    }
    
    /* 2 [数据完善] 必要时补充下载目标路径 */
    [self p_replenishDownloadPathIfNeeded:downloadItem];

    /* 3 [完成检查] 文件无须重复下载检查 */
    BOOL isDownloadFinished = (!downloadItem.ignorePreviousFile && [[NSFileManager defaultManager] fileExistsAtPath:downloadItem.pathToFile]);
    if (isDownloadFinished) {
        [self p_setItemStateSuccess:downloadItem];
        [self p_callbackSuccessComplete:downloadItem];
        return;
    }
        
    /* 4 [任务管理] 确保添加下载Item到下载管理字典 */
    BOOL itemNewAdded = [self p_addDownloadToContainer:downloadItem];
    if (itemNewAdded) {
        /* 新加入的项目进行添加回调 */
        [self p_callbackAdd:downloadItem];
    }
    
    /* 5 [仅挂起逻辑] 仅挂起任务，暂不启动 */
    if (!startImmediately) {
        downloadItem.cachePolicy = NSURLRequestReloadIgnoringCacheData;
        [self p_setItemStateSuspending:downloadItem];
        [self p_callbackSuspended:downloadItem];
        return;
    }
    
    /* 6 兼容抢占逻辑 */
    [self p_smartHandleDownloadPreempt:downloadItem];
            
    /* 7 [异常处理] 当前无法启动任务，则让任务先等待，保持Ready状态 */
    if (![self p_canStartDownloadForNow:downloadItem]) {
        [self p_setItemStateWaiting:downloadItem];
        return;
    }
    
    /* 8 [下载准备 & 异常处理] 预创建下载目录 */
    error = [self p_createDownloadDirIfNeeded:downloadItem];
    if (error) {
        [self p_setItemStateFailed:downloadItem];
        [self p_callbackErrorComplete:downloadItem error:error];
        return;
    }
        
    /* 9 启动Task任务 */
    [self p_smartResumeTask:downloadItem];
    [self p_setItemStateDownloading:downloadItem];

    /* 10 日志&回调处理 */
    [self p_callbackResume:downloadItem];
}
E. 方案：封装去嵌套
- 思路：多嵌套往往伴随着做了过多细节，这些细节可以进行方法封装
- 示例：同见D的示例
F. 思考：为什么当前大火的「声明式UI」会伴随大量的嵌套？
- 视图关系的呈现更直接：UI本就绑定着「层次关系」的概念，声明式UI的层次结构将UI必然存在的「层次关系」最直接地呈现，可有效降低代码的阅读理解成本。
- 并非无限嵌套：即便是声明式UI，也不会建议我们无限制地增加嵌套层数，而是应该在合理的时机/场景封装UI构建块（Builder），或UI组件（Component）。
- 针对逻辑，同样需避免嵌套：针对声明式UI框架下的事件响应/逻辑操作，本质上也是一个个函数，他们中的逻辑处理也应该避免过多的嵌套
✅ 鸿蒙ArkTS的声明式UI写法Demo
✅ UI本身含层次关系，UI中的嵌套一定程度上可以更直接地反应该关系
@Component export struct SHAlertContainer {

  @Builder emptyChild(){}
  @BuilderParam child:() => void = this.emptyChild;
  closeBtnPos: SHAlertCloseBtnPos = SHAlertCloseBtnPos.None;
  closeBtnClicCallback: SHAlertClickCallback = (event: ClickEvent)=>{};

  @Builder closeButtonRightTop() {
    if (SHAlertCloseBtnPos.TopRight == this.closeBtnPos) {
      Row() {
        Image($r('app.media.sha_close_topright'))
          .width('30')
          .height('30')
          .objectFit(ImageFit.Fill)
          .offset({ x: -12 })
          .onClick(this.closeBtnClicCallback)
      }
      .margin({ left: '60%' })
      .width('40%')
      .height(54)
      .position({ x: 0, y: 0 })
      .justifyContent(FlexAlign.End)
      .alignItems(VerticalAlign.Center)
    }
  }

  @Builder closeButtonBottom() {
    Row() {
      if (SHAlertCloseBtnPos.Bottom == this.closeBtnPos) {
        Image($r('app.media.sha_close_bottom'))
          .width('33')
          .height('33')
          .objectFit(ImageFit.Fill)
          .onClick(this.closeBtnClicCallback)
      }
    }
    .width('100%')
    .height('56')
    .justifyContent(FlexAlign.Center)
    .alignItems(VerticalAlign.Bottom)
  }

  build() {
    Column() {
      SHAlertBoard() {
        this.child()
      }
      this.closeButtonBottom();
      this.closeButtonRightTop();
    }
    .width('78.93%')    
    .offset({ y:28 })   
  }
}
2.4.4 概念聚合
- 单类方法过多时，要考虑方法概念的聚合
- 一些本身就有聚合概念的方法（如同属某个代理协议），写在一起
A 代理回调要写在一起，并使用 #pragma mark -  标注
✅ 遵照协议需要显示声明
@interface SoulRTEventManager ()
<SONetworkStatusManagerProtocol,
SoulRTEventDBServiceDelegate,
SoulRTEventSocketServiceDelegate>
@end
✅ 代理回调要写在一起, 并使用 #pragma mark - xxxDelegate
@implementation SoulRTEventManager
#pragma mark - SONetworkStatusManagerProtocol
- (void)statusManagerStatusDidUpdate:(SONetworkStatusManager *)manager { ... }
#pragma mark - SoulRTEventDBServiceDelegate
- (void)dbService:(SoulRTEventDBService *)dbService postError:(NSError *)error { ... }
#pragma mark - SoulRTEventSocketServiceDelegate
- (void)socketService:(SoulRTEventSocketService *)service onError:(NSError *)error { ... }
- (void)socketServiceConnecStart:(SoulRTEventSocketService *)service { ... }
- (void)socketServiceConnectSuccess:(SoulRTEventSocketService *)service { ... }
- (void)socketServiceConnectClosed:(SoulRTEventSocketService *)service { ... }
- (void)socketServiceReceivePang:(SoulRTEventSocketService *)service { ... }
- (void)socketService:(SoulRTEventSocketService *)service receiveBusinessResp:(SoulRTEventResp *)businessResp { ... }
@end
2.5 逻辑性规范
2.5.1 避免在for循环内部改变循环索引
❌ 循环内部改变index值，逻辑变得更为复杂，操作不当会导致循环失控
for (int index = 0; index < 10; index++){ 
   ... 
   logicToChange(index) 
}  
2.5.2 避免在枚举内部操作被枚举的结构
❌ 枚举过程中对枚举结构的操作，使逻辑变得复杂，边缘场景的预期模拟变得困难
NSMutableArray *tmpStrArray = ...
for (NSString *tmpStr in tmpStrArray) {
    if ([tmpStr isEqualToString:@"1"]) {
        [tmpStrArray removeObject:tmpStr];
    }
}

// ==> 可调整为

✅ 枚举safeArray，操作tmpStrArray，safeArray枚举过程中保持不变，保障枚举全量，稳定地进行
NSMutableArray *tmpStrArray = ...
NSArray *safeArray = tmpStrArray.copy;
for (NSString *tmpStr in safeArray) {
    if ([tmpStr isEqualToString:@"1"]) {
        [tmpStrArray removeObject:tmpStr];
    }
}
2.5.3 函数内部比较复杂的逻辑提取出来作为单独的函数
一个函数内的不清晰(逻辑判断比较多，行数较多)的那片代码，往往可以被提取出去，构成一个新的函数，然后在原来的地方调用它这样你就可以使用有意义的函数名来代替注释，增加程序的可读性。
❌ 主流程包含较细节的代码
✅ 若文件本身代码行数不多，保持这样也无可厚非
openEmailSite(); 
login();     
writeTitle(title); 
writeContent(content); 
writeReceiver(receiver); 
addAttachment(attachment); 
send();  

-->

✅ 封装writeEmail方法，这需要抽象总结的能力
void writeEmail(title, content,receiver,attachment) 
{ 
  writeTitle(title); 
  writeContent(content); 
  writeReceiver(receiver); 
  addAttachment(attachment); 
}  

✅ 封装writeEmail方法，使主流程更加直观清晰
openEmailSite(); 
login(); 
writeEmail(title, content,receiver,attachment) 
send();   
2.6 技巧性规范参考
2.6.1 使用宏封装代码块的方法
使用宏定义常量，单行代码大家都不陌生，通过宏封装代码块也有一种常见写法可供参考
✅ 使用宏封装代码块的方法参考，着重在介绍方法
#define DemoLogic \
do { \
    int a = 2; \
    int b = 3; \
    NSLog(@"%d", a + b); \
} while(0)

// 使用
DemoLogic;
2.6.2 位移枚举使用NS_OPTIONS
✅ 当定义的枚举是基于掩码标记操作的位移枚举时，建议使用NS_OPTIONS
✅ 和NS_ENUM写法差异不大，但更符合概念
typedef NS_OPTIONS(NSUInteger, UISwipeGestureRecognizerDirection) {
    UISwipeGestureRecognizerDirectionNone = 0,  //值为0
    UISwipeGestureRecognizerDirectionRight = 1 << 0,  //值为2的0次方
    UISwipeGestureRecognizerDirectionLeft = 1 << 1,  //值为2的1次方
    UISwipeGestureRecognizerDirectionUp = 1 << 2,  //值为2的2次方
    UISwipeGestureRecognizerDirectionDown = 1 << 3  //值为2的3次方
};
2.6.3 使用比较运算符时，常量建议写在前面
❌ tmpVal == 25，当笔误写成 tmpVal = 25 时，可能造成逻辑异常
if (tmpVal == 25) { ... }

==> 调整为

✅ 而 25 = tmpVal 不合语法规范，编译器可提前帮我们发现相关错误
if (25 == tmpVal) { ... }
2.6.4 定义任何变量时，进行初始化
❌ 不进行显性的初始化，容易造成边缘逻辑漏洞
NSString *tmpStr;

==> 调整为

✅ 显性的初始化
NSString *tmpStr = nil;
NSString *tmpStr = @"";
NSString *tmpStr = @"unknown";
2.6.5 巧用字典映射

❌ 该场景，无论使用swifch还是if，都伴随着大量的「模板代码」:
❌ 每组if中变化的实际只有 errCode 和 errStr
- (NSString *)parseErr:(int)errCode {
    NSString *errStr = @"";
    if (1 == errCode) {
        errStr = @"错误1"
    } else if (1 == errCode) {
        errStr = @"错误2"
    }
    ...
    else if (n == errCode) {
        errStr = @"错误n"
    }
    return errStr;
}

==> 调整为

✅ 直接定义一个映射字典，让「模板代码」保持一份
✅ 很多扩展性的架构设计中，也应该参考该思路：只调整扩展，让「模板代码」保持一份
- (NSString *)parseErr:(int)errCode {
    NSDictionary *errStrMap = @{
        @(1): @"错误1",
        @(2): @"错误2",
        ...
        @(n): @"错误n",
    };
    NSString *errStr = errStrMap[@(errCode)];
    errStr = errStr ?: @""
    return errStr;
}
2.7 适应不同的规范：三方SDK/独立SDK，少量调整时，尽量遵照原有规范
- 部分代码规范旨在保障一定的一致性，很多场景没有绝对的对错
- 拟写新代码时，尽量按照约定的规范进行
- 调整旧模块代码时
  - 若代码本身存在有效地规范，则建议参照原规范调整
  - 若原代码本身就存在规范冲突，则使用我们约定的规范调整
2.8 业务开发规范
2.8.1 路由模板
新增路由可以参考使用路由模板
详见：20230213 SoulRouter Xcode文件模板对接
2.8.2 服务器数据解析使用健壮性解析接口
针对接口返回的解析，需要做一下健壮性设计，相关背景：客户端异常测试
A. 针对复杂的接口返回，建议抽象数据结构，通过YYModel进行安全数据映射
参考：https://github.com/ibireme/YYModel
B. 针对字典(NSDictionary)的Value，期望通过SO类型安全接口明确类型
NSDictionary *aDic = ...
// 字典期望确保下类型有效性（若已确保，无须该行代码）
aDic = SODictionary(aDic);

#import <SoulUtils/SoulUtils.h>

// 获取字典安全类型
NSString *valStr = SOString(aDic[key]);                 // 普通类型保障
NSString *valStr = SOStringWith(aDic[key], @"888");     // 带兜底的类型保障

NSInteger valInt = SOInteger(aDic[key]);                // 普通类型保障
NSInteger valInt = SOIntegerWith(aDic[key], 888);       // 带兜底的类型保障

float valFloat = SOFloat(aDic[key]);                    // 普通类型保障
float valFloat = SOFloatWith(aDic[key], 888.88);        // 带兜底的类型保障

double valDouble = SODouble(aDic[key]);                 // 普通类型保障
double valDouble = SODoubleWith(aDic[key], 888.88);     // 带兜底的类型保障

BOOL valBool = SOBool(aDic[key]);                       // 普通类型保障
BOOL valBool = SOBoolWith(aDic[key], NO);               // 带兜底的类型保障

NSDictionary *valDic = SODictionary(aDic[key]);                             // 普通类型保障
NSDictionary *valDic = SODictionaryWith(aDic[key], @{@"key": @"value"});    // 带兜底的类型保障

NSArray *valArr = SOArray(aDic[key]);                                       // 普通类型保障
NSArray *valArr = SOArrayWith(aDic[key], @[@"item1"]);                      // 带兜底的类型保障

C. 针对数组(NSArray)的访问，期望保障访问不越界
NSArray *anArr = ...

// 通过YYCategories的接口访问，可保障数组不越界
#import <YYCategories/YYCategories.h>
id tmpItem = [anArr objectOrNilAtIndex:index];
D. 服务器返回模型不要添加自定义属性，有可能导致冲突。业务领域模型可以从服务器数据模型继承或者转化
[图片]
2.8.3 初始化场景避免耗时函数的使用
A. 初始化场景应避免耗时函数的使用
// 以聊天相册的初始化举例
@implementation SOPhotoAlbumView

- (instancetype)initWithFrame:(CGRect)frame fetchConfig:(nullable SOPhotoAlbumBasicConfig *)config {
    // ✅ 父方法调用 - 合理
    if (self = [super initWithFrame:frame]) {
        // ✅ 基本配置初始化 - 合理
        _autoHidden = YES;
        _config = config;
        _albumIdentify = [NSString stringWithFormat:@"%u_%lu", arc4random() % 10000, (unsigned long)self.hash];
        
        // ✅ 基础的通知注册，不耗时 - 合理
        [self addNotifiaction];
        
        // ✅ 视图对象进行基础的UI构建操作 - 合理
        // 🤔 但具体实现时，子UI组件若直接配合了耗时操作，则不合适
        [self setupUI];
        
        // 🤔 具体问题具体分析，若简单的数据配置,则合理；
        //    若数据的填充需要辅以大量的计算，则不建议放置到初始化
        [self setupData];
        
        // ❌ 相册权限的请求，用户相册的加载。
        //    权限请求：和用户产生交互，是独立的行为，放置到初始化容易触发非预期的权限弹框(业务行为超出掌控）
        //    相册加载：可能造成极端耗时的行为，应作为独立方法在确定需要时进行显性的调用
        [self authorizate];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame {
    // ✅ 对初始化方法进行封装，合理
    return [self initWithFrame:frame fetchConfig:nil];
}

#end
B. 懒加载场景应避免耗时函数的使用
// 以聊天相册的懒加载举例

- (SOPhotoAlbumView *)photoAlbumBoard {
    if (!_photoAlbumBoard) {
    
        // ✅ 基础的视图初始化&基础配置 - 合理
        // 🤔 SOPhotoAlbumView的初始化方法中是否有耗时方法由它进一步保障 
        _photoAlbumBoard = [[SOPhotoAlbumView alloc] initWithFrame:CGRectMake(0, 0, KScreenWidth, kChatKeyBoardBGScrollerH + 6)];
        _photoAlbumBoard.fromSource = SoulEditMediaSrouceChatAlbum;
        _photoAlbumBoard.firstShowCannotSelect = YES;
        
        // ✅ 该场景为基础配置，合理
        // 🤔 要进一步关注 photoAlbumPlaceholderBoard/photoJudgeAide/presetingVc 3个
        //    懒加载是否有额外的耗时操作，属于子组件的检查范畴
        [_photoAlbumBoard setHalfSuperView:self.photoAlbumPlaceholderBoard halfFrame:CGRectMake(0, 0, KScreenWidth, kChatKeyBoardBGScrollerH + 6)];
        [_photoAlbumBoard setJudgeSelectDelegate:self.photoJudgeAide];
        _photoAlbumBoard.presetingBrowseViewController = self.presetingVc;

        // 🤔 要进一步关注 SOPhotoAlbumDoorModel初始化是否有额外的耗时操作
        SOPhotoAlbumDoorModel *takePhotoM = [[SOPhotoAlbumDoorModel alloc] initWithDoorType:SOPhotoAlbumDoorTypeJumpTakePhoto];
        _photoAlbumBoard.doorModels = @[takePhotoM];

        // ✅ 基础配置 - 合理
        _photoAlbumBoard.albumDelegate = self;
        _photoAlbumBoard.postingReleaseModel = nil;
        
        // ✅ 该场景为基础配置，合理
        // 🤔 关注 SOUserInfoManager 初始化是否有额外的耗时操作，属于子组件的检查范畴
        if ([SOUserInfoManager sharedInstance].baseModel.superVIP || [SOUserInfoManager sharedInstance].baseModel.ssr) {
            _photoAlbumBoard.vipDefaultDuration = 10 * 60;
        }
        
        // ✅ 该场景为基础配置，合理
        // 🤔 要进一步关注 supportExchangePicType/supprotSnap 2个
        //    懒加载是否有额外的耗时操作，属于子组件的检查范畴
        _photoAlbumBoard.supportExchangePicType = self.supportExchangePicType;
        _photoAlbumBoard.supprotSnap = self.supprotSnap;
        
        // 🤔 方法有可能有耗时操作，需检查确认
        [_photoAlbumBoard setStartShowWithHalfStart:YES animation:NO];
        
        // ✅ 基础配置 - 合理
        _photoAlbumBoard.showBottomSelect = YES;
        _photoAlbumBoard.canOrigin = YES;
        _photoAlbumBoard.sureBtnStr = @"发送";
    }
    return _photoAlbumBoard;
}
C. 相关问题：进入私聊时非期望地立刻初始化了相册
- 调整MR： https://123.soulapp-inc.cn/devops#/versionInner/integrate/detail/4/424/22146
2.8.4 版本限制处理
 ❗️版本兼容相关的代码添加后，需要进行版本兼容的测试验证
A 逻辑的版本限制
- (void)refreshImmediately{
    // ✅ 逻辑中的版本限制可以基于 @available() 实现，并可以介入if条件判断
    if (@available(iOS 14.0, *)) {
        [self.wormhole passMessageObjectWithMessage:@"refresh" identifier:refreshImmediatelyIdentifer];
    }
}
B 方法的版本限制
    // ✅ 一些方法若期望高版本可用，可对其进行标识
    @available(iOS 16.1, *)
    private func startLiveActivity(
        status: SOLiveDeliveryAttributes.BusinessContentStatus,
        forStyle style: LiveActivityStyle
    ) {
        ...
    }
C 类的版本限制 - ❗️关注路由
❗️ 针对版本声明的类，需再次保障版本兼容性的测试
相关问题：20241216 - iOS 13 系统启动闪退
// ✅ 期望高版本才能使用的类，可以对其进行标识
// ❗️ 路由在应用启动阶段会对所有的OC类进行检测，该场景的检测部关注版本兼容声明，可能造成崩溃，需要进行有效的测试
@available(iOS 14.0, *)
// ❗️ 当标注了public后，实际系统也会将其当做OC类，触发路由的检测
public class SOPhotosSystemPicker: SOPhotosPicker {
    // ❗️ 这边的处理实际是比较危险的，类构建时就可能触发PHPicker的构建，PHPicher是一个高版本方法，
    //    低版本误入类内时容易触发崩溃。
    private var configuration = PHPickerConfiguration(photoLibrary: .shared())
}
2.8.5 选项(位移枚举Option)应用规范
2.8.5.1 选项概念说明
Option是一类和枚举非常相近相似的类型定义，它的各个选项间常常可以组合存在，而不是像枚举一样必须相互互斥，比如：
// ✅ 其实定义了4中模式：
// 0 - 顺序+串行（默认）
// 1 - 顺序+并发 - NSEnumerationConcurrent
// 2 - 倒序+串行 - NSEnumerationReverse
// 3 - 倒序+并发 - NSEnumerationConcurrent & NSEnumerationReverse
// ❌ 而不是只能在 1 和 2 两个选项中互斥选择
typedef NS_OPTIONS(NSUInteger, NSEnumerationOptions) {
    NSEnumerationConcurrent = (1UL << 0),
    NSEnumerationReverse = (1UL << 1),
};

// SDWebImageOptions的定义更多，其组合很庞大，不一一列举
typedef NS_OPTIONS(NSUInteger, SDWebImageOptions) {
    SDWebImageRetryFailed = 1 << 0,
    SDWebImageLowPriority = 1 << 1,
    SDWebImageProgressiveLoad = 1 << 2,
    SDWebImageRefreshCached = 1 << 3,
    ...
    SDWebImageWaitStoreCache = 1 << 22,
    SDWebImageTransformVectorImage = 1 << 23,
    SDWebImageAvoidAutoCancelImage = 1 << 24,
    SDWebImageWaitTransition = 1 << 25,
};
2.8.5.2 选项使用方法参考
选项的使用，实际是掩码操作的使用
可参考：尝试使用位掩码Mmm...mask!
// ✅ 选项初始化
NSEnumerationOptions tmpOpt = 0;
NSEnumerationOptions tmpOpt = NSEnumerationConcurrent;
NSEnumerationOptions tmpOpt = NSEnumerationConcurrent | NSEnumerationReverse;

// ✅ 选项的叠加（按位或）
tmpOpt |= NSEnumerationConcurrent;
tmpOpt |= NSEnumerationReverse;

// ✅ 选项的取消（按位反与）
tmpOpt &= ~NSEnumerationConcurrent;
tmpOpt &= ~NSEnumerationReverse;

// ✅ 选项的重置
tmpOpt = 0;
tmpOpt = NSEnumerationReverse;
tmpOpt = NSEnumerationConcurrent;
tmpOpt = NSEnumerationConcurrent | NSEnumerationReverse;
2.8.5.3 问题案例解析
我们实际使用过程中，可能因一些对选项数据结构理解的不到位而发生的问题，比如私聊相册的枚举场景
    // ❌ 针对选项，其默认值应该是0, 而不是选项定义的第一个值
    //   这边第一个选项 NSEnumerationConcurrent 的概念是并发执行，其实并不是使用者所期望的
    //   可以直接初始化为0即可，如：NSEnumerationOptions enumOption = 0;    
    NSEnumerationOptions enumOption = NSEnumerationConcurrent;
    if (config.sortCreationDateDescending) {        
        // ✅ 直接对选项赋值是可接受的
        enumOption = NSEnumerationReverse;
    }
    
    [fetchResult enumerateObjectsWithOptions:enumOption usingBlock:^(PHAsset * _Nonnull  obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([self isCanAddWithAsset:obj Config:config]) {
            // ✅❌ 这边涉及对一个数组的操作，如果上面使用了 NSEnumerationConcurrent 选项，
            //     则枚举的回调变成的并发的，容易造成多线程问题。
            [assetArr addObject:[self getOwnAssetWithSystemAsset:obj]];
        }
        if (limitCount > 0 && assetArr.count == limitCount) {
            *stop = YES;
        }
    }];
2.8.6 属性访问规范
2.8.6.1 属性的访问一般建议使用点操作符(.)，避免使用箭头操作符(->)
针对nil对象的访问，使用 . 会比使用 -> 更安全
相关问题案例参考：20250114 群聊大厅导航栏换肤崩溃问题
// . -> 访问差异的简单Demo演示

@interface CYTestObj : NSObject
@property (nonatomic, assign) NSInteger customTag;
+ (void)doTest;
@end

@implementation CYTestObj
+ (void)doTest {
    CYTestObj *tmpObj = nil;
    // ✅ 推荐使用.操作符来访问对象的属性，它会被系统转化为方法的调用，会向相关对象发送命令消息
    //   iOS的机制中，nil是一个安全的消息接收体，任何发给nil的消息都会当做无效消息，而不会造成崩溃
    NSLog(@"%d", (int)(tmpObj.customTag));
    // ❌ ->相当于基于CYTestObj的数据结偏移去访问内存，0地址附近的内存一般是我们不可用的，自然会崩溃
    //    详情见下面的进一步测试示意说明
    NSLog(@"%d", (int)(tmpObj->_customTag));
    
    CYTestObj *tmpObj1 = [[CYTestObj alloc] init];
    // 有效对象地址，输出 105ed5ee0
    NSLog(@"%llx", (long long)(tmpObj1));                   
    // 有效对象地址属性，输出 105ed5ee8，✅ 可以看出来_customTag属性相对tmpObj1基地址的偏移是 8 （105ed5ee8 - 105ed5ee0）
    NSLog(@"%llx", (long long)(&(tmpObj1->_customTag)));    
    
    // nil对象地址，输出 0
    NSLog(@"%llx", (long long)(tmpObj));
    // nil对象地址属性，输出 8，❌ 显然，访问8这个地址是非常危险的，会造成崩溃
    NSLog(@"%llx", (long long)(&(tmpObj->_customTag)));
}
@end
2.8.6.2 底层/中间件/频繁调用场景可按需使用->，或者向架构组咨询建议
由于->是指针的直接访问，绕过了.的消息发送，极限性能考量的场景，效率会更高一些。
2.8.7 正确处理nil
2.8.7.1 防范对API调用使用nil
    大多数Apple的API对nil参数是不加防护的，典型如[NSMutableArray addObject:(ObjectType)anObject ], [NSMutableDictionary setObject:(ObjectType)anObject forKey:(id<NSCopying>)aKey] 均不支持使用nil为参数。因此在对这些API调用前需要判断参数是否为ni。一些较简洁的处理方法：[array addObjec:obj ?: @""]; 也可以使用 2.8.2 中SOVaule序列函数。
    在一些业务中需要注意是否有设置初始值。尤其是一会缓存类数据。例如：
- (void)resetAudioCategory
  {
     if (![self.preAudioSessionCategory isEqualToString:[AVAudioSession sharedInstance].category] &&
          !self.isNeedMute) {
          [[AVAudioSession sharedInstance] setCategory:self.preAudioSessionCategory error:nil];
      }
  由于preAudioSessionCategory为设置初始化的值，而调用[[AVAudioSession sharedInstance] setCategory:nil error:nil]会导致崩溃
2.8.7.2 block回调中self处理
  为了避免block持有对象导致的内存循环引用，通常会对block中对象会将self转换成weak automatic的变量，这样就避免了block对self强引用，即：
__weak typeof(self) weakSelf = self;
__weak typeof(self) weakSelf = self;
__weak __typeof(self) weakSelf = self;
  以上三种写法，任选一种都是可以的。 需要注意的是只有block被self本身持有，或者block被全局对象长期持有需要这样处理，如果block是一次性的例如
  if(complete){
      complete()
  }
 在上面的例子是不要对block中对象进行weak化的。
注意在延迟类的回调函数，由于持有对象可能已经释放（尤其是针对UI对象，由于切换场景，UI对象会被设置为nil）。尽量使用SOWeak(self) 和SOStrong(self),不要使用常用的@weakify(self)和@strong(self)。
同时还需要注意的是self.method() 和self->method()存在差别，前者self为nil不会触发调用，但是后者self为nil会导致崩溃。
3、Review意见记录
3.1 2024-07-17 昌鸿补充建议
1. 针对事件处理命名建议统一 onXXX:
  1. 在onXXX中代码避免过长，存在复杂业务逻辑可以抽取到子方法中，避免热修复时处理困难
    1. ✅已补充，见2.3.5-C
  2. onXXX::(UIResponder *)sender { 建议保留 参数sender，存在较多实现需要知道业务的操作入口 
    1. ✅已补充，见2.1.6-B
  3. 针对通知的处理，可以和事件一样命名onXXX: (NSNotification *)ntf 
    1. ✅已补充，见2.1.6-C
2. 针对delegate的实现方法应该写在一起，同时使用#pramga mark 注释 - ✅已补充，见2.4.4
  1. 同样，不建议在delegate中编写较长的业务代码 - ✅已补充，见2.3.5-C
3. 初始化创建方法
  - (instancetype) initWithXXX 使用 instancetype，而不是具体的类命名 - ✅已补充，见2.1.8 
  - 单例统一使用 + (instancetype)sharedInstance, sharedXXX或者 shared(swift) 命名
    -  ✅已补充，见2.1.7-A
  - 如果一个类需要可共享实例，但不限制创建多个实例，默认命名+（instancetype）DefaultXXX,
    -  ✅已补充，见2.1.7-B
4. 通用的一些命名规范 - ✅已补充，见2.1.1
  1. 服务接口数据结构定义 XXXModel  XXXResp
  2. 接口请求包装类 XXXRequest
  3. Router跳转 XXXRouter
  4. protocol定义类 XXX protocol
  5. ViewModel命名 XXXViewModel