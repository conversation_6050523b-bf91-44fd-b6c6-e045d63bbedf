---
alwaysApply: true
---
# SLog 日志记录标准规范

## 1. 规则概述

定义 Soul App 中客户端日志记录的标准调用方式和编码规范，使用 SLog 工具替代 `NSLog` 和 `print`。确保日志信息的一致性、可追溯性和可维护性，便于调试和问题排查。

---

## 2. 基本调用方式

### 关键词：slog/sloginfo/slogerr/slogwarn

### 2.1 Swift 调用方式

**✅ 正例：** 标准的日志记录方法
```swift
// 推荐：使用 SLog 的 info, warn, error 等方法，并提供明确的 tag
SLog.info(tag: "SquareFeed", content: "开始请求帖子列表...")
SLog.warn(tag: "Pay", content: "支付凭证校验失败，receipt: \(receipt)")
SLog.error(tag: "IM", content: "消息发送失败, error: \(error.localizedDescription)")
```

**❌ 反例：** 直接使用 `print` 或 `NSLog`
```swift
// 不推荐：日志无法被统一管理，在 Release 包中也会输出，影响性能
print("开始请求帖子列表...")
NSLog("消息发送失败, error: %@", error.localizedDescription)
```

### 2.2 Objective-C 调用方式

**✅ 正例：** 标准的日志记录方法
```objectivec
// 推荐：使用 SLog...WithTag 宏，代码更简洁
SLogInfoWithTag(SquareFeed, @"开始请求帖子列表...");
SLogWarnWithTag(Pay, @"支付凭证校验失败，receipt: %@", receipt);
SLogErrorWithTag(IM, @"消息发送失败, error: %@", error.localizedDescription);

// 也可以直接调用 SLogAPI 方法
// [SLogAPI infoWithTag:@"SquareFeed" content:@"开始请求帖子列表..."];
```

**❌ 反例：** 直接使用 `NSLog`
```objectivec
// 不推荐：日志无法被统一管理，在 Release 包中也会输出，影响性能
NSLog(@"开始请求帖子列表...");
```

---

## 3. 代码审查检查点

- [ ] 是否使用了 SLog 替代 `NSLog` 或 `print` 进行日志记录？
- [ ] `tag` 是否清晰地表达了业务模块，并且不为空？
- [ ] 日志级别 (`info`, `warn`, `error`) 是否使用恰当？
- [ ] 日志内容是否清晰、简洁，包含了必要的上下文信息？
- [ ] 是否避免在日志中记录敏感信息（如密码、Token）？

---

## 4. 相关规则

- 遵循 `coding-rules.mdc` 中的通用命名规范。
- 关键词：SLog, SLogAPI, 日志