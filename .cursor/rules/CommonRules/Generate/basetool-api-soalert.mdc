---
description: 
globs: 
alwaysApply: true
---

## 创建SOAlert弹窗

Swift 代码示例如下：
```swift
    // 若没有引用头文件则引用头文件
    import SOAlertKit

    /** 标题 */
    let titleElm: SOAlertTitleLabel = SOAlertElement(x: "标题")

    /** 弹窗正文 */
    let contentElm: SOAlertDetailLabel = SOAlertElement(x: "内容")

    /** 左按钮 */
    let leftBtnElm: SOAlertButton = SOAlertElement(x: "取消")
    leftBtnElm.setElementBackgroundColor(SOColorDefine.soAutoColor(1))
    leftBtnElm.setElementTextColor(SOColorDefine.soAutoColor(0))
    leftBtnElm.soAction = { }

    /** 右按钮 */
    let rightBtnElm: SOAlertButton = SOAlertElement(x: "确定")
    rightBtnElm.setElementBackgroundColor(SOColorDefine.soAutoColor(4))
    rightBtnElm.setElementTextColor(SOColorDefine.soAutoColor(3))
    rightBtnElm.soAction = { }

    /** 展示弹窗 */
    if let hands = SOAlertHands.shared() {
        let alertData = [[titleElm], [contentElm], [leftBtnElm, rightBtnElm]]
        let alert = hands.createAlert(withData: alertData, insetArray: [24, 12, 24])
        alert.setCloseStyle(SOAlertViewNoneCloseStyle)
        hands.addAlert(alert)
    }
```

OC 代码示例如下
```objc
    // 若没有引用头文件则引用头文件
    #import <SOAlertKit/SOAlertKit.h>

    // 标题
    SOAlertElement *titleEle = SOAlertTitle(@"标题");

    // 正文
    SOAlertElement *contentEle = SOAlertDetail(@"正文");

    // 左按钮
    SOAlertElement *leftBtn = SOAlertButton(@"取消");
    [leftBtn setElementBackgroundColor:GET_COLOR(4)];
    [leftBtn setElementTextColor:GET_COLOR(3)];
    [leftBtn addTarget:self selector:@selector(bgAlertClickRecoverDefault)];

    // 右按钮
    SOAlertElement *rightBtn = SOAlertButton(@"确定");
    [rightBtn setElementBackgroundColor:GET_COLOR(1)];
    [rightBtn setElementTextColor:GET_COLOR(0)];
    [rightBtn addTarget:self selector:@selector(bgAlertVIPAction)];
            
    // 展示
    id <SOAlertViewControlProtocol> alert = [SOAlertHandsInstance createAlertWithData:@[@[titleEle], @[contentEle], @[leftBtn, rightBtn]] insetArray:@[@24, @12, @24]];
    [alert setCloseStyle:SOAlertViewNoneCloseStyle];
    [SOAlertHandsInstance addAlert:alert];
```