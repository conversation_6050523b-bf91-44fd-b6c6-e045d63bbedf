# 库介绍
下面枚举了 Lottie 动画相关的库，生成 Lottie 动画相关原生代码的时候，需要优先考虑下面库的提供的方法。

## 库 `LOTAnimationView`: Lottie 动画库，提供高性能的矢量动画播放功能

### 1. 类: LOTAnimationView 继承自: UIView
```
// 基本属性
@property (nonatomic, assign, readonly) BOOL isAnimationSetted;
@property (nonatomic, readonly) BOOL isAnimationPlaying;
@property (nonatomic, assign) BOOL loopAnimation;
@property (nonatomic, assign) NSInteger loopAnimationCount;
@property (nonatomic, assign) CGFloat animationProgress;
@property (nonatomic, assign) CGFloat animationSpeed;
@property (nonatomic, copy, nullable) LOTAnimationCompletionBlock completionBlock;

// 基本创建方法
+ (nonnull instancetype)animationNamed:(nonnull NSString *)animationName;
+ (nonnull instancetype)animationNamed:(nonnull NSString *)animationName inBundle:(nullable NSBundle *)bundle;
+ (nonnull instancetype)animationWithFilePath:(nonnull NSString *)filePath;
+ (nonnull instancetype)animationWithJsonData:(nonnull NSData *)jsonData;

// ZIP包加载
+ (nonnull instancetype)animationWithPackURL:(nonnull NSURL *)url animationName:(nullable NSString *)animationName;

// 设置动画
- (void)setAnimationNamed:(nonnull NSString *)animationName;
- (void)setAnimationWithFilePath:(nonnull NSString *)jsonFilePath;
- (void)setAnimationWithZipPath:(nonnull NSString *)zipPath completeCallback:(void(^)(BOOL success))completeCalback;

// 播放控制
- (void)play;
- (void)pause;
- (void)stop;
- (void)playWithCompletion:(nullable LOTAnimationCompletionBlock)completion;
- (void)playFromProgress:(CGFloat)fromProgress toProgress:(CGFloat)toProgress loop:(BOOL)loop completion:(nullable LOTAnimationCompletionBlock)completion;
```

### 2. 基本使用规范

#### 2.1 Objective-C 使用方式
```objective-c
// 推荐：从文件路径加载动画
- (void)setupLottieAnimation {
    NSString *filePath = [[NSBundle mainBundle] pathForResource:@"loading" ofType:@"json"];
    LOTAnimationView *animationView = [LOTAnimationView animationWithFilePath:filePath];
    
    // 配置动画属性
    animationView.loopAnimation = YES;
    animationView.frame = CGRectMake(0, 0, 100, 100);
    animationView.center = self.view.center;
    
    [self.view addSubview:animationView];
    [animationView play];
}

// 推荐：从Bundle加载动画
- (void)setupBundleAnimation {
    LOTAnimationView *animationView = [LOTAnimationView animationNamed:@"celebration"];
    animationView.frame = CGRectMake(50, 50, 200, 200);
    animationView.loopAnimationCount = 3;
    
    [self.view addSubview:animationView];
    [animationView playWithCompletion:^(BOOL animationFinished) {
        NSLog(@"动画播放完成");
    }];
}

// 推荐：ZIP包动画加载
- (void)setupZipAnimation {
    LOTAnimationView *animationView = [[LOTAnimationView alloc] init];
    animationView.frame = CGRectMake(50, 50, 200, 200);
    [self.view addSubview:animationView];
    
    NSString *zipPath = [[NSBundle mainBundle] pathForResource:@"animations" ofType:@"zip"];
    [animationView setAnimationWithZipPath:zipPath completeCallback:^(BOOL success) {
        if (success) {
            [animationView play];
        }
    }];
}
```

#### 2.2 Swift 使用方式
```swift
// 推荐：Swift中的Lottie动画使用
func setupLottieAnimation() {
    guard let filePath = Bundle.main.path(forResource: "loading", ofType: "json") else { return }
    
    let animationView = LOTAnimationView.animationWithFilePath(filePath)
    animationView.loopAnimation = true
    animationView.frame = CGRect(x: 0, y: 0, width: 100, height: 100)
    animationView.center = view.center
    
    view.addSubview(animationView)
    animationView.play()
}

// 推荐：从Bundle加载
func setupBundleAnimation() {
    let animationView = LOTAnimationView.animationNamed("celebration")
    animationView.frame = CGRect(x: 50, y: 50, width: 200, height: 200)
    animationView.loopAnimationCount = 3
    
    view.addSubview(animationView)
    animationView.play { finished in
        print("动画播放完成: \(finished)")
    }
}
```

### 3. 使用注意事项
- **统一管理**：所有Lottie动画优先使用 `LOTAnimationView`
- **资源优化**：合理选择JSON文件或ZIP包格式
- **性能考虑**：避免同时播放过多复杂动画
- **内存管理**：在视图消失时及时停止动画播放
- **异步加载**：ZIP包动画加载是异步的，注意回调处理
- 遵循 `coding-rules.mdc` 中的命名规范
- 配合 `gen-swift.mdc` 和 `gen-oc.mdc` 的代码生成规范
description:
globs:
alwaysApply: true
---