# 库介绍
下面枚举了权限管理相关的库，生成权限相关原生代码的时候，需要优先考虑下面库的提供的方法。

## 库 `SoulApplicationAuthorization`: 系统权限管理库，提供统一的权限检查和请求功能

### 1. 类: SoulApplicationAuthorization 继承自: NSObject
```
// 获取单例
+ (instancetype)shareInstance;

// 跳转系统设置页面
- (void)goSetting;

// 相机权限
- (AVAuthorizationStatus)cameraAuthorizationStatus;
- (BOOL)isCameraAuthorizationed;
- (void)requestCameraAuthorizationWithResult:(void (^)(BOOL result))finishBlock;

// 相册权限
- (PHAuthorizationStatus)photoAlbumAuthorizationStatus;
- (BOOL)isPhotoAlbumAuthorizationed;
- (void)requestPhotoAlbumAuthorizationWithResult:(void (^)(BOOL result))finishBlock;

// 麦克风权限
- (AVAuthorizationStatus)audioAuthorizationStatus;
- (BOOL)isAudioAuthorizationed;
- (void)requestAudioAuthorizationWithResult:(void (^)(BOOL result))finishBlock;

// 通讯录权限
- (CNAuthorizationStatus)addressBookAuthorizationStatus;
- (BOOL)isAddressBookAuthorizationed;
- (void)requestAddressBookAuthorizationWithResult:(void (^)(BOOL result))finishBlock;

// 地理位置权限
- (CLAuthorizationStatus)locationAuthorizationStatus;
- (BOOL)isLocationAuthorizationed;
- (void)requestLocationAuthorizationeWithResult:(void (^)(BOOL result))finishBlock;

// 通知权限
- (void)isRemoteNotificationAuthorizationedBlock:(void (^)(BOOL enabel, BOOL isNotDetermined))resulstBlock;
- (void)requestRemoteNotificationAuthorizationedWithResult:(void (^)(BOOL result))finishBlock;

// IDFA权限
- (BOOL)isIDFAAuthorizationed;
- (void)requestIdfaAuthorizationWithResult:(void (^)(BOOL result))finishBlock;
- (BOOL)isGetedIdfaAuthorizationStatus;
```

### 2. 基本使用规范

#### 2.1 Objective-C 使用方式
```objective-c
// 推荐：统一的权限检查和请求流程
- (void)requestCameraPermission {
    SoulApplicationAuthorization *auth = [SoulApplicationAuthorization shareInstance];
    
    // 先检查权限状态
    if ([auth isCameraAuthorizationed]) {
        [self openCamera];
        return;
    }
    
    // 请求权限
    [auth requestCameraAuthorizationWithResult:^(BOOL result) {
        if (result) {
            [self openCamera];
        } else {
            [auth goSetting]; // 引导用户去设置
        }
    }];
}

// 推荐：音频权限检查（语聊房、KTV等场景）
- (void)checkAudioPermission {
    BOOL authorization = [[SoulApplicationAuthorization shareInstance] isAudioAuthorizationed];
    if (!authorization) {
        [[SoulApplicationAuthorization shareInstance] requestAudioAuthorizationWithResult:^(BOOL result) {
            // 处理权限请求结果
        }];
    }
}

// 推荐：通知权限检查
- (void)checkNotificationPermission {
    [[SoulApplicationAuthorization shareInstance] isRemoteNotificationAuthorizationedBlock:^(BOOL enabel, BOOL isNotDetermined) {
        if (!enabel && !isNotDetermined) {
            // 权限被拒绝，引导用户开启
            [[SoulApplicationAuthorization shareInstance] requestRemoteNotificationAuthorizationedWithResult:^(BOOL result) {
                // 处理结果
            }];
        }
    }];
}
```

#### 2.2 Swift 使用方式
```swift
// 推荐：Swift中的权限检查
func checkAudioPermission() {
    let authorization = SoulApplicationAuthorization.shareInstance().isAudioAuthorizationed()
    if !authorization {
        SoulApplicationAuthorization.shareInstance().requestAudioAuthorization { result in
            // 处理权限请求结果
        }
    }
}

// 推荐：相机权限请求
func requestCameraPermission() {
    SoulApplicationAuthorization.shareInstance().requestCameraAuthorization { [weak self] result in
        DispatchQueue.main.async {
            if result {
                self?.openCamera()
            } else {
                SoulApplicationAuthorization.shareInstance().goSetting()
            }
        }
    }
}
```

### 3. 使用注意事项
- **统一管理**：所有权限操作优先使用 `SoulApplicationAuthorization`
- **先检查后请求**：执行权限相关功能前，先检查权限状态
- **用户引导**：权限被拒绝时，使用 `goSetting` 引导用户去设置
- **线程安全**：权限回调可能在子线程，UI操作需切换到主线程
- 遵循 `coding-rules.mdc` 中的命名规范
- 配合 `gen-swift.mdc` 和 `gen-oc.mdc` 的代码生成规范
description:
globs:
alwaysApply: true
---
