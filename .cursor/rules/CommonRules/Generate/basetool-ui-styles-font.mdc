---
description:
globs:
alwaysApply: true
---
# UI 字体设置标准规则

## 1. 规则概述
定义 Soul App 中 UI 字体设置的标准调用方式和编码规范，确保字体管理的一致性、可读性和可维护性，并支持不同字体粗细和大小的灵活设置。

## 2. 基本调用方式

### 2.1 Swift 调用方式
✅ 正例：标准的字体设置方法  
通过统一的 `.soul.PingFangSC(size:type:)` 方法设置字体，支持设置字体大小和宽度类型。

```swift
// 推荐：通过统一方法设置字体
label.font = .soul.PingFangSC(size: 12)
label.font = .soul.PingFangSC(size: 16, type: .medium)
```

- `size`：字体大小，类型为 `CGFloat`
- `type`：字体宽度类型，类型为 `PingFangSCType`，默认 `.regular`

#### Swift 枚举定义
```swift
public enum PingFangSCType: String {
    case ultralight  // 100
    case thin        // 200
    case light       // 300
    case regular     // 400
    case medium      // 500
    case bold        // 600
}
```

#### Swift 方法定义
```swift
public static func PingFangSC(size: CGFloat, type: PingFangSCType = .regular) -> UIFont
```

### 2.2 Objective-C 调用方式
✅ 正例：标准的字体设置方法  
通过统一的 `SOPingfangFont` 等宏设置字体，支持设置字体大小和宽度。

```objective-c
// 推荐：通过统一宏设置字体
label.font = SOPingfangFont(12);
label.font = SOPingfangMediumFont(16);
label.font = SOPingfangBoldFont(18);
```

- `SOPingfangLightFont(x)`        // Light
- `SOPingfangFont(x)`             // Regular
- `SOPingfangMediumFont(x)`       // Medium
- `SOPingfangBoldFont(x)`         // Semibold

#### Objective-C 宏定义
```objective-c
#define SOPingfangLightFont(x)    [UIFont fontWithName:@"PingFangSC-Light" size:x]      // 300
#define SOPingfangFont(x)         [UIFont fontWithName:@"PingFangSC-Regular" size:x]    // 400
#define SOPingfangMediumFont(x)   [UIFont fontWithName:@"PingFangSC-Medium" size:x]     // 500
#define SOPingfangBoldFont(x)     [UIFont fontWithName:@"PingFangSC-Semibold" size:x]   // 600
```

## 3. 命名规范

- 统一使用 `.soul.PingFangSC(size:type:)`（Swift）或 `SOPingfangFont(x)` 等宏（Objective-C）进行字体设置。
- 字体类型（type）命名应与 `PingFangSCType` 枚举保持一致，便于理解和维护。
- 字体相关方法、宏、枚举应集中管理，便于统一维护和升级。

## 4. 字体参数说明

- **字体大小**：必须为数字类型（Swift 为 CGFloat，Objective-C 为 float/double/int）。
- **字体宽度**：
  - Swift：通过 `type` 参数传递，类型为 `PingFangSCType`，默认 `.regular`。
  - Objective-C：通过不同宏区分（如 SOPingfangFont、SOPingfangMediumFont 等）。

## 5. 代码审查检查点

- [ ] 是否统一使用 `.soul.PingFangSC(size:type:)` 或 `SOPingfangFont(x)` 等宏设置字体
- [ ] 字体类型（type）是否有注释或文档说明
- [ ] 是否集中管理字体相关方法、宏、枚举
- [ ] 是否支持不同字体宽度和大小的灵活设置
- [ ] 是否禁止在业务代码中硬编码字体名称

## 6. 相关规则

- 遵循 `coding-rules.mdc` 中的命名规范
- 配合 `gen-swift.mdc` 和 `gen-oc.mdc` 的代码生成规范
- 参考现有的字体管理工具类实现
- 字体样式与颜色、AB 实验等其他 UI 规范保持一致的调用和管理方式