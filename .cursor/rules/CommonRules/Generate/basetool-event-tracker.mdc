---
description:
globs:
alwaysApply: true
---
# 埋点记录上报标准规则

## 1. 规则概述
定义 Soul App 中埋点（事件追踪）记录与上报的标准调用方式和编码规范，确保埋点逻辑的一致性、可读性和可维护性。

## 2. 基本调用方式

### 2.1 Swift 调用方式
✅ 正例：标准的埋点上报方法
```swift
// 页面曝光（PV）
SoulEvent.eventPageId("", pagePrama: nil)

// 曝光（Expose）
var eventParams: [String:Any] = [:]
SoulEvent.eventExpose("", params: eventParams, pageId: nil, pagePrama: nil)

// 点击（Click）
var eventParams: [String:Any] = [:]
SoulEvent.eventClick("", params: eventParams, pageId: nil, pagePrama: nil)

// 性能（Perform）
var eventParams: [String:Any] = [:]
SoulEvent.eventPerform("", params: eventParams, pageId: nil, pagePrama: nil)
```

❌ 反例：直接拼接参数或硬编码事件上报逻辑
```swift
// 不推荐：直接拼接参数或硬编码事件名
let eventName = "page_view_" + pageId
SoulEvent.eventClick(eventName, params: ["key": "value"], pageId: nil, pagePrama: nil)
```

### 2.2 Objective-C 调用方式
✅ 正例：标准的埋点上报方法
```objective-c
// 页面曝光（PV）
[SoulEvent eventPageId:@"" pagePrama:nil];

// 曝光（Expose）
NSMutableDictionary *eventParams = [NSMutableDictionary new];
[SoulEvent eventExpose:@"" params:eventParams pageId:nil pagePrama:nil];

// 点击（Click）
NSMutableDictionary *eventParams = [NSMutableDictionary new];
[SoulEvent eventClick:@"" params:eventParams pageId:nil pagePrama:nil];

// 性能（Perform）
NSMutableDictionary *eventParams = [NSMutableDictionary new];
[SoulEvent eventPerform:@"" params:eventParams pageId:nil pagePrama:nil];
```

❌ 反例：直接拼接参数或硬编码事件上报逻辑
```objective-c
// 不推荐：直接拼接参数或硬编码事件名
NSString *eventName = [NSString stringWithFormat:@"page_view_%@", pageId];
[SoulEvent eventClick:eventName params:@{ @"key": @"value" } pageId:nil pagePrama:nil];
```

## 3. 命名规范
- 方法名需表达埋点业务含义，避免模糊命名。
- 区分 PV、Expose、Click、Perform 四类事件，方法名需显式区分。
- 推荐命名方式：`eventPageId`、`eventExpose`、`eventClick`、`eventPerform`。
- 事件名、参数建议集中管理，避免硬编码。

## 4. 参数与配置
- `eventId`：事件唯一标识，类型为字符串，需支持常量或枚举集中管理。
- `params`：事件参数，类型为字典，需支持可选。
- `pageId`：页面唯一标识，类型为字符串，需支持可选。
- `pagePrama`：页面参数，类型为字典，需支持可选。
- 推荐将常用 eventId/参数定义为常量或枚举。

## 5. 可维护性与扩展性
- 埋点逻辑应封装在独立方法或事件追踪管理器中，禁止在业务代码中直接拼接事件名或参数。
- 支持统一的参数校验和错误处理。
- 支持扩展事件类型、参数等配置。

## 6. 代码审查检查点
- [ ] 是否封装为独立方法，避免重复代码
- [ ] 方法命名是否表达业务含义，区分事件类型
- [ ] 是否集中管理 eventId/参数，避免硬编码
- [ ] 是否处理参数为 nil 的情况
- [ ] 是否有统一的参数校验和错误处理
- [ ] 是否禁止直接拼接事件名/参数
- [ ] 是否添加必要注释说明事件用途

## 7. 相关规则
- 遵循 `coding-rules.mdc` 中的命名规范
- 配合 `gen-swift.mdc` 和 `gen-oc.mdc` 的代码生成规范
- 参考现有的 `SoulEvent` 等埋点工具类实现
- 埋点逻辑风格与 UI 颜色、字体等其他规范保持一致的调用和管理方式