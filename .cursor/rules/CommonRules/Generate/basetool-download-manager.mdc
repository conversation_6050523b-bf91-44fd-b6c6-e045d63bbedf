---
description:
globs:
alwaysApply: true
---
# 资源下载标准规则

## 1. 规则概述
定义 Soul App 中资源下载的标准调用方式和编码规范，确保下载逻辑的一致性、可读性和可维护性。

## 2. 基本调用方式

### 2.1 Swift 调用方式
✅ 正例：标准的资源下载方法
```swift
// 推荐：封装为具体业务方法，方法名表达下载意图
func download(url: String?, completion: @escaping (URL?) -> Void) {
    guard let url = url else {
        completion(nil)
        return
    }
    SODownloadManager.sharedInstance().download(withUrl: url, progress: nil) { error, fileURL, obj in
        guard error == nil, let fileURL = fileURL else {
            completion(nil)
            return
        }
        completion(fileURL)
    }
}
```

❌ 反例：直接使用 URLSession 或拼接下载逻辑
```swift
// 不推荐：直接拼接下载逻辑，缺乏复用和维护性
let task = URLSession.shared.downloadTask(with: URL(string: url)!) { url, response, error in ... }
task.resume()
```

### 2.2 Objective-C 调用方式
✅ 正例：标准的资源下载方法
```objective-c
// 推荐：封装为具体业务方法，方法名表达下载意图
- (void)downloadWithURL:(NSString *)url completion:(void(^)(NSURL * _Nullable fileUrl))completion {
    if (url.length == 0) {
        if (completion) completion(nil);
        return;
    }
    [SODownloadManager.sharedInstance downloadWithUrl:url progress:nil options:DownloadOptionsIgnoreRepeatDownload completion:^(NSError * _Nullable error, NSURL * _Nullable fileUrl, NSObject * _Nonnull model) {
        if (error) {
            if (completion) completion(nil);
        } else {
            if (completion) completion(fileUrl);
        }
    }];
}
```

❌ 反例：直接使用 NSURLSession 或拼接下载逻辑
```objective-c
// 不推荐：直接拼接下载逻辑，缺乏复用和维护性
NSURLSession *session = [NSURLSession sharedSession];
[[session downloadTaskWithURL:[NSURL URLWithString:url]] resume];
```

## 3. 命名规范
- 方法名需表达下载业务含义，避免模糊命名。
- 推荐命名方式：`downloadAvatarWithURL:completion:`、`downloadResource(url:completion:)`。
- 参数命名统一为 `url`，回调为 `completion`。

## 4. 参数与配置
- `url`：资源下载地址，类型为字符串，需支持可选或判空。
- `completion`：下载完成回调，返回本地文件 URL 或 nil。
- 进度、下载选项等参数建议支持扩展，但业务层默认可传 nil 或默认值。

## 5. 可维护性与扩展性
- 下载逻辑应封装在独立方法或下载管理器中，禁止在业务代码中直接拼接下载逻辑。
- 支持统一的错误处理和回调。
- 支持扩展下载选项、进度回调等配置。

## 6. 代码审查检查点
- [ ] 是否封装为独立方法，避免重复代码
- [ ] 方法命名是否表达业务含义
- [ ] 是否集中管理下载参数
- [ ] 是否处理 url 为空或无效的情况
- [ ] 是否有统一的错误处理
- [ ] 是否禁止直接拼接下载逻辑
- [ ] 是否添加必要注释说明接口用途

## 7. 相关规则
- 遵循 `coding-rules.mdc` 中的命名规范
- 配合 `gen-swift.mdc` 和 `gen-oc.mdc` 的代码生成规范
- 参考现有的 `SODownloadManager` 等下载工具类实现
- 下载逻辑风格与 UI 颜色、字体等其他规范保持一致的调用和管理方式