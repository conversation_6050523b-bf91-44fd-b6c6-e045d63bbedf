---
description:
globs:
alwaysApply: true
---
# UI 颜色设置标准规则

## 1. 规则概述
定义 Soul App 中 UI 颜色设置的标准调用方式和编码规范，确保颜色管理的一致性、可读性和可维护性，并支持深色模式自动适配。

## 2. 基本调用方式

### 2.1 Swift 调用方式
✅ 正例：标准的颜色设置方法
```swift
// 推荐：通过统一的 color(tag) 方法设置颜色
view.backgroundColor = .soul.color(0)
label.textColor = .soul.color(1)
```

### 2.2 Objective-C 调用方式
✅ 正例：标准的颜色设置方法
```objective-c
// 推荐：通过统一的 SOColor(tag) 方法设置颜色
view.backgroundColor = SOColor(0);
label.textColor = SOColor(1);
```

## 3. 命名规范
- 统一使用 `.soul.color(tag)`（Swift）或 `SOColor(tag)`（Objective-C）进行颜色设置。
- 颜色 Tag 命名应有注释或文档，说明每个 Tag 的业务含义和用途。
- 颜色映射表建议使用常量、枚举或配置文件集中管理。

## 4. 深色模式适配
- 每个颜色 Tag 必须同时定义浅色和深色值。
- 颜色方法内部自动根据系统模式返回对应颜色。
- 禁止在业务代码中手动判断深色模式，全部交由 color 方法内部处理。

## 5. 代码审查检查点
- [ ] 是否统一使用 color(tag) 或 SOColor(tag) 设置颜色
- [ ] 颜色 Tag 是否有注释或文档说明
- [ ] 是否集中管理颜色映射表
- [ ] 是否支持深色模式自动适配
- [ ] 是否禁止在业务代码中手动判断深色模式

## 6. 相关规则
- 遵循 `coding-rules.mdc` 中的命名规范
- 配合 `gen-swift.mdc` 和 `gen-oc.mdc` 的代码生成规范
- 参考现有的颜色管理工具类实现