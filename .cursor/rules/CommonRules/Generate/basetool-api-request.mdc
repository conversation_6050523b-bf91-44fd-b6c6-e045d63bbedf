---
description:
globs:
alwaysApply: true
---
# 网络请求标准规则

## 1. 规则概述
定义 Soul App 中网络请求的标准调用方式和编码规范，确保网络层代码的一致性、可读性和可维护性。

## 2. 基本调用方式

### 2.1 Swift 调用方式
✅ 正例：标准的 GET/POST 请求方法
```swift
// 推荐：封装为具体业务方法，方法名表达请求意图
func requestGet(parameters: [String: Any]?, completion: @escaping (Bool) -> Void) {
    guard let host = HttpDnsHelper.curEnvDomain(.newPost) else {
        completion(false)
        return
    }
    let path = ""
    SoulAPIManager.sharedInstance()?.requestGet(path, baseUrl: host, parameters: parameters) { model in
        guard model.codeSuccess else {
            completion(false)
            return
        }
        completion(true)
    } failure: { code, message in
        completion(false)
    }
}

func requestPost(parameters: [String: Any]?, completion: @escaping (Bool) -> Void) {
    guard let host = HttpDnsHelper.curEnvDomain(.newPost) else {
        completion(false)
        return
    }
    let path = ""
    let parameters = parameters ?? [:]
    guard let body = try? JSONSerialization.data(withJSONObject: parameters, options: .prettyPrinted) else {
        completion(false)
        return
    }
    SoulAPIManager.sharedInstance()?.post(withUrl: path, baseUrl: host, body: body) { model in
        guard model.codeSuccess else {
            completion(false)
            return
        }
        completion(true)
    } failure: { code, message in
        completion(false)
    }
}
```

❌ 反例：直接在业务逻辑中拼接 URL 或参数
```swift
// 不推荐：直接拼接 URL 或参数，缺乏复用和维护性
let url = "https://api.soulapp.cn/v1/test?param=1"
URLSession.shared.dataTask(with: URL(string: url)!) { ... }
```

### 2.2 Objective-C 调用方式
✅ 正例：标准的 GET/POST 请求方法
```objective-c
// 推荐：封装为具体业务方法，方法名表达请求意图
- (void)requestGetWithParameters:(NSDictionary *)parameters completion:(void(^)(BOOL success))completion {
    SoulNetworkRequest *request = [[SoulNetworkRequest alloc] init];
    request.server = SONewPostBaseDomain;
    request.httpMethod = SoulNetworkTTPMethodGET;
    request.path = @"";
    request.parameters = [parameters mutableCopy];
    request.requestSerializerType = SoulNetworkRequestSerializerJSON;
    [SoulAPIManager.sharedInstance request:request success:^(SoulResponseCommonModel * _Nullable model, SoulNetworkBaseRequest * _Nonnull request) {
        if (model.codeSuccess) {
            if (completion) completion(true);
            return;
        }
        if (completion) completion(false);
    } failure:^(NSInteger status, NSString * _Nullable msg, SoulNetworkBaseRequest * _Nonnull request) {
        if (completion) completion(false);
    }];
}

- (void)requestPostWithParameters:(NSDictionary *)parameters completion:(void(^)(BOOL success))completion {
    SoulNetworkRequest *request = [[SoulNetworkRequest alloc] init];
    request.server = SONewPostBaseDomain;
    request.httpMethod = SoulNetworkTTPMethodPOST;
    request.path = @"";
    request.parameters = [parameters mutableCopy];
    request.requestSerializerType = SoulNetworkRequestSerializerJSON;
    [SoulAPIManager.sharedInstance request:request success:^(SoulResponseCommonModel * _Nullable model, SoulNetworkBaseRequest * _Nonnull request) {
        if (model.codeSuccess) {
            if (completion) completion(true);
            return;
        }
        if (completion) completion(false);
    } failure:^(NSInteger status, NSString * _Nullable msg, SoulNetworkBaseRequest * _Nonnull request) {
        if (completion) completion(false);
    }];
}
```

❌ 反例：直接拼接 URL 或参数
```objective-c
// 不推荐：直接拼接 URL 或参数，缺乏复用和维护性
NSString *url = [NSString stringWithFormat:@"https://api.soulapp.cn/v1/test?param=1"];
NSURLSession *session = [NSURLSession sharedSession];
[[session dataTaskWithURL:[NSURL URLWithString:url]] resume];
```

## 3. 命名规范
- 方法名需表达请求业务含义，避免模糊命名。
- GET/POST 方法需显式区分。
- 推荐命名方式：`requestGetUserInfo`、`requestPostFeedback`。
- 参数命名统一为 `parameters`，路径命名为 `path`。

## 4. 参数与配置
- `path`：接口路径，需集中管理，避免硬编码。
- `parameters`：请求参数，类型为字典，需支持可选。
- `baseUrl`/`server`：统一通过环境配置获取。
- 推荐将常用 path/参数定义为常量或枚举。

## 5. 可维护性与扩展性
- 网络请求逻辑应封装在独立方法或网络层中，禁止在业务代码中直接拼接 URL 或参数。
- 支持统一的错误处理和回调。
- 支持扩展请求头、超时、重试等配置。

## 6. 代码审查检查点
- [ ] 是否封装为独立方法，避免重复代码
- [ ] 方法命名是否表达业务含义
- [ ] 是否区分 GET/POST
- [ ] 是否集中管理 path/参数
- [ ] 是否处理参数为 nil 的情况
- [ ] 是否有统一的错误处理
- [ ] 是否禁止直接拼接 URL/参数
- [ ] 是否添加必要注释说明接口用途

## 7. 相关规则
- 遵循 `coding-rules.mdc` 中的命名规范
- 配合 `gen-swift.mdc` 和 `gen-oc.mdc` 的代码生成规范
- 参考现有的 `SoulAPIManager`、`SoulNetworkRequest` 等网络工具类实现
- 网络请求风格与 AB 实验、UI 颜色等其他规范保持一致的调用和管理方式