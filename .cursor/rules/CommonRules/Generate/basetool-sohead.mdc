---
description: 
globs: 
alwaysApply: true
---
### 需要使用头像组件时，使用SoulHeadPortrait

- 生成一个头像组件视图
OC代码示意如下
```objective-c
    #import <SoulHeadPortrait/SoulHeadPortrait.h>
    SOHeadPortraitView *headView = [[SOHeadPortraitView alloc] init];
    headView.sizeType = 48;                     // 设置头像的大小
    [headView setHeadWithUrl:headUrl];          // 设置头像图片
    [headView setHeadFrameWithUrl:frameUrl];    // 设置头像挂件
```

Swift代码示意如下
```swift
    import SoulHeadPortrait
    let headView = SOHeadPortraitView()
    headView.sizeType = 48                      // 设置头像的大小
    portraitView.setHeadWith(headUrl)           // 设置头像图片
    portraitView.setHeadFrameWith(headUrl)      // 设置头像挂件
```

