---
description: 
globs: 
alwaysApply: true
---
Rule Name: CommonRules/ab-test-standards
Description: 
# AB 实验调用标准规则

## 1. 规则概述
定义 Soul App 中 AB 实验的标准调用方式和编码规范，确保AB实验代码的一致性、可读性和可维护性。

## 2. 基本调用方式

### 2.1 Objective-C 调用方式
```objective-c
// 推荐：封装为具体的业务方法，方法名清晰表达AB实验的作用
// 例如：
- (BOOL)isDiscoverTabOptimizationEnabled {
    return SOABValue(@"213751");
}
```

### 2.2 Swift 调用方式
```swift
// 推荐：封装为计算属性或方法
private var isNewChatUIEnabled: Bool {
    guard let ab = ABTestStrategyCenter.shareInstance().multiTestStrategy(forKey: "214284"),
          ab.value == "a" else {
        return false
    }
    return true
}
```

## 3. 相关规则
- 遵循 [coding-rules.mdc](mdc:.cursor/rules/CommonRules/coding-rules.mdc) 中的命名规范
- 配合 [gen-swift.mdc](mdc:.cursor/rules/CommonRules/Generate/gen-swift.mdc) 和 [gen-oc.mdc](mdc:.cursor/rules/CommonRules/Generate/gen-oc.mdc) 的代码生成规范
