---
description: 
globs: 
alwaysApply: false
---

# 项目结构
SoulApp/
├── Soul_New/ - 主应用程序代码
│   ├── Resource/ - 资源文件
│   ├── Resources/ - 资源文件
│   ├── LeveledResources/ - 分级资源
│   ├── LocalizeFiles/ - 本地化文件
│   └── AppDelegate.{h,m} - 应用入口
├── SOPods/ - Git Submodule方式引入的私有库
├── LocalPods/ - 本地私有库
├── Pods/ - CocoaPods管理的第三方库
├── SoulShareExtension/ - 分享扩展
├── SoulWidget/ - 小组件
├── IntentHandler/ - Siri意图处理
├── SoulPushExtension/ - 推送服务扩展
├── assets/ - 资源文件
├── Resources/ - 资源文件
├── Podfile - CocoaPods依赖配置
├── devops - 库版本管理
└── swiftlint.yml - Swift代码规范配置


# 项目介绍
1. 这是一个 iOS 的项目
1. 项目使用了原生、Flutter、Reactnative、H5等技术，其中原生使用了 Swift、OC 语言开发。
2. 优先使用 Swift 语言生成代码，如果当前文件里面已经是 OC 代码，那么优先使用 OC 代码生成。
3. `cocoapods-soul-component-plugin` 是自定义插件，会自动关联 @Podfile 里面库和 [component.json](mdc:devops/component.json) 和 [component_user.json](mdc:devops/component_user.json) 里面的版本。优先使用 [component_user.json](mdc:devops/component_user.json) 里面的版本。
4. 文件夹[component.json](mdc:devops/component.json) LocalPods 里面同一个库可能有多个版本，要注意 [component.json](mdc:devops/component.json) 和 [component_user.json](mdc:devops/component_user.json) 里面的版本。对应的关系是：如果字段 `local`为false，代表使用的是Framework库，否则使用的是源码。如果`local`为true，同时有字段`version`，那么对应的库的文件夹是 `{key}-{version}`;如果`local`为true，同时有字段`branch`，那么对应的库的文件夹是 `{key}-{branch}`,注意如果字段`branch`里面有`/`替换成`-`。比如下面这个配置，对应的文件夹是 `SOVideoPlayer-soul-test`。
```
"SOVideoPlayer":{
	"git":"***********************:soul_ios_sdk/sovideoplayer.git",
	"submodule":false,
	"branch":"soul/test",
	"local":true
}
```
5. 生成的代码规范，需要符合 [coding-rules.mdc](mdc:.cursor/rules/CommonRules/coding-rules.mdc)

# 库介绍
下面枚举了目前核心的库，生成代码的时候，需要优先考虑下面库的提供的方法。
1. 基础 UI 库介绍： [baseui-library.mdc](mdc:.cursor/rules/CommonRules/Libary/baseui-library.mdc) 
2. 通用 Kit 库介绍： [kit-library.mdc](mdc:.cursor/rules/CommonRules/Libary/kit-library.mdc)
3. 网络相关库介绍： [network-library.mdc](mdc:.cursor/rules/CommonRules/Libary/network-library.mdc)
4. IM 相关库介绍： [im-library.mdc](mdc:.cursor/rules/CommonRules/Libary/im-library.mdc)
5. Flutter 库介绍： [flutter-library.mdc](mdc:.cursor/rules/CommonRules/Libary/flutter-library.mdc)
6. React Native 库介绍： [reactnative-library.mdc](mdc:.cursor/rules/CommonRules/Libary/reactnative-library.mdc)
7. RTC 相关库介绍： [rtc-library.mdc](mdc:.cursor/rules/CommonRules/Libary/rtc-library.mdc)
7. 基础逻辑相关库介绍： [other-library.mdc](mdc:.cursor/rules/CommonRules/Libary/other-library.mdc)


