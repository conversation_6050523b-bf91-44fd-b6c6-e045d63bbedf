//
//  IntentHandler.swift
//  IntentHandler
//
//  Created by curry.zhang on 2025/3/10.
//  Copyright © 2025 Soul. All rights reserved.
//

import Intents

class IntentHandler: INExtension, SelectCharacterIntentHandling {
    
    let appGroupIdentifier = "group.com.soul.cn"

    func provideCharacterOptionsCollection(for intent: SelectCharacterIntent, with completion: @escaping (INObjectCollection<AICharacter>?, (any Error)?) -> Void) {
        // 读取数据
        if let userDefaults = UserDefaults(suiteName: appGroupIdentifier),
           let jsonData = userDefaults.data(forKey: "HerWorldWidget_AvatarsJsonData"),
           let array = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [NSDictionary] {
            // 将 NSDictionary 转换成 AICharacter
            let characters: [AICharacter] = array.compactMap { dict in
                guard let aiUserId = dict["aiUserId"] as? Int,
                      let signature = dict["signature"] as? String else {
                    return nil
                }
                return AICharacter(identifier: "\(aiUserId)", display: signature)
            }
            completion(INObjectCollection(items: characters), nil)
            return
        }
        // 若解析失败，返回空数组
        completion(INObjectCollection(items: []), nil)
    }

    func handle(intent: SelectCharacterIntent, completion: @escaping (SelectCharacterIntentResponse) -> Void) {
        guard let _ = intent.character else {
            completion(SelectCharacterIntentResponse(code: .failure, userActivity: nil))
            return
        }
        // 返回成功
        completion(SelectCharacterIntentResponse(code: .success, userActivity: nil))
    }
}
