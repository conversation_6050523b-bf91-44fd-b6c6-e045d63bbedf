import os
import random
import string

def generate_random_string(length):
    """生成指定长度的随机字符串"""
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))

def create_random_file(folder_path, file_name, file_size):
    """在指定文件夹中创建一个指定大小的随机文件"""
    file_path = os.path.join(folder_path, file_name)
    random_content = generate_random_string(file_size)

    with open(file_path, 'w') as file:
        file.write(random_content)

    print(f'随机文件已创建：{file_path}')

def traverse_and_create_random_file(root_folder, file_size):
    """遍历文件夹并在每个文件夹中创建一个随机文件"""
    for item in os.listdir(root_folder):
        item_path = os.path.join(root_folder, item)

        if os.path.isdir(item_path):
            random_filename = f'random_file_{generate_random_string(8)}.txt'
            create_random_file(item_path, random_filename, file_size)

# 设置根文件夹和随机文件的大小
root_folder_path = '/Users/<USER>/SoulProject/SoulApp3/LocalPods'
root_folder_path_2 = '/Users/<USER>/SoulProject/SoulApp3/SOPods'

random_file_size = 8  # 文件大小，以字节为单位

# 执行遍历和创建随机文件
traverse_and_create_random_file(root_folder_path, random_file_size)
traverse_and_create_random_file(root_folder_path_2, random_file_size)
