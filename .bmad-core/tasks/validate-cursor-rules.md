# validate-cursor-rules

## Purpose
Validate code compliance against cursor rules for iOS development standards, naming conventions, and library usage patterns.

## Input
- File path or directory to validate
- Specific rule categories to check (optional)

## Process
1. **Load Cursor Rules Configuration**
   - Read cursorRulesIntegration settings from core-config.yml
   - Load all coreRules and libraryRules specified in configuration
   - Parse rule metadata (globs, alwaysApply flags)

2. **Code Analysis**
   - Scan specified files for iOS code patterns
   - Check naming conventions (camelCase, PascalCase usage)
   - Validate library usage against approved patterns
   - Verify file structure and organization

3. **Rule Compliance Check**
   - Apply coding-rules.mdc standards
   - Check against generation rules (gen-base.mdc, gen-oc.mdc)
   - Validate library usage patterns
   - Identify violations and non-compliant patterns

4. **Generate Report**
   - List all violations with file locations and line numbers
   - Provide specific rule references for each violation
   - Suggest corrections based on cursor rules
   - Rate overall compliance score

## Output Format
```markdown
# Cursor Rules Validation Report

## Summary
- Files Checked: {count}
- Violations Found: {count}
- Compliance Score: {percentage}%

## Violations
### {Rule Category}
- **File**: {file_path}:{line_number}
- **Rule**: {rule_name}
- **Issue**: {description}
- **Suggestion**: {correction}

## Library Usage Issues
- **Library**: {library_name}
- **Issue**: {description}
- **Recommended**: {proper_usage}

## Recommendations
1. {recommendation_1}
2. {recommendation_2}
```

## Integration Points
- Accessible via `*check-rules` command in dev agent
- Can be invoked during task-execution flow before marking [x] complete
- Integrates with existing quality gate discipline
- Links to execute-checklist task for comprehensive validation

## Dependencies
- Core configuration file with cursorRulesIntegration settings
- Access to .cursor/rules directory structure
- File system access for code analysis