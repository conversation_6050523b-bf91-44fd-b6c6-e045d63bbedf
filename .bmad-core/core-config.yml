version: 4.21.1
markdownExploder: true
prd:
  prdFile: docs/prd.md
  prdVersion: v4
  prdSharded: true
  prdShardedLocation: docs/prd
  epicFilePattern: epic-{n}*.md
architecture:
  architectureFile: docs/architecture.md
  architectureVersion: v4
  architectureSharded: true
  architectureShardedLocation: docs/architecture
customTechnicalDocuments: null
devLoadAlwaysFiles:
  - docs/architecture/coding-standards.md
  - docs/architecture/tech-stack.md
  - docs/architecture/source-tree.md
  - .cursor/rules/CommonRules/coding-rules.mdc
cursorRulesIntegration:
  enabled: true
  rulesPath: .cursor/rules
  coreRules:
    - CommonRules/coding-rules.mdc
    - CommonRules/Generate/gen-base.mdc 
    - CommonRules/Generate/gen-oc.mdc
  libraryRules:
    - CommonRules/Libary/baseui-library.mdc
    - CommonRules/Libary/network-library.mdc
    - CommonRules/Libary/kit-library.mdc
devDebugLog: .ai/debug-log.md
devStoryLocation: docs/stories
