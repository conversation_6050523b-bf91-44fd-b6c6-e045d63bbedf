workflow:
  id: brownfield-so-ui
  name: Soul iOS UI Implementation from Figma
  description: >-
    Agent workflow for implementing iOS UI components from Figma designs in the Soul App project.
    Uses Figma MCP integration for design extraction and visual comparison to ensure high fidelity implementation.
  type: brownfield
  project_types:
    - ios-ui-implementation
    - figma-to-code
    - ui-component-creation
    - visual-design-matching

  # 配置参数
  configuration:
    # 视觉相似度配置
    similarity_threshold: 0.9
    similarity_threshold_fallback: 0.8
    
    # 迭代优化配置
    max_iterations: 1
    
    # 工作目录配置 - 关键优化点
    project_root: "FigmaDemo"  # 项目根目录
    
    # 输出路径配置
    output_paths:
      figma_url_info: "FigmaDemo/docs/figma_url_info.md"
      design_spec: "FigmaDemo/docs/figma_design_spec.md" 
      figma_raw_data: "FigmaDemo/docs/figma_raw_data.json"
      hierarchy_parsed_data: "FigmaDemo/docs/figma_hierarchy_parsed.json"
      comparison_report: "FigmaDemo/docs/comparison_report.md"
      components: "FigmaDemo/FigmaDemo/Components"
      docs_directory: "FigmaDemo/docs/"
      branch_name_file: "FigmaDemo/docs/current_branch.txt"
    
    # 脚本路径配置 - 验证路径存在
    script_paths:
      demo_setup: "scripts/figma/demo_down.sh"
      demo_push: "scripts/figma/demo_push.sh"
      figma_node_getter: "FigmaDemo/scripts/get_figma_node.sh"
      figma_hierarchy_parser: "FigmaDemo/scripts/figma_hierarchy_parser.py"
    
    # 项目特定配置
    project_settings:
      main_controller: "ViewController"
      xcode_project: "FigmaDemo/FigmaDemo.xcworkspace"
    
    # Figma API 配置
    figma_api:
      mcp_config_path: "~/.cursor/mcp.json"  # MCP 配置文件路径
      mcp_server_name: "Figma MCP"           # MCP 服务器名称
      api_key_field: "x-figma-token"         # API Key 字段名
      
    # 质量标准配置
    quality_thresholds:
      compile_errors: 0
      compile_warnings: 0
      
    # 设计文档质量标准配置
    documentation_design_standards:
        purpose: "完整的可交付设计文档"
        required_analysis:
          - "statistical_overview"      # 节点统计信息
          - "complete_hierarchy"        # 完整层级分析
          - "complete_text_mapping"     # 详细文案映射
          - "exhaustive_color_mapping"  # 完整颜色映射
          - "detailed_font_system"      # 详细字体系统
          - "component_specifications"  # 组件规范
          - "technical_implementation"  # 技术实现指导
          - "code_examples"            # 代码示例
        data_utilization: "comprehensive"
        time_constraint: "none"
      
  # iOS 开发规范
  ios_development_rules:
    language_preference:
      primary: "Objective-C/UIKit"
      fallback: "Swift/SwiftUI"
      guideline: "如果没有强制要求，优先使用 Objective-C/UIKit 生成代码"
    
    code_organization:
      - "UI 布局约束：Objective-C 项目使用 Masonry，Swift 项目使用 SnapKit"
      - "UI 组件通过创建独立新文件生成，便于后续代码移植。"
      - "如果创建的是页面，继承于 SoulBaseViewController #import <SoulBaseUI/SoulBaseViewController.h>；如果创建的是组件，继承于 UIView。"
      - "在当前项目 {{configuration.project_settings.main_controller}} 上集成新组件，方便验证效果"
      - "如果 Figma 节点 Colors 设置的是 D/S{number} 代表生成代码时候使用 SOColor：比如 D/S1 或者 D/S01 对应代码 SOColor(0)。同时 SOColor 是定义在 #import <SODarkMode/SOColorDefine.h>。"
      - "如果使用了 SOPingfangLightFont/SOPingfangFont/SOPingfangBoldFont/SOPingfangMediumFont 的定义，需要引入头文件 #import <SOAlertKit/SOAlertMacros.h>"
      - "生成的对象的名称不能使用 new 开头，比如你可以定义为 momentLabel，但是一定不能定义为 newMomentLabel。原因是 new 开头的对象在 Objective-C 中会编译失败。"
      - "生成的最顶部位置的组件的约束通过 mas_safeAreaLayoutGuideTop 设置，比如：make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop);“
      - "遵循 Soul App 编码规范，包括颜色、字体、网络请求等标准。"
  
    ui_implementation:
      - "状态栏和底部安全区域 UI 无需实现"
      - "图片、RECTANGLE类型优先使用系统 Icon 或空图片占位，避免自定义绘制"
      - "忽略图片的具体内容修改，因为最终会从远端下载"
      - "代码结构必须与 get_figma_data 工具返回的结构保持一致"      
      - "代码实现与 Figma 设计效果在结构上不能有重大差异"

  sequence:
    - step: demo_project_setup
      agent: developer
      action: "通过脚本下载并设置 Git Demo 项目环境"
      creates: 
        - "{{configuration.project_root}}/项目目录"
        - "{{configuration.output_paths.branch_name_file}}"
      script_command: "{{configuration.script_paths.demo_setup}}"
      post_setup_commands:
        - "cd {{configuration.project_root}} && git branch --show-current > docs/current_branch.txt"
        - "echo '✅ 分支名称已记录到: {{configuration.output_paths.branch_name_file}}'"
      validation_commands:
        - "test -d {{configuration.project_root}}/FigmaDemo"
        - "test -f {{configuration.project_root}}/Podfile"
        - "test -f {{configuration.output_paths.branch_name_file}}"
      notes:
        - "**执行脚本**: {{configuration.script_paths.demo_setup}}"
        - "**工作目录**: Soul App 项目根目录（包含 Soul_New.xcworkspace）"
        - "**脚本功能**："
        - "1. 下载 Git Demo 项目到 {{configuration.project_root}}"
        - "2. 初始化项目环境和依赖"
        - "3. 配置必要的构建设置"
        - "4. 确保项目可正常编译运行"
        - "5. 记录当前分支名称到 {{configuration.output_paths.branch_name_file}}"
        - "**验证**: 确认 {{configuration.project_root}} 项目目录创建成功且可编译"
        - "**输出**: 完整的 Demo 项目环境，后续 UI 组件生成都必须基于这个项目"

    - step: figma_data_extraction
      agent: developer
      action: 
        - "使用 {{configuration.script_paths.figma_node_getter}} 获取 Figma 数据"
        - "记录 Figma URL 信息到文档文件中，供 LLM 直接访问"
      creates: 
        - "{{configuration.output_paths.figma_raw_data}}"
        - "{{configuration.output_paths.figma_url_info}}"
      pre_execution_commands:
        - "chmod +x {{configuration.script_paths.figma_node_getter}}"
      validation_commands:
        - "test -f {{configuration.output_paths.figma_raw_data}} && echo '✅ Figma 数据文件已创建'"
        - "test -f {{configuration.output_paths.figma_url_info}} && echo '✅ Figma URL 信息已记录'"
      notes:
        - "**工作目录**: Soul App 项目根目录（包含 Soul_New.xcworkspace）"
        - "**功能 1**: 使用 {{configuration.output_paths.figma_url_info}} 存储 Figma 链接
        - "**功能 2**: 使用 {{configuration.script_paths.figma_node_getter}} 获取 Figma 数据"
        - "**示例命令**: sh {{configuration.script_paths.figma_node_getter}} API_KEY FILE_KEY NODE_ID OUT_PUT_FILE"
        - "**参数说明**："
        - "- API_KEY: 从 {{configuration.figma_api.mcp_config_path}} 中解析获取"
        - "  路径: mcpServers['{{configuration.figma_api.mcp_server_name}}'].headers['{{configuration.figma_api.api_key_field}}']"
        - "  示例: figd_XNaicAoxMO0OdFqqFlF1nrd6aVytMhOBwow77iOZ"
        - "- FILE_KEY: 从 Figma URL 中提取 (如: 95Ut7CUFupDuT23Zo8fEpL)"
        - "- NODE_ID: 从 Figma URL 中提取 (如: 1-2)"
        - "- OUT_PUT_FILE: 输出到 {{configuration.output_paths.figma_raw_data}}"

    - step: script_parsing
      agent: developer
      action: "使用专用脚本准确解析 Figma 数据的层级关系"
      creates: "{{configuration.output_paths.hierarchy_parsed_data}}"
      requires: "{{configuration.output_paths.figma_raw_data}}"
      pre_execution_commands:
        - "test -f {{configuration.output_paths.figma_raw_data}} || echo '❌ 输入文件不存在，请先执行 figma_data_extraction'"
      script_command: "cd {{configuration.project_root}} && python3 scripts/figma_hierarchy_parser.py docs/figma_raw_data.json docs/figma_hierarchy_parsed.json"
      validation_commands:
        - "test -f {{configuration.output_paths.hierarchy_parsed_data}} && echo '✅ 层级解析文件已创建' "
      notes:
        - "**工作目录**: Soul App 项目根目录（包含 Soul_New.xcworkspace）"
        - "**使用脚本**: {{configuration.script_paths.figma_hierarchy_parser}}"
        - "**执行命令**: python3 {{configuration.script_paths.figma_hierarchy_parser}} {{configuration.output_paths.figma_raw_data}} {{configuration.output_paths.hierarchy_parsed_data}}"

    - step: structure_analysis
      agent: designer  
      action: "基于图片链接和脚本解析结果生成详细的设计规范文档"
      creates: "{{configuration.output_paths.design_spec}}"
      requires: 
        - "{{configuration.documentation_design_standards}}"
        - "{{configuration.output_paths.figma_url_info}}"
        - "{{configuration.output_paths.hierarchy_parsed_data}}"
        - "{{configuration.output_paths.figma_raw_data}}"
      notes:
        - "**工作目录**: Soul App 项目根目录（包含 Soul_New.xcworkspace）"
        - "**目标**: 可以多花费些时间生成详细的基础设计规范，确保后续的 ui_code_generation 步骤可以顺利进行"
        - "**生成规则**: 必须遵循 {{configuration.documentation_design_standards}} 中的要求，尽可能详细"
        - "**数据来源**: 使用截图链接 {{configuration.output_paths.figma_url_info}} 和页面层级结构 {{configuration.output_paths.hierarchy_parsed_data}} 和原始数据 {{configuration.output_paths.figma_raw_data}} 作为生成设计规范的上下文"
        - "**重要**: 不要使用 MCP 的 get_figma_data 工具生成设计规范，除非用户强制要求"
        
    - step: ui_code_generation
      agent: developer
      action: "根据验证通过的设计文档生成 iOS UI 组件代码"
      creates: "{{configuration.output_paths.components}}/*.m/*.h 或 *.swift 文件"
      requires: 
        - "{{configuration.output_paths.design_spec}}"
        - "{{configuration.output_paths.hierarchy_parsed_data}}"
        - "{{configuration.output_paths.figma_url_info}}"
      notes: 
        - "**工作目录**: Soul App 项目根目录（包含 Soul_New.xcworkspace）"
        - "**重要**: 使用设计文档 {{configuration.output_paths.design_spec}}，截图链接 {{configuration.output_paths.figma_url_info}} 和页面层级结构 {{configuration.output_paths.hierarchy_parsed_data}} 作为上下文，一起提交给 llm 生成代码"
        - "严格遵循 ios_development_rules.code_organization 编码规范。"
        - "严格遵循 ios_development_rules.ui_implementation 实现要求。"
        - "优先使用 Objective-C/UIKit 生成独立的 UI 组件文件，生成在 {{configuration.output_paths.components}} 文件夹里面。"
        - "生成的组件需要在 {{configuration.project_settings.main_controller}} 中集成，便于后续验证。"
        - "**代码结构必须严格反映脚本解析的准确层级关系数据**"
        - "使用命令 xcodebuild -workspace FigmaDemo/FigmaDemo.xcworkspace -scheme FigmaDemo -configuration Debug -sdk iphonesimulator -derivedDataPath FigmaDemo/build 编译验证代码，直到编译成功"

    - step: visual_validation
      agent: qa
      action: "执行视觉对比验证，视觉对比最多一次"
      creates: "{{configuration.output_paths.comparison_report}}"
      requires: "{{configuration.output_paths.components}} 代码文件"
      tools_required:
        - run_terminal_cmd
        - read_file
        - compare_ui_with_figma
      pre_execution_commands:
        - "BRANCH_NAME=$(cat {{configuration.output_paths.branch_name_file}} | tr -d '\\n')"
      script_command: "BRANCH_NAME=$(cat {{configuration.output_paths.branch_name_file}} | tr -d '\\n') && sh {{configuration.script_paths.demo_push}} $BRANCH_NAME"
      validation_commands:
        - "test -f {{configuration.output_paths.comparison_report}}"
        - "echo '✅ 视觉对比报告已生成'"
      notes: 
        - "**工作目录**: Soul App 项目根目录（包含 Soul_New.xcworkspace）"
        - "提交代码到远端分支并进行视觉对比验证"
        - "目标相似度: >= {{configuration.similarity_threshold}}"

    - step: iterative_optimization
      agent: developer
      condition: "similarity < {{configuration.similarity_threshold}}，只对比一次"
      action: "严格按照 MCP recommendations 进行代码优化"
      requires: "{{configuration.output_paths.comparison_report}}"
      max_iterations: {{configuration.max_iterations}}
      process:
        1. "分析 MCP 返回的 recommendations"
        2. "生成具体修改建议的 TodoList"
        3. "逐项执行代码修改"
        4. "重新执行视觉对比验证"
        5. "更新 TodoList 状态"
        6. "直接进入 ios_standards_validation 流程"

    - step: ios_standards_validation
      agent: qa
      action: "验证代码是否严格遵循 iOS 开发规范和实现要求"
      creates: "{{configuration.output_paths.docs_directory}}ios_standards_validation_report.md"
      requires: 
        - "{{configuration.output_paths.components}} 代码文件"
        - "{{configuration.output_paths.hierarchy_parsed_data}}"
        - "{{configuration.output_paths.design_spec}}"
      validation_categories:
        code_organization_compliance:
          - "验证是否正确使用 Masonry 或者 SnapKit 进行 UI 布局约束"
          - "验证 UI 组件是否通过独立新文件生成，便于代码移植"
          - "验证页面是否继承于 SoulBaseViewController，组件是否继承于 UIView"
          - "验证是否在 {{configuration.project_settings.main_controller}} 中正确集成新组件"
          - "验证 SOColor 颜色系统使用：D/S{number} 对应 SOColor(number)，导入 <SODarkMode/SOColorDefine.h>"
          - "验证对象命名规范：确保没有使用 'new' 开头的对象名（避免 Objective-C 编译错误）"
          - "生成的最顶部位置的组件的约束通过 mas_safeAreaLayoutGuideTop 设置，比如：make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop);“
          - "验证是否遵循 Soul App 编码规范（颜色、字体、网络请求等标准）"
        ui_implementation_compliance:
          - "验证状态栏和底部安全区域 UI 是否按规范无需实现或正确隐藏"
          - "验证图片、RECTANGLE类型是否优先使用系统 Icon 或空图片占位"
          - "验证是否忽略图片具体内容修改（因最终从远端下载）"
          - "验证代码实现与 Figma 截图链接在结构上是否无重大差异"
        code_quality_checks:
          - "检查编译状态：0 错误，0 警告"
          - "检查内存管理：无循环引用"
          - "检查约束完整性：Masonry 或者 SnapKit 约束是否完整无冲突"
          - "检查组件独立性：UI 组件是否可独立复用"
      tools_required:
        - read_file
        - grep
        - codebase_search
      validation_process:
        1. "读取并分析生成的 iOS 代码文件结构和内容"
        2. "对比 ios_development_rules.code_organization 中每一项规范"
        3. "对比 ios_development_rules.ui_implementation 中每一项要求"
        4. "检查代码编译状态和质量指标"
        5. "生成详细的规范遵循情况报告"
        6. "识别不符合规范的代码并提供具体修改建议"
        7. "使用命令 xcodebuild -workspace FigmaDemo/FigmaDemo.xcworkspace -scheme FigmaDemo -configuration Debug -sdk iphonesimulator -derivedDataPath FigmaDemo/build 编译验证代码，直到编译成功"
      pass_criteria:
        - "code_organization_compliance: 通过"
        - "ui_implementation_compliance: 通过"
        - "code_quality_checks: 通过"
        - "编译成功，无错误无警告"
      notes: 
        - "这是确保代码质量和规范性的关键验证步骤"
        - "必须在迭代优化前完成，避免后续修改破坏规范性"
        - "如有不符合项，需要生成具体的修复 TodoList"
        - "通过此验证后，代码才能进入迭代优化阶段"

    - step: final_validation
      agent: qa
      action: "全面质量验证和交付物生成"
      validates: "all_artifacts"
      validation_checklist:
        structure_integrity:
          - "再次验证代码结构与原始 Figma 数据的一致性"
          - "确认没有层级关系错误"
        code_quality:
          - "使用命令 xcodebuild -workspace FigmaDemo/FigmaDemo.xcworkspace -scheme FigmaDemo -configuration Debug -sdk iphonesimulator -derivedDataPath FigmaDemo/build"
          - "代码编译无错误和警告"
          - "遵循 Soul App 编码规范和架构模式"
        documentation:
          - "设计规格文档完整且结构准确"
          - "验证组件规范的详细程度"
          - "使用示例和集成指南完整"
      notes: "执行最终代码检查，生成完整的实现文档和使用示例。确保所有交付物符合质量标准，特别是结构准确性和文档完整性。"

    - workflow_end:
      primary_condition: "代码编译通过"
      script_command: "BRANCH_NAME=$(cat {{configuration.output_paths.branch_name_file}} | tr -d '\\n') && sh {{configuration.script_paths.demo_push}} $BRANCH_NAME"
      secondary_conditions:
        - "UI 组件在模拟器中正常显示"
        - "所有 TodoList 项目已完成"
        - "最终验证检查通过"
      action: implementation_complete
      notes: |
        🎉 UI 实现完成！相似度值已达到 {{configuration.similarity_threshold}} 以上。
        
        📦 标准交付物：
        1. iOS UI 组件代码文件 (Objective-C/Swift)
        2. Figma 原始数据 ({{configuration.output_paths.figma_raw_data}})
        3. 脚本解析的层级数据 ({{configuration.output_paths.hierarchy_parsed_data}})
        4. 详细设计规格文档 ({{configuration.output_paths.design_spec}})
        5. 视觉对比报告 ({{configuration.output_paths.comparison_report}})
        6. 实现过程记录 (TodoList 历史)
        7. 组件使用示例和集成指南
        8. Figma URL 信息文档 ({{configuration.output_paths.figma_url_info}})
        
        ✅ 质量验证通过：
        - 代码质量符合 Soul App 规范
        - 组件功能完整可用
        - 详细文档支持代码生成、独立交付使用

  flow_diagram: |
    ```mermaid
    graph TD
        A[开始: 提供参数] --> B[developer: 获取 Figma 数据]
        B --> C[developer: 脚本解析层级关系]
        C --> D[designer: 基于脚本结果生成设计规格]
        D --> E[developer: 生成 iOS UI 代码]
        E --> F[qa: 视觉对比验证]
        F --> G{相似度 >= 阈值?}
        G -->|否| H[developer: 迭代优化]
        H --> I{达到最大迭代次数?}
        I -->|否| F
        I -->|是| J[用户确认结束]
        G -->|是| K[qa: 最终验证]
        J --> K
        K --> L[完成: UI 实现达标]

        style L fill:#90EE90
        style B fill:#87CEEB
        style C fill:#87CEEB
        style D fill:#FFE4B5
        style E fill:#FFE4B5
        style F fill:#ADD8E6
        style H fill:#FFA07A
        style K fill:#E6E6FA
    ```

  # 执行指导 - 解决路径和目录问题
  execution_guidance:
    directory_management:
      working_directory_setup:
        - "工作目录基于 Soul App 项目根目录（包含 Soul_New.xcworkspace 的目录）"
        - "所有路径配置均基于项目根目录的相对路径"
        - "执行命令前确保在正确的项目根目录中"
        - "使用 `pwd` 验证当前目录，应该包含 FigmaDemo/ 子目录"
      
      path_verification:
        - "执行步骤前验证关键路径存在性："
        - "  - 项目根目录标识: test -f Soul_New.xcworkspace/contents.xcworkspacedata"
        - "  - FigmaDemo 目录: test -d {{configuration.project_root}}"
        - "  - 文档目录: test -d {{configuration.output_paths.docs_directory}}"
        - "  - 脚本文件: test -f {{configuration.script_paths.figma_hierarchy_parser}}"
        - "如路径不存在，立即报错并停止执行"
      
      file_management:
        - "创建文件前确保目录存在: mkdir -p $(dirname path)"
        - "写入大文件时使用 write 工具而非 echo 重定向"
        - "验证文件创建成功: test -f file_path"
        - "JSON文件验证格式: python3 -c \"import json; json.load(open('file'))\""
    
    environment_detection:
      project_root_detection:
        - "自动检测项目根目录的方法："
        - "1. 查找包含 Soul_New.xcworkspace 的目录"
        - "2. 确认该目录包含 FigmaDemo/ 子目录"
        - "3. 验证目录结构: ls -la | grep -E '(Soul_New|FigmaDemo)'"
    
    common_issues_solutions:
      directory_not_found:
        problem: "cd: no such file or directory"
        solution: "确保在 Soul App 项目根目录中执行（包含 Soul_New.xcworkspace）"
        command: "pwd && ls -la | grep Soul_New"
      
      script_not_found:
        problem: "脚本文件不存在"
        solution: "验证脚本路径相对于项目根目录"
        command: "test -f {{configuration.script_paths.figma_hierarchy_parser}}"
      
      branch_name_missing:
        problem: "分支名称未记录"
        solution: "确保 demo_project_setup 步骤完成并记录分支"
        command: "cat {{configuration.output_paths.branch_name_file}}"

  # 使用指导
  usage_guidance:
    prerequisites:
      - "确保 {{configuration.figma_api.mcp_config_path}} 文件存在且包含 Figma API Key"
      - "确保 get_figma_node.sh 脚本存在且可执行"
      - "安装 Python3 和 jq 工具"
    
    required_parameters:
      - "API_KEY: jq -r '.mcpServers[\"{{configuration.figma_api.mcp_server_name}}\"].headers[\"{{configuration.figma_api.api_key_field}}\"]' {{configuration.figma_api.mcp_config_path}}"
      - "FILE_KEY: 从 Figma URL 中提取 (如: 95Ut7CUFupDuT23Zo8fEpL)"
      - "NODE_ID: 从 Figma URL 中提取 (如: 1-2)"
    
    workflow_steps:
      - "1. demo_project_setup: 设置 Demo 项目环境"
      - "2. figma_data_extraction: 获取 Figma 数据"
      - "3. script_parsing: 解析层级关系"
      - "4. structure_analysis: 生成设计规格"
      - "5. ui_code_generation: 生成 iOS 代码"
      - "6. visual_validation: 视觉对比验证"

  summary:
    description: "从 Figma 设计稿实现高保真 iOS UI 组件的完整工作流程"
    output_files:
      - "figma_raw_data.json: 原始 Figma 数据"
      - "figma_hierarchy_parsed.json: 解析的层级数据"
      - "figma_design_spec.md: 设计规格文档"
      - "figma_url_info.md: Figma URL 信息，供 LLM 直接访问"
      - "iOS 组件代码文件 (.m/.h 或 .swift)"