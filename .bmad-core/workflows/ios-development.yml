# iOS 原生开发工作流配置
# 专为 iOS 原生开发项目设计，集成 Cursor 规则验证，确保代码质量和开发标准一致性
workflow:
  id: ios-development
  name: iOS Native Development with Cursor Rules  # iOS 原生开发工作流（集成 Cursor 规则）
  description: >-
    Specialized workflow for iOS native development projects with integrated cursor rules
    compliance, ensuring code quality and consistency with established iOS development standards.
    # 专为 iOS 原生开发项目设计的专业工作流，集成 Cursor 规则合规性检查，
    # 确保代码质量和既定 iOS 开发标准的一致性
  type: ios-native  # 工作流类型：iOS 原生
  project_types:    # 适用的项目类型
    - ios-native-app      # iOS 原生应用
    - objective-c-module  # Objective-C 模块
    - swift-component     # Swift 组件
    - ios-framework       # iOS 框架
    - ios-library         # iOS 库

  sequence:  # 工作流执行序列
    # 步骤1：规则初始化
    - step: rules_initialization
      agent: bmad-master  # 执行代理：主控代理
      action: load cursor rules configuration and validate environment  # 加载 Cursor 规则配置并验证环境
      creates: cursor rules validation report  # 生成：Cursor 规则验证报告
      notes: "Initialize cursor rules integration, validate all rule files are accessible and properly configured."
      # 初始化 Cursor 规则集成，验证所有规则文件可访问且配置正确

    # 步骤2：项目分析
    - step: project_analysis
      agent: architect  # 执行代理：架构师
      action: analyze existing iOS project structure and use task document-project  # 分析现有 iOS 项目结构并使用文档化项目任务
      creates: project documentation with cursor rules compliance assessment  # 生成：包含 Cursor 规则合规评估的项目文档
      notes: "Review existing iOS codebase, identify cursor rules violations, and assess modernization needs."
      # 审查现有 iOS 代码库，识别 Cursor 规则违规并评估现代化需求

    # 步骤3：产品需求文档创建
    - agent: pm  # 执行代理：产品经理
      creates: prd.md  # 生成：产品需求文档
      uses: brownfield-prd-tmpl  # 使用：棕地项目 PRD 模板
      requires:   # 依赖项
        - project_analysis        # 项目分析结果
        - cursor_rules_config     # Cursor 规则配置
      notes: "Creates PRD with iOS-specific requirements and cursor rules compliance goals. SAVE OUTPUT: Copy final prd.md to your project's docs/ folder."
      # 创建包含 iOS 特定需求和 Cursor 规则合规目标的 PRD。保存输出：将最终的 prd.md 复制到项目的 docs/ 文件夹

    # 步骤4：架构设计
    - agent: architect  # 执行代理：架构师
      creates: architecture.md  # 生成：架构文档
      uses: brownfield-architecture-tmpl  # 使用：棕地项目架构模板
      requires:  # 依赖项
        - prd.md                            # 产品需求文档
        - cursor_rules_compliance_report    # Cursor 规则合规报告
      notes: "Creates iOS architecture with cursor rules integration, library usage patterns, and code organization standards. SAVE OUTPUT: Copy final architecture.md to your project's docs/ folder."
      # 创建集成 Cursor 规则的 iOS 架构，包含库使用模式和代码组织标准。保存输出：将最终的 architecture.md 复制到项目的 docs/ 文件夹

    # 步骤5：Cursor 规则验证
    - step: cursor_rules_validation
      agent: dev  # 执行代理：开发者
      action: validate cursor rules compliance for project  # 验证项目的 Cursor 规则合规性
      uses: validate-cursor-rules  # 使用：Cursor 规则验证任务
      requires: architecture.md  # 依赖：架构文档
      notes: "Comprehensive cursor rules validation to establish baseline compliance and identify areas for improvement."
      # 全面的 Cursor 规则验证，建立基线合规性并识别改进领域

    # 步骤6：产品负责人验证
    - agent: po  # 执行代理：产品负责人
      validates: all_artifacts_with_cursor_rules  # 验证：所有包含 Cursor 规则的工件
      uses:   # 使用的检查清单
        - po-master-checklist     # PO 主检查清单
      notes: "Validates all documents for iOS best practices and cursor rules compliance. May require updates to any document."
      # 验证所有文档的 iOS 最佳实践和 Cursor 规则合规性。可能需要更新任何文档

    # 步骤7：文档更新（条件性）
    - agent: various  # 执行代理：多个代理
      updates: any_flagged_documents  # 更新：被标记的文档
      condition: po_checklist_issues  # 条件：PO 检查清单发现问题
      notes: "If PO finds cursor rules violations or iOS best practice issues, return to relevant agent to fix and re-export updated documents to docs/ folder."
      # 如果 PO 发现 Cursor 规则违规或 iOS 最佳实践问题，返回相关代理修复并重新导出更新的文档到 docs/ 文件夹

    # 工作流结束：转入 iOS 开发循环
    - workflow_end:
      action: move_to_ios_development  # 行动：转入 iOS 开发
      notes: |
        iOS Development Planning Complete! Transition to iOS Development Cycle:
        # iOS 开发规划完成！转入 iOS 开发循环：
        
        1. ENSURE DOCUMENTS AND RULES ARE IN PROJECT:
        # 1. 确保文档和规则在项目中：
           - Copy final prd.md to project's docs/prd.md
           # 将最终的 prd.md 复制到项目的 docs/prd.md
           - Copy final architecture.md to project's docs/architecture.md
           # 将最终的 architecture.md 复制到项目的 docs/architecture.md
           - Verify cursor rules are properly configured in .cursor/rules/
           # 验证 Cursor 规则在 .cursor/rules/ 中已正确配置
           - Confirm bmad core-config.yml has cursorRulesIntegration enabled
           # 确认 bmad core-config.yml 已启用 cursorRulesIntegration
        
        2. SHARD DOCUMENTS (in IDE):
        # 2. 文档分片（在 IDE 中）：
           - Use PO agent: @po then ask to shard docs/prd.md and docs/architecture.md
           # 使用 PO 代理：@po 然后要求分片 docs/prd.md 和 docs/architecture.md
           - This creates docs/prd/ and docs/architecture/ folders with sharded content
           # 这将创建包含分片内容的 docs/prd/ 和 docs/architecture/ 文件夹
           - Ensures cursor rules context is maintained in sharded documents
           # 确保在分片文档中保持 Cursor 规则上下文
        
        3. START iOS DEVELOPMENT CYCLE:
        # 3. 启动 iOS 开发循环：
           a. SM Agent (New Chat): @sm → *create
           # a. SM 代理（新对话）：@sm → *create
              - Creates next iOS story from sharded docs
              # 从分片文档创建下一个 iOS 故事
              - Includes cursor rules compliance requirements
              # 包含 Cursor 规则合规要求
              - Review and approve story (Draft → Approved)
              # 审查和批准故事（草稿 → 已批准）
           
           b. Dev Agent (New Chat): @dev
           # b. 开发代理（新对话）：@dev
              - CRITICAL: Automatically loads cursor rules on startup
              # 关键：启动时自动加载 Cursor 规则
              - Implements approved story following iOS best practices
              # 按照 iOS 最佳实践实现已批准的故事
              - Uses *check-rules command to validate compliance
              # 使用 *check-rules 命令验证合规性
              - Updates File List with all changes
              # 更新文件列表包含所有更改
              - Marks story as "Review" when cursor rules validation passes
              # 当 Cursor 规则验证通过时将故事标记为"审查"
           
           c. QA Agent (New Chat): @qa → review-story
           # c. QA 代理（新对话）：@qa → review-story
              - Senior iOS dev review with cursor rules validation
              # 高级 iOS 开发审查，包含 Cursor 规则验证
              - Checks Objective-C/Swift best practices
              # 检查 Objective-C/Swift 最佳实践
              - Validates library usage patterns
              # 验证库使用模式
              - Fixes small issues directly following cursor rules
              # 直接修复小问题，遵循 Cursor 规则
              - Updates story status (Review → Done or stays Review)
              # 更新故事状态（审查 → 完成 或保持审查）
           
           d. If QA finds cursor rules violations:
           # d. 如果 QA 发现 Cursor 规则违规：
              - Dev Agent (New Chat): Address cursor rules issues
              # 开发代理（新对话）：解决 Cursor 规则问题
              - Use *check-rules to validate fixes
              # 使用 *check-rules 验证修复
              - Return to QA for final approval
              # 返回 QA 进行最终批准
        
        4. CONTINUOUS COMPLIANCE:
        # 4. 持续合规：
           - Use *check-rules before completing any story
           # 在完成任何故事之前使用 *check-rules
           - Validate cursor rules during code reviews
           # 在代码审查期间验证 Cursor 规则
           - Maintain cursor rules compliance
           # 保持 Cursor 规则合规性
           - Update cursor rules as iOS standards evolve
           # 随着 iOS 标准的发展更新 Cursor 规则
        
        5. REPEAT: Continue cycle for all epic stories with cursor rules enforcement
        # 5. 重复：对所有史诗故事继续循环，强制执行 Cursor 规则
        
        Reference: data#bmad-kb:iOS Development with Cursor Rules
        # 参考：data#bmad-kb:iOS Development with Cursor Rules

  # 流程图：工作流可视化
  flow_diagram: |
    ```mermaid
    graph TD
        A[Start: iOS Development] --> B[bmad-master: Initialize cursor rules]
        B --> C[architect: Analyze iOS project + cursor rules]
        C --> D[pm: prd.md with iOS requirements]
        D --> E[architect: iOS architecture.md]
        E --> F[dev: Validate cursor rules compliance]
        F --> G[po: Validate with cursor rules]
        G --> H{Cursor rules compliant?}
        H -->|No| I[Return to relevant agent for fixes]
        H -->|Yes| J[Move to iOS Development Cycle]
        I --> G

        style J fill:#90EE90
        style B fill:#87CEEB
        style F fill:#FFB6C1
        style G fill:#DDA0DD
    ```
    %% 流程说明：
    %% A: 开始 iOS 开发 → B: 主控代理初始化 Cursor 规则
    %% B → C: 架构师分析 iOS 项目 + Cursor 规则
    %% C → D: 产品经理创建包含 iOS 需求的 prd.md
    %% D → E: 架构师创建 iOS architecture.md
    %% E → F: 开发者验证 Cursor 规则合规性
    %% F → G: 产品负责人使用 cursor rules 验证
    %% G → H: Cursor 规则是否合规？
    %% H(否) → I: 返回相关代理修复
    %% H(是) → J: 转入 iOS 开发循环

  # 决策指导：何时使用此工作流
  decision_guidance:
    when_to_use:  # 使用场景
      - iOS native application development      # iOS 原生应用开发
      - Objective-C module creation            # Objective-C 模块创建
      - Swift component development            # Swift 组件开发
      - iOS framework/library projects        # iOS 框架/库项目
      - Projects requiring strict cursor rules compliance  # 需要严格 Cursor 规则合规的项目
      - Code modernization with standards enforcement      # 带标准强制执行的代码现代化

    cursor_rules_integration:  # Cursor 规则集成
      - Automatic rules loading in dev agent startup       # 开发代理启动时自动加载规则
      - Continuous compliance validation                   # 持续合规验证
      - Library usage pattern enforcement                  # 库使用模式强制执行
      - Code quality gates with cursor rules               # 带 Cursor 规则的代码质量门禁
      - iOS best practices integration                     # iOS 最佳实践集成

  # 交接提示：代理间的标准化交接话术
  handoff_prompts:
    rules_init_to_analysis: "Cursor rules initialized. Analyze iOS project structure with compliance assessment."
    # 规则初始化到分析："Cursor 规则已初始化。分析 iOS 项目结构并进行合规评估。"
    
    analysis_to_pm: "iOS project analysis complete with cursor rules baseline. Create comprehensive PRD."
    # 分析到产品经理："iOS 项目分析完成，包含 Cursor 规则基线。创建全面的 PRD。"
    
    pm_to_architect: "PRD ready with iOS requirements. Save it as docs/prd.md, then create iOS architecture with cursor rules integration."
    # 产品经理到架构师："PRD 已准备好，包含 iOS 需求。保存为 docs/prd.md，然后创建集成 Cursor 规则的 iOS 架构。"
    
    architect_to_validation: "Architecture complete. Save it as docs/architecture.md. Validate cursor rules compliance."
    # 架构师到验证："架构完成。保存为 docs/architecture.md。验证 Cursor 规则合规性。"
    
    validation_to_po: "Cursor rules validation complete. Please validate all artifacts for iOS standards and cursor rules compliance."
    # 验证到产品负责人："Cursor 规则验证完成。请验证所有工件的 iOS 标准和 Cursor 规则合规性。"
    
    po_issues: "PO found cursor rules violations in [document]. Return to [agent] to fix following iOS standards."
    # PO 发现问题："PO 在 [文档] 中发现 Cursor 规则违规。返回 [代理] 按照 iOS 标准修复。"
    
    complete: "All iOS planning artifacts validated with cursor rules compliance. Move to iOS development cycle."
    # 完成："所有 iOS 规划工件已通过 Cursor 规则合规验证。转入 iOS 开发循环。"

  # iOS 特定要求
  ios_specific_requirements:
    coding_standards:  # 编码标准
      - Follow cursor rules naming conventions     # 遵循 Cursor 规则命名约定
      - Use approved library patterns              # 使用已批准的库模式
      - Maintain proper file organization         # 保持正确的文件组织
      - Implement iOS best practices              # 实施 iOS 最佳实践

    development_tools:  # 开发工具
      - Xcode project integration                 # Xcode 项目集成
      - Cursor rules validation                   # Cursor 规则验证
      - Automated compliance checking             # 自动化合规检查
      - iOS-specific linting rules               # iOS 特定的 linting 规则

    quality_gates:  # 质量门禁
      - Cursor rules compliance validation        # Cursor 规则合规验证
      - iOS code review standards                 # iOS 代码审查标准
      - Library usage verification               # 库使用验证
      - Architecture pattern compliance          # 架构模式合规