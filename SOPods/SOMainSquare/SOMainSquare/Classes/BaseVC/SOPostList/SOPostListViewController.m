//
//  SOPostListViewController.m
//  Soul_New
//
//  Created by HG on 2018/11/9.
//  Copyright © 2018 Soul. All rights reserved.

#import <SOPostModuleService/SOPostEventExposeManage.h>
#import <SoulLog/SLogAPI.h>
#import <SoulCoreBase/SoulCoreBase.h>
#import "SOPostListViewController.h"
#import <SoulBussinessKit/SOTopicLabelsModel.h>
#import <SOMainSquare/SOCoCreatePostsViewModel.h>
#import <SOPostModuleService/SOSquareTimelinePromptLayout.h>
#import <SOVideoPlayer/SOVideoKit.h>
#import <SoulBussinessKit/SOTopicRequest.h>
#import <SOPostModuleService/SOPostHeader.h>
#import <SoulUIKit/SONavigationViewController.h>
#import <SOPostModuleService/SoulerThumbUpActionHelper.h>
#import <SOPostModuleService/SOSquareTimelineCell.h>
#import <SOVideoPlayer/SOVideoKit.h>
#import <SOPostModuleService/SOPostHeader.h>
#import <SoulProtocols/SOPostToTopicDetailProtocol.h>
#import <SoulProtocols/SOPostSquareToSoulmateProtocol.h>
#import <SoulProtocols/SOListVCToSubSquareProtocol.h>
#import <SoulBussinessKit/SOPostToolModel.h>
#import <SoulBussinessKit/SOPostToolFlexiblyView.h>
#import <SoulSocialShare/SOCommonShareAndToolView.h>
#import <SoulUIKit/SOActionSheet.h>
#import <SOPostModuleService/SquareEmptyCell.h>
#import <SOPostModuleService/SOSquareTimelinePromptCell.h>
#import <SOPostModuleService/SOSquareTimeLineActionCell.h>
#import <SOPostModuleService/SOSquareTagSimilarCell.h>
#import <SOPostModuleService/SquareRecommandCell.h>
#import <SOPostModuleService/SASquareSearchNewMoreUserCell.h>
#import <SOPostModuleService/SquareTopicSimilarityCell.h>
#import <SOPostModuleService/SoulTimeLineNoFollowerPromptCell.h>
#import <SOPostModuleService/SoulTimeLineFollowPromptCell.h>
#import "SOPostListViewController+DataTracker.h"
#import "SOPostListViewController+Search.h"
#import "SOPostListViewController+SOUtils.h"
#import <SoulCoreBase/SOAudioSessionCategoryManager.h>

//xib 只下沉了类 资源文件在主工程;
#import <SOPostModuleService/SOCoCreatePrompt1.h>
#import <SOPostModuleService/SOCoCreatePrompt2.h>
#import <SOPostModuleService/SOCoCreatePrompt3.h>
#import <SOPostModuleService/SquareTopicSimilarityModel.h>
#import <SOPostModuleService/SASearchPostListViewModel.h>
#import <SOPostModuleService/SAPostManager.h>
#import "SOPostListViewController+AdvertViewController.h"
#import <MJRefresh/MJRefresh.h>
#import <SoulBussinessKit/SOEmptyView.h>
#import <MMDrawerController/UIViewController+MMDrawerController.h>
#import <SOPostModuleService/SOPostHeader.h>
#import <SoulUIKit/SoulAlertView.h>
#import <SOMusicPlayer/SOMusicPlayer.h>
#import <SoulBussinessKit/SOPostInfoRequest.h>
#import <SOPostModuleService/SOSearchSquareFunctionCell.h>
#import <SOPostModuleService/SubSquareTopicSimilarityViewModel.h>
#import <SoulRouter/ZIKServiceRouter+Discover.h>
#import <SoulProtocols/SoulYoungManagerProtocol.h>
#import <SOPostModuleService/SOSquareTimelineCell.h>
#import <SoulProtocols/SOMusicPlayerManagerProtocol.h>
#import <SoulNetworking/SoulNetworking.h>
#import "SOCTMediorRoter.h"
#import <SOPostModuleService/SOSearchTagCell.h>
#import <SOPostModuleService/SOSearchSquareTitleCell.h>
#import <SOPostModuleService/SOSearchComboTagCell.h>
#import <SOPostModuleService/SOSearchMusicCommentCell.h>

#import <SOPostModuleService/SOSearchGroupChatComboCell.h>
#import <SOPostModuleService/SOSearchGroupChatSingleCell.h>
#import <SOPostModuleService/SASquareSearchNewUserCell.h>
#import <SOPostModuleService/SASquareSearchUserListCell.h>
#import <SoulProtocols/SOMusicPlayerManagerProtocol.h>
#import <SoulProtocols/SOAudioManagerProtocol.h>
#import <SoulProtocols/StrangerViewControllerProtocol.h>
#import <SoulCore/SoulCore.h>
#import "SubSquareSchoolMomentsViewController.h"
#import <SOPostModuleService/SOSquareOfficialGuideCell.h>
#import <SOPostModuleService/SOSearchTagContentCell.h>
#import "SOPostListViewController+RegisterNotification.h"

#import "SOSearchVC.h"
#import <SOWebInterface/SOWebManagerProtocol.h>
#import <SoulProtocols/SOCameraManagerProtocol.h>

#import "SOCommentKeyboardController.h"
#import <SoulProtocols/SoulPostImageBrowserControllerProtocol.h>
#import <SoulProtocols/SoulPostImageBrowserViewControllerProtocol.h>
#import <SoulProtocols/FeelingViewControllerProtocol.h>
#import <SoulRouter/SoulRouter.h>

#import <SOldPhotoBrowser/PhotoBrowserViewController.h>
#import <SOldPhotoBrowser/PhotoController.h>
#import "SOPostImageBrowserModel.h"
#import <SoulSocialShare/SOShareButtonView.h>
#import <SoulSocialShare/SOUniteTabShareView.h>
#import <SoulProtocols/SOReleaseViewControllerProtocol.h>
#import <SoulProtocols/ChatRoomLifeUtilProtocol.h>
#import <SoulBussinessKit/SOMusicPlayerManager.h>
#import <SoulAssets/UIImage+SoulAssets.h>
#import <SoulAssets/NSURL+SoulAssets.h>

#import "SOListRefreshViewController.h"
//后期还原的
#import "SubSquareTopicViewController.h"
#import "SOOfficialMessageViewController.h"
#import "SOPostListViewController+OtherBusiness.h"
#import <SoulBussinessKit/SAPostTextLayoutFactory.h>
#import "SOSchoolBarPostListViewController.h"
#import <SoulWeb/SOWebManager.h>
#import <SoulAPMSDK/SoulAPMSDK.h>
#import <SOPostModuleService/SOInsightAnalysisHelper.h>
#import <SOPostModuleService/NSObject+SOPageConfig.h>
#import <SoulBussinessKit/SOPost+biz.h>
#import <SoulAdManager/SoulAdModel.h>
#import <SoulBussinessKit/SOSquareFrameManager.h>
#import <SoulProtocols/SoulProtocols.h>
#import "SoulSquareMainRecommandViewController.h"
#import "SoulSquareMainFollowedViewController.h"
#import "SoulSquareMainRecentViewController.h"
#import "RecommandTagsViewController.h"
#import "SOTopicInfoViewController.h"
#import "SOCoCreateEditViewController.h"
#import "SOMainSquareViewController.h"
#import "SOAnswerSquareViewController.h"
#import "SubSquareCoCreateViewController.h"
#import "SOCreateVoiceViewController.h"
#import "SubSquareRouter.h"
#import "SOPostListViewController+SquareFunction.h"
#import "SOPostListViewController+SquareProperty.h"
#import <SOPostModuleService/SOInteractionLikeItemView.h>
#import <SoulProtocols/SOLoveBallSubassemblyManagerProtocol.h>
#import <SoulProtocols/SOMatchSubassemblyManagerProtocol.h>
#import <SOGiftPlayVideoView/SAGiftAnimationManager.h>
#import <SOPhotoKit/SOPhotoKit.h>
#import <SoulADManager/SoulAdEventDefine.h>
#import <SOPostModuleService/SOSearchAppletsCell.h>
#import <SOPostModuleService/SOSearchTextGroupChatCell.h>
#import <SOPostModuleService/SOSearchMusicCell.h>
#import <SoulProtocols/SOChatRoomSearchResultCellProtocol.h>
#import "SOTopicSquareSoulerTabCell.h"
#import <SOPostModuleService/SoulNativeAdTableViewCell.h>
#import <SOPostModuleService/SOPostModuleService-swift.h>
#import <SoulBussinessKit/SoulBussinessKit-Swift.h>
#import <SOMainSquare/SOMainSquare-swift.h>
#import <SOAppFrameInterface/SoulRootRouterProtocol.h>
#import <SOUGCInterface/SOUGCInterface.h>
#import <SOPostModuleService/SOSquareFeedsCardCell.h>
#import <SoulBussinessKit/SOHQualitPostApplyManager.h>
#import <SOEvaluateManager/SOSquareEvaluateCellItem.h>
#import <SoulCore/SOPostLottieLimitManage.h>
#import <SoulBussinessKit/SOBehaviorCollectionManager.h>
#import <SOUGCBaseUI/SOSquareExposeTrackManager.h>
#import <SoulBussinessKit/SOSquarePostFliterManager.h>
#import <SoulProtocols/SOSquareWithHomePageInterfaceProtocol.h>

@interface SOPostListViewController ()
<
SOSquareTimelinePromptCellDelegate,
SquareRecommandCellDelegate,
RecommandTagCellDelegate,
SoulTimeLineFollowPromptCellDelegate,
SATagSquareUserListViewDelegate,
SOSquareTagSimilarCellDelegate,
SORecommandSquareChatCellDelegate,
SOVideoPlayerDelegate,
SOSearchTagCellDelegate,
SASquareSearchNewUserCellDelegate,
SOSearchSquareTitleCellDelegate,
SOSearchGroupChatComboCellDelegate,
SOSearchGroupChatSingleCellDelegate,
SOSearchGroupChatSimpleCellDelegate,
SOSearchComboTagCellDelegate,
SOSearchTagContentCellDelegate,
SOPhotoAlbumDelegate,
SOSearchMusicCommentCellDelegate,
SOSquareWithHomePageInterfaceProtocol>

@property (nonatomic, assign) BOOL isLikeRequest;
@property (nonatomic, assign) BOOL isCollectRequest;
@property (nonatomic, assign) CGFloat offsetY_last;
@property (nonatomic, strong) NSString *uuid;
@property (nonatomic, assign) BOOL isRequesting;
@property (nonatomic, assign) BOOL useSrolled;
@property (nonatomic, copy) RouterEventBlock routerEventBlock;

@property (nonatomic, weak) NSObject *imageBrowserCtrl;

@property (nonatomic, weak) SOSquareTimelineCell* keyboardCell;
@property (nonatomic, copy)void(^smoothAnimationChangeVideo)(void);

@property (nonatomic,strong) SOCommentKeyboardController *keyboardVC;

//广告曝光只触发一次
@property (nonatomic, assign) BOOL isReportAD;
@property (nonatomic, weak)SOSquareTimelineCell *playEmojiCell;
@property (nonatomic, strong) SOPhotoAlbumView *photoAlbumView;
@property (nonatomic, copy) NSString *templateId;

@end

@implementation SOPostListViewController
@synthesize tableView = _tableView;

#pragma mark - ViewController init
- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    BOOL isTTPermissions = [[MMKV defaultMMKV] getBoolForKey:TTLicenseNotificationLicenseResultKey];
    if (isTTPermissions) {
        [SOVideoPlayerGlobalConfiguration clearEngineStrategy];
    }
}

- (instancetype)init {
    if (self = [super init]) {
        self.rendBeginInterval = [[NSDate date] timeIntervalSince1970] * 1000;
        [self configrationPageInfo];
    }
    return self;
}

- (instancetype)initWithMangerWithTableView:(UITableView *)tableView type:(SubSquareRouterType)type {
    if (self = [super init]) {
        self.routerType = type;
        self.tableView = tableView;
        [self configrationPageInfo];
    }
    return self;
}

- (instancetype)initWithRequest:(SoulSquareNewRequest *_Nullable)request type:(SubSquareRouterType)type {
    if (self = [self init]) {
        _tableViewModel = [[SOPostsTableViewModel alloc] initWithRequst:request];
        _tableViewModel.routerType = type;
        _routerType = type;
        [self configTableViewModel];
        [self configrationPageInfo];
    }
    return self;
}

- (instancetype)initWithTableViewModel:(SOPostsTableViewModel *)tableViewModel type:(SubSquareRouterType)type {
    if (self = [self init]) {
        _tableViewModel = tableViewModel;
        _tableViewModel.routerType = type;
        _routerType = type;
        [self configTableViewModel];
        [self configrationPageInfo];
    }
    return self;
}

- (NSString *)eventPageId {
    return SOString(self.pageId);
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = GET_COLOR(0);
    self.view.isAccessibilityElement = NO;
    
    NSString *warning =  [NSString stringWithFormat:@"%@_pageId不能为nil,用当前界面的埋点的PageId补全",NSStringFromClass(self.class)];//!OCLint
    NSAssert(self.pageId.length, warning);
    
    //生命周期通知除外 其他通知都建在分类
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationDidBecomeActiveNotification:) name:UIApplicationDidBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationWillResignActiveNotification:) name:UIApplicationWillResignActiveNotification object:nil];
    // app字体调节后回调
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appFontChanged)
                                                 name:SOFontSizeTypeDidChangedNoti object:nil];
    
    [self registerNotification];
    [self setPostListDelegate];
    [self layoutCustomSubviews];
    [self initlizeNetRequest];
    [self TTSDKPreDownLoad];
    //需要放在viewDidLoad 最后
    [self inslightTrackRender:PAGE_RENDER_EVENT];
    /// 帖子删除管理
    [self addUIactor:self.postDeleteManage];
    
    /// 记录离开时间
    if ([self refreshManage]){
        [self addUIactor:[self refreshManage]];
    }
}

- (void)TTSDKPreDownLoad {
    BOOL isTTPermissions = [[MMKV defaultMMKV] getBoolForKey:TTLicenseNotificationLicenseResultKey];
    
    //添加预下载开关
    BOOL ttsdkSwitch = [SoulConfigManager sharedInstance].globalConfig.ugc_ttsdk_predownload_switch;
    
    if (isTTPermissions && ttsdkSwitch) {
        [SOVideoPlayerGlobalConfiguration enableEngineStrategy:SOVideoPlayerStrategyTypePreload];
        //[SOVideoPlayerGlobalConfiguration enableEngineStrategy:SOVideoPlayerStrategyTypePreRender];
    }
}

- (void)initlizeNetRequest {
    if([self.delegate respondsToSelector:@selector(postListVC:willBeginRequestWithRefreshType:)]){
        [self.delegate postListVC:self willBeginRequestWithRefreshType:TableViewRefreshTypeFirstLoad];
    }
    
    if (self.routerType == SubSquareRouterTypeTopic) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self.tableViewModel beginFristRequest];
        });
    } else if (self.routerType == SubSquareRouterTypeSchoolBarHot || self.routerType == SubSquareRouterTypeSchoolBarLatest || self.routerType == SubSquareRouterTypeSchoolBarActivity){
        //校园吧请求由外界控制
        self.isCancleAutoFirstLoad = YES;
    } else {
        if (!self.isCancleAutoFirstLoad) {
            [self.tableViewModel beginFristRequest];
        } else {
            //不触发
            NSLog(@"不主动触发请求");
        }
    }
    if([self.delegate respondsToSelector:@selector(postListVC:didBeginRequestWithRefreshType:)]){
        [self.delegate postListVC:self didBeginRequestWithRefreshType:TableViewRefreshTypeFirstLoad];
    }
}

/// app字体改变的时候，刷新列表
- (void)appFontChanged {
    [self scrollToTopWithRefreash:true];
}

#pragma mark --LifeCycle
// 子控制器将要被展示
- (void)postListViewControllWillAppear:(BOOL)animated {
    // 这个方法暂时是无效的
}

- (void)postListViewControllDidAppear:(BOOL)animated {
    NSLog(@"____%@ 出现了",NSStringFromClass(self.class));
    self.uuid = nil;
    self.tableView.isAccessibilityElement = YES;
    [self.tableView so_setPopupViewIsAccessibilityModal];
    
    [self resetDisplayTime];
    /// 记录pv 的时间戳
    self.sessionId = [NSString stringWithFormat:@"%.0f",[NSDate new].timeIntervalSince1970];
    [self.eventHander squareEventPVReport];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"PostVideoPlayControlNoti" object:nil userInfo:@{@"state":@2}];
    
    [self.actorList enumerateObjectsUsingBlock:^(id<SoulUIActorDelegate>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(viewDidAppear:)]){
            [obj viewDidAppear:animated];
        }
    }];
    /// 滚动停止开始 计算打招呼的显示 快捷聊天
    [self findBestQuickChatViewWithScrollView:self.tableView];
    
}

// 子控制器将要被隐藏
- (void)postListViewControllWillDisAppear:(BOOL)animated {
    // 这个方法暂时是无效的
}

- (void)postListViewControllDidDisAppear:(BOOL)animated {
    DDLogInfo(@"postListViewControllDidDisAppear");
    NSLog(@"____%@ 消失了",NSStringFromClass(self.class));
    self.uuid = nil;
    self.tableView.accessibilityViewIsModal = NO;

    [self recordDisplayTime];
    [self.actorList enumerateObjectsUsingBlock:^(id<SoulUIActorDelegate>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(viewDidDisappear:)]){
            [obj viewDidDisappear:animated];
        }
    }];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self sendMemoryVisibilityNotification:YES];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    [self sendMemoryVisibilityNotification:NO];
}

- (void)sendMemoryVisibilityNotification:(BOOL)isVisibility {
    if ([self isKindOfClass:NSClassFromString(@"SORecWebSubViewController")]) {
        if (self.configItem.tabType.integerValue == 200) { // 回忆广场专用
            [[NSNotificationCenter defaultCenter] postNotificationName:@"kSOSquareMemoryVisibilityNotificationKey" object:nil userInfo:@{@"visibility" : @(isVisibility)}];
        }
    }
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
    _isNotWatchPost = NO;
    [self resetDisplayTime];
    
    //主广场系列 的生命周期自己管理
    if ([self isRefuseContinueLifecycle]) {
        [self postListViewControllDidAppear:NO];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    _isNotWatchPost = YES;
    if (self.routerType != SubSquareRouterTypeMainRecommand && self.routerType != SubSquareRouterTypeSearch) {
        [self recordDisplayTime];
    }
    
    //主广场系列 的生命周期自己管理
    if ([self isRefuseContinueLifecycle]) {
        [self postListViewControllDidDisAppear:NO];
    }
}

- (void)setPostListDelegate {
    if (self.parentViewController && [self.parentViewController conformsToProtocol:@protocol(SOPostListVCRefreshDelegate)]) {
        id <SOPostListVCRefreshDelegate> delegate = (id)self.parentViewController;
        self.delegate = delegate;
    }
}

- (void)layoutCustomSubviews {
    [self.view addSubview:self.tableView];
    if (_isVideoTab) {
        self.tableView.frame = CGRectMake(0, kNavgationBarHeight, KScreenWidth, kScreenHeight - kNavgationBarHeight - kTabBarHeight);
    } else if (self.routerType == SubSquareRouterTypeTopic && self.isNewTagSquare) {
        [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.right.equalTo(self.view);
            make.bottom.mas_equalTo(0);
        }];
    } else {
        [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.right.bottom.equalTo(self.view);
        }];
    }
    self.tableViewAdaptor.tableView = self.tableView;
    [self.view addSubview:self.netWorkErrorView];
    [self.view addSubview:self.placeholderView];
    @weakify(self);
    
    [[RACObserve(self.tableViewModel, sectionModelArray) filter:^BOOL(NSArray * sectionModelArray) {
        return sectionModelArray.count > 0;
    }] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        dispatch_async(dispatch_get_main_queue(), ^{
            self.tableViewAdaptor.sectionItems = x;
            [self dealPostDelete];
            [self.tableView reloadData];
        });
    }];
}

- (void)dealPostDelete {
    for (SOTableViewSectionModel *sectionModel in _tableViewModel.sectionModelArray) {
        SOWeakIfy(self);
        SOWeakIfy(sectionModel);
        sectionModel.deleteLayoutBlock = ^{
            NSLog(@"帖子被删除");
            SOStrongIfy(self);
            SOStrongIfy(sectionModel);
            SOPost *post = sectionModel.dataModel;
            if ([post isKindOfClass:[SOPost class]]) {
                for (SOTableViewSectionModel *sectionModel in self.tableViewModel.sectionModelArray) {
                    for (SOSquareEvaluateCellItem *cellItem in sectionModel.sectionItems) {
                        if([cellItem isKindOfClass:[SOSquareEvaluateCellItem class]] && [cellItem.model.bizId isEqualToString:post.postId]) {
                            NSLog(@"移除帖子关联调研组件");
                            [sectionModel.sectionItems removeObject:cellItem];
                            [self.tableView reloadData];
                            return;
                        }
                    }
                }
            }
        };
    }
}

- (BOOL)chectVisibleCellsHasVideo {
    BOOL hasVideo = NO;
    for (SOTableViewSectionModel *sectionModel in _tableViewModel.sectionModelArray) {
        if (![sectionModel.sectionItems.firstObject isKindOfClass:SOSquareTimelineLayout.class]) {
            continue;
        }
        SOSquareTimelineLayout *layout = sectionModel.sectionItems.firstObject;
        if ([layout.post.type isEqualToString:@"VIDEO"]) {
            hasVideo = YES;
            break;
        }
    }
    return hasVideo;
}

- (void)disableHeadRefresh {
    self.tableView.mj_header = nil;
}

- (void)disableFooterRefresh {
    self.tableView.mj_footer = nil;
}

- (UIViewController *)soContainerController {
    _soContainerController = _soContainerController?:nil; //添加原因：解决Warn most问题需要补充下划线参数赋值，但同时期望保持业务逻辑无变化。
    return self.parentViewController;
}

- (void)postListDidEnterForeground:(SubSquareRouterType)type {
    
}

- (void)postListDidEnterBackgroud:(SubSquareRouterType)type {
    
}

- (void)postListViewControllScreenLocked:(NSNotification *)notify {
    
}

- (void)postListViewControllScreenUnLocked:(NSNotification *)notify {
    
}

#pragma mark -- setter

- (NSString *)uuid {
    _uuid = _uuid?:nil; //添加原因：解决Warn most问题需要补充下划线参数赋值，但同时期望保持业务逻辑无变化。
    return [SOGlobalPropertyManager sharedInstance].uuid;
}

- (void)setTableView:(UITableView *)tableView {
    _tableView = tableView;
}

- (UITableView *)tableView {
    if (_tableView) {
        return _tableView;
    }
    _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    
    _tableView.delegate = self.tableViewAdaptor;
    _tableView.dataSource = self.tableViewAdaptor;
    _tableView.showsVerticalScrollIndicator = NO;
    _tableView.showsHorizontalScrollIndicator = NO;
    _tableView.scrollsToTop = YES;
    _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    _tableView.backgroundColor = GET_COLOR(0);
    _tableView.separatorColor = [UIColor clearColor];
    _tableView.estimatedRowHeight = 0;
    _tableView.estimatedSectionHeaderHeight = 0;
    _tableView.isAccessibilityElement = YES;
    _tableView.estimatedSectionFooterHeight = 0;
    [_tableView.panGestureRecognizer addTarget:self action:@selector(tablePanGestureOwnFuction)];
    
    return _tableView;
}

- (SOTableViewAdaptor *)tableViewAdaptor {
    if (!_tableViewAdaptor) {
        _tableViewAdaptor  = [[SOTableViewAdaptor alloc] init];
        _tableViewAdaptor.delegate = self;
    }
    return _tableViewAdaptor;
}

- (SOCommentKeyboardController *)keyboardVC {
    if (!_keyboardVC) {
        SOCommentKeyboardController *keyboardVC = [SOCommentKeyboardController new];
        _keyboardVC = keyboardVC;
    }
    return _keyboardVC;
}

- (SOSquareEventHandler *)eventHander {
    if (!_eventHander) {
        _eventHander = [[SOSquareEventHandler alloc] init];
        _eventHander.responder = self;
    }
    return _eventHander;
}

- (SOSquareLinkMonitor *)linkMonitor {
    if (!_linkMonitor) {
        _linkMonitor = [[SOSquareLinkMonitor alloc] init];
    }
    return _linkMonitor;
}

- (SOSquarePresenter *)presenter {
    if (!_presenter) {
        _presenter = [[SOSquarePresenter alloc] init];
        _presenter.responder = self;
    }
    return _presenter;
}

- (UINavigationController *)navigationController {
    if (_mainNavigationController) {
        return _mainNavigationController;
    } else {
        return [super navigationController];
    }
}

- (UITabBarController *)tabBarController {
    if (_mainNavigationController) {
        return _mainNavigationController.tabBarController;
    } else {
        return [super tabBarController];
    }
}

- (SOSquareLinkMonitor *)getLinkMonitor {
    return self.linkMonitor;
}

- (SOSquareExposeTrackManager *)exposeManager {
    if (!_exposeManager) {
        _exposeManager = [SOSquareExposeTrackManager new];
        [_exposeManager registerViewDidAppear:@selector(postListViewControllDidAppear:) withTarget:self];
        [_exposeManager registerViewDidDisAppear:@selector(postListViewControllDidDisAppear:) withTarget:self];
        [_exposeManager addObserverTableViewLifeCycle:self];
        _exposeManager.shouldAsyncExecuteBlock = NO;
        
    }
    return _exposeManager;
}

- (void)addTableViewRefreshHeader {
    if([self.delegate respondsToSelector:@selector(postListVC:willBeginRequestWithRefreshType:)]){
        [self.delegate postListVC:self willBeginRequestWithRefreshType:TableViewRefreshTypeRefresh];
    }
    
    [self.tableViewModel endFooterLoadMore];
    [self.tableViewModel beginHeaderRefresh];
    
    if([self.delegate respondsToSelector:@selector(postListVC:didBeginRequestWithRefreshType:)]){
        [self.delegate postListVC:self didBeginRequestWithRefreshType:TableViewRefreshTypeRefresh];
    }
}

- (void)addTableViewAutoLoadMoreFooter {
    if([self.delegate respondsToSelector:@selector(postListVC:willBeginRequestWithRefreshType:)]){
        [self.delegate postListVC:self willBeginRequestWithRefreshType:TableViewRefreshTypeLoadMore];
    }
    
    [self.tableViewModel beginFooterLoadMore];
    if([self.delegate respondsToSelector:@selector(postListVC:didBeginRequestWithRefreshType:)]){
        [self.delegate postListVC:self didBeginRequestWithRefreshType:TableViewRefreshTypeLoadMore];
    }
}

- (void)configTableViewModel {
    _tableViewModel.postListVC = self;
    @weakify(self);
    [_tableViewModel setTableView:self.tableView];
    
    // add header refresh
    [self.tableViewModel addTableViewRefreshHeader:^{
        @strongify(self);
        /// 下拉刷新  主动触发当前页面可上报cell的曝光。另外同步执行，避免数据已经更换
        self.exposeManager.shouldAsyncExecuteBlock = YES;
        [self.exposeManager exposeCurrentCells];
        self.isHeaderLoading = YES;
        [self addTableViewRefreshHeader];
    }];
    
    // add footer loadmore
    [self.tableViewModel addTableViewAutoLoadMoreFooter:^{
        @strongify(self);
        [self addTableViewAutoLoadMoreFooter];
    }];
    
    //  上拉或者下拉结束的回调
    [self.tableViewModel setFirstLoadComepletionBlock:^(id  _Nonnull response) {
        @strongify(self);
        if (self.routerType == SubSquareRouterTypeSearch) {
            if (self.view.window && [self.tableViewModel isKindOfClass:SONewSearchPostListViewModel.class]) {//可视
                SONewSearchPostListViewModel *searchVM = (SONewSearchPostListViewModel *)self.tableViewModel;
                NSString *result = @"";
                if([self isKindOfClass:NSClassFromString(@"SASearchPostListViewController")]) {
                    result = ((NSArray *)response[@"moduleData"][@"post"][@"data"]).count == 0 ? @"0":@"1";
                }else if([self isKindOfClass:NSClassFromString(@"SONewSearchPostListViewController")]) {
                    result = ((NSArray *)response[@"cardList"]).count == 0 ? @"0":@"1";
                }
                
                [SoulEvent eventExpose:@"SearchResultSquare_SearchResult"
                                params:@{
                    @"keyword": searchVM.searchKeyword,
                    @"tab_id": @"1",
                    @"result": result,
                    @"searchId": SOString([self searchId])
                }
                                pageId:PostSquare_SearchResult pagePrama:nil];
            }
        }
        
        [self.tableViewModel endFristLoadMore];
        [self.tableView reloadData];
        
        if ([self.delegate respondsToSelector:@selector(postListVC:refreshType:didfinishLoadWithResponse:)]) {
            [self.delegate postListVC:self refreshType:TableViewRefreshTypeFirstLoad didfinishLoadWithResponse:response];
        }
        if (self.routerType == SubSquareRouterTypeMainRecommand || self.routerType == SubSquareRouterTypeMainRecent) {
            dispatch_async(dispatch_get_main_queue(), ^{
                @strongify(self);
                [self firstLoadPostCompletedHandleLinkageAD];
            });
        }
        [self configEmptyPlaceHolderView];
        [self setPlaceholderViewHidden:YES];
        [self setNetworkErrorViewHidden:YES];
        /// 首次加载完成后，触发帖子头像状态动画
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self.tableViewModel loadPostAvatarStatusAnimation];
        });
    } failure:^{
        @strongify(self);
        [self.tableViewModel endFristLoadMore];
        [self setPlaceholderViewHidden:YES];
        [self setNetworkErrorViewHidden:NO];
        if ([self.delegate respondsToSelector:@selector(postListVC:refreshType:didfinishLoadWithResponse:)]) {
            [self.delegate postListVC:self refreshType:TableViewRefreshTypeFirstLoad didfinishLoadWithResponse:nil];
        }
    }];
    
    // header refresh
    [self.tableViewModel setHeadRefreshComepletionBlock:^(id  _Nonnull response) {
        @strongify(self);
        [self.tableView reloadData];
        if (self.routerType == SubSquareRouterTypeMainRecommand || self.routerType == SubSquareRouterTypeMainRecCategory) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                // 首页推荐广场的添加刷新的延时 播放推荐条数的动画
                [self.tableViewModel endHeaderRefresh];
                [self.tableViewModel endFooterLoadMore];
                self.isHeaderLoading = NO;
            });
        } else if (self.routerType == SubSquareRouterTypeMainRecent || self.routerType == SubSquareRouterTypeMainLocation) {
            //兼容推荐tab下的下拉菜单广场
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                // 首页推荐广场的添加刷新的延时 播放推荐条数的动画
                [self.tableViewModel endHeaderRefresh];
                [self.tableViewModel endFooterLoadMore];
                self.isHeaderLoading = NO;
            });
        } else {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self.tableViewModel endHeaderRefresh];
                [self.tableViewModel endFooterLoadMore];
                self.isHeaderLoading = NO;
            });
        }
        ///TODO:
        [self setPlaceholderViewHidden:YES];
        [self setNetworkErrorViewHidden:YES];
        
        if ([self.delegate respondsToSelector:@selector(postListVC:refreshType:didfinishLoadWithResponse:)]) {
            [self.delegate postListVC:self refreshType:TableViewRefreshTypeRefresh didfinishLoadWithResponse:response];
        }
        [self configEmptyPlaceHolderView];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [[NSNotificationCenter defaultCenter] postNotificationName:@"PostVideoPlayControlNoti" object:nil userInfo:@{@"state":@0}];
            [[NSNotificationCenter defaultCenter] postNotificationName:@"PostVideoPlayControlNoti" object:nil userInfo:@{@"state":@2}];
        });
        /// 首次加载完成后，触发帖子头像状态动画
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self.tableViewModel loadPostAvatarStatusAnimation];
        });
    } failure:^{
        @strongify(self);
        self.isHeaderLoading = NO;
        [self setPlaceholderViewHidden:YES];
        [self setNetworkErrorViewHidden:NO];
        [self.tableViewModel endHeaderRefresh];
        [self.tableViewModel endFooterLoadMore];
        if ([self.delegate respondsToSelector:@selector(postListVC:refreshType:didfinishLoadWithResponse:)]) {
            [self.delegate postListVC:self refreshType:TableViewRefreshTypeRefresh didfinishLoadWithResponse:nil];
        }
    }];
    
    // footer loadMore
    [self.tableViewModel setLoadMoreComepletionBlock:^(id  _Nonnull response) {
        @strongify(self);
        //检查是否可以自动加载
        if (self.routerType == SubSquareRouterTypeSearch || self.routerType == SubSquareRouterTypeTopic) {
            SquareResponse *squareInfo = [SquareResponseAdapter responseWithSquareRequest:self.tableViewModel.postListRequest response:response];
            
            NSObject <SoulPostImageBrowserControllerProtocol> *control = KSoulPostImageBrowserController.so_obj;
            
            [control so_invoke:@selector(loadMorePostWithResult:addUserDetailPage:) arguments:SOManiFold(squareInfo.posts,@(NO))];
        }
        @try {
            //发现广场 手动插入 防止正在播放的视频被reloadData打断
            [self.tableView reloadData];
        } @catch (NSException *exception) {
            
        }
        
        [self.tableViewModel endFooterLoadMore];
        [self.tableViewModel endHeaderRefresh];
        
        if ([self.delegate respondsToSelector:@selector(postListVC:refreshType:didfinishLoadWithResponse:)]) {
            [self.delegate postListVC:self refreshType:TableViewRefreshTypeLoadMore didfinishLoadWithResponse:response];
        }
        [self configEmptyPlaceHolderView];
        
        BOOL bRouterType = self.routerType == SubSquareRouterTypeSchoolMoments ||
        self.routerType == SubSquareRouterTypeSchoolBarLatest ||
        self.routerType == SubSquareRouterTypeSchoolBarHot || self.routerType == SubSquareRouterTypeMainLocation || self.routerType == SubSquareRouterTypeMainRecent || self.routerType == SubSquareRouterTypeQuestionAnswer;

        if (bRouterType && !self.tableViewModel.sectionModelArray.count) {
            self.tableView.mj_footer.hidden = YES;
        }else {
            self.tableView.mj_footer.hidden = NO;
        }
        
    } failure:^{
        @strongify(self);
        [self.tableViewModel endFooterLoadMore];
        [self.tableViewModel endHeaderRefresh];
        if ([self.delegate respondsToSelector:@selector(postListVC:refreshType:didfinishLoadWithResponse:)]) {
            [self.delegate postListVC:self refreshType:TableViewRefreshTypeLoadMore didfinishLoadWithResponse:nil];
        }
    }];
}

- (SOMainSquareViewController * _Nullable)getMainSquareViewController {
    SOMainSquareViewController *mainSquareVC = nil;
    id<NewTabBarControllerProtocol> tabbarController = [ZIKRouterToService(NewTabBarControllerProtocol) makeDestination];
    UITabBarController *tabbarVC = (UITabBarController *)tabbarController;
    
    UINavigationController *selectedNavigationController = tabbarVC.selectedViewController;
    if ([selectedNavigationController.viewControllers.firstObject isMemberOfClass:SOMainSquareViewController.class]) {
        mainSquareVC = (SOMainSquareViewController *)selectedNavigationController.viewControllers.firstObject;
        if (![mainSquareVC isKindOfClass:SOMainSquareViewController.class]) {
            NSLog(@"__________________mainSquareVC is nil selectedViewController is error");
        }
    }
    if (mainSquareVC == nil) {
        for (UINavigationController *nv in tabbarVC.viewControllers) {
            if ([nv.viewControllers.firstObject isMemberOfClass:SOMainSquareViewController.class]) {
                mainSquareVC = (SOMainSquareViewController *)nv.viewControllers.firstObject;
                break;
            }
        }
    }
    return mainSquareVC;
}

- (void)setIsFollowTag:(BOOL)isFollowTag {
    if (_isFollowTag == isFollowTag) { return; }
    _isFollowTag = isFollowTag;
}

- (void)requestDataWhenDataIsEmpty {
    if (self.tableViewModel.sectionModelArray.count != 0) return;
    [self.tableViewModel beginFristRequest];
}

- (void)showError:(NSString *)msg {
    [MBProgressHUD showError:msg toView:nil];
}

- (SONetworkError *)netWorkErrorView {
    if (_netWorkErrorView) {
        return _netWorkErrorView;
    }
    _netWorkErrorView = [[SONetworkError alloc] initWithFrame:CGRectMake(0, -kNavgationBarHeight, self.view.width, KScreenHeight-KNavBarHeight)];
    @weakify(self);
    _netWorkErrorView.requestAgainBlock = ^{
        @strongify(self);
        [self.tableViewModel beginFristRequest];
    };
    return _netWorkErrorView;
}

- (SOSquareCommonToastView *)commonToastView {
    if (!_commonToastView) {
        _commonToastView = [[SOSquareCommonToastView alloc] init];
        _commonToastView.hidden = YES;
    }
    return _commonToastView;
}

- (SOPlaceHolderView *)placeholderView {
    if (_placeholderView) {
        return _placeholderView;
    }
    NSString *imageName = @"square_placeholder";
    _placeholderView = [[SOPlaceHolderView alloc] initWithFrame:CGRectMake(0, 0, self.view.width, self.view.height) imageName:imageName];
    return _placeholderView;
}

- (void)setNetworkErrorViewHidden:(BOOL)isHidden {
    self.netWorkErrorView.hidden = isHidden;
    if (!isHidden){
        SLogInfoWithTag(SquareList, @"netError routerType %lu",(unsigned long)self.tableViewModel.routerType);
    }
}

- (void)setPlaceholderViewHidden:(BOOL)isHidden {
    if (isHidden) {
        [_placeholderView hiddenPlacehilderView];
        self.tableView.scrollEnabled = YES;
    }else{
        [self.view addSubview:self.placeholderView];
    }
}

- (void)dismiss {
    [self.mm_drawerController setOpenDrawerGestureModeMask:MMOpenDrawerGestureModeBezelPanningCenterView];
}

- (void)showQuestionAlertWithDismissBlock:(dispatch_block_t)block {
    if ([[MMKV defaultMMKV] getBoolForKey:@"alertshowed"]) {
        !block ?: block();
    }else{
        !block ?: block();
    }
}

- (void)showGiftVCWithPost:(SOPost*)post cell:(SOSquareTimelineCell *)cell {
    id<SoulYoungManagerProtocol> youngManager = [ZIKRouterToService(SoulYoungManagerProtocol) makeDestination];
    if (youngManager.configModel.isTeenageMode) {
        [MBProgressHUD showError:SoulYoungGiftLimitToast toView:self.view];
        return;
    }
    UIViewController * _giftNewVC = [self showGiftVCWithPost:post cell:cell withContainVC:self];
    @weakify(self);
    [self showQuestionAlertWithDismissBlock:^{
        @strongify(self);
        SONavigationViewController *nav = [[SONavigationViewController alloc] initWithRootViewController:_giftNewVC];
        nav.view.backgroundColor = [UIColor clearColor];
        nav.modalPresentationStyle = UIModalPresentationOverCurrentContext | UIModalPresentationFullScreen;
        [self presentViewController:nav animated:NO completion:NULL];
    }];
    
}

- (UIViewController *)showGiftVCWithPost:(SOPost *)post
                                    cell:(SOSquareTimelineCell *)cell
                           withContainVC:(UIViewController *)containerVC {
    
    id <SONewGiftUserInfoModelProtocol> userModel = [ZIKRouterToService(SONewGiftUserInfoModelProtocol) makeDestination];
    userModel.headBg = post.avatarColor;
    userModel.headIcon = post.avatarName;
    userModel.giftSource = SONewGiftSourceTopicInfo;
    userModel.userId = [SoulUserIDUtils getOriginId:post.authorIdEcpt];
    userModel.postId = post.postId;
    userModel.nightMode = YES;
    
    UIViewController< SONewGiftMainViewControllerProtocol> *_giftNewVC = (UIViewController< SONewGiftMainViewControllerProtocol> *)[ZIKRouterToView(SONewGiftMainViewControllerProtocol)makeDestinationWithConfiguring:^(ZIKViewRouteConfiguration<SONewGiftMainViewControllerConfigurationProtocol> * _Nonnull config) {
        [config makeDestinationWithUserModel:userModel];
    }];
    
    _giftNewVC.senceId = @"1";
    _giftNewVC.sendNewBeckoningGiftBlock = ^(NSDictionary * _Nonnull dict) {
        [SAGiftAnimationManager playAnimationWithExt:dict otherViews:nil type:SAGiftAnimationTypeDefault delegate:nil failure:nil];
        
    };
    
    _giftNewVC.sendNewGuardianPropsBlock = ^(NSDictionary * _Nonnull dict) {
        
    };
    
    _giftNewVC.sendNewSuperMemberGiftBlock = ^(NSDictionary * _Nonnull dict) {
    };
    return _giftNewVC;
}

- (BOOL)canShowHitLabel{
    return true;
}
#pragma mark - SORecommandSquareChatCellDelegate
- (void)clearRecommandSquareChatRooms:(SORecommandSquareChatCell *)cell layout:(SORecommandSquareChatCellLayout *)layout indexPath:(NSIndexPath *)indexPath{
    [self removeRecommandSquareChatRooms:cell layout:layout indexPath:(NSIndexPath *)indexPath];
}

- (void)enterChatRoom:(SORecommandSquareChatCell *)cell layout:(SORecommandSquareChatCellItemLayout *)layout{
    [self enterRecommandSquareChatRoom:cell layout:layout];
}

- (void)gotoRoomList:(SORecommandSquareChatCell *)cell{
    [self enterRoomList:cell];
}

#pragma mark -- need Overwrite

- (void)removeRecommandSquareChatRooms:(SORecommandSquareChatCell *)cell layout:(SORecommandSquareChatCellLayout *)layout indexPath:(NSIndexPath *)indexPath{
    //need overwrite
}

- (void)enterRecommandSquareChatRoom:(SORecommandSquareChatCell *)cell layout:(SORecommandSquareChatCellItemLayout *)layout {
    //need overwrite
}

- (void)enterRoomList:(SORecommandSquareChatCell *)cell{
    //need overwrite
}

- (void)removePrivacyTagMatch:(SOTableViewSectionModel *)sectionModel {
    //need overWrite
}

#pragma mark - SOTableViewAdaptorProtocol

- (void)tableView:(UITableView *)tableView didSelectObject:(id<SOTableviewCellItemProtocol>)object rowAtIndexPath:(NSIndexPath *)indexPath {
    SOSquareTimelineBaseLayout *layout = (SOSquareTimelineBaseLayout *)[self.tableViewModel cellModelForIndexPath:indexPath];
    id<SubSquareRouterProtocol> subSquareRouter = [ZIKRouterToService(SubSquareRouterProtocol) makeDestination];
    if (layout.cellType == SquareCellTypeSearchTag) {
        SOTagModel *tag = (SOTagModel *)layout.dataModel;
        
        /* 全部tab-相关标签-tag卡片点击埋点 */
        [SoulEvent eventClick:@"SearchResultSquare_AllRelatedTagCard"
                       params:nil
                       pageId:PostSquare_SearchResult pagePrama:nil];
        
        UIViewController *topicVC = [subSquareRouter routeToTopicSquareViewControllerWithTopicName:tag.tagName];
        [self.navigationController pushViewController:topicVC animated:YES];
    }
    SquareTopicSimilarityLayout * similarityLayout = (SquareTopicSimilarityLayout *)[self.tableViewModel cellModelForIndexPath:indexPath];
    if (layout.cellType == SquareCellTypeSimilarity) {
        SquareTopicSimilarityModel *similarityModel = similarityLayout.similarModel;
        
        [SoulEvent eventClick:@"TagSquare_RelatedTag" params:@{@"tId" :  similarityModel.tagId ?: @""} pageId:PostSquare_Tag pagePrama:nil];
        /// tagName标记，以待以后使用
        if ([similarityModel.tagName isEqualToString:similarityModel.name]) return;
        UIViewController * targetVC = [subSquareRouter routeToTopicSquareViewControllerWithTopicName:similarityModel.name];
        [self.navigationController pushViewController:targetVC animated:YES];
    }
}

- (void)tableView:(UITableView *)tableView willSetObject:(id<SOTableviewCellItemProtocol>)object cell:(UITableViewCell<SOTableViewCellProtocol>*)cell {
    SOSquareTimelineBaseLayout *layout = (id)object;
    switch (layout.cellType) {
        case SquareCellTypeNormal:
        {
            SOSquareTimelineLayout *cellLayout = (SOSquareTimelineLayout *)layout;
            if ([cell isKindOfClass:SOSquareTimelineCell.class]) {
                SOSquareTimelineCell *timelineCell = (id)cell;
                timelineCell.delegate = (id)self;
                cellLayout.delegate = self;
            }
        } break;
            
        default:
            break;
    }
}

- (void)tableView:(UITableView *)tableView didSetObject:(id<SOTableviewCellItemProtocol>)object cell:(UITableViewCell<SOTableViewCellProtocol>*)cell {
    SOSquareTimelineBaseLayout *layout = (id)object;
    NSIndexPath *indexPath = [tableView indexPathForCell:cell];
    switch (layout.cellType) {
        case SquareCellTypeChatRoom: {
            SOBaseTableViewCell *chatRoomCell = (SOBaseTableViewCell *)cell;
            [self respondseRoomCellEventControls:(id)chatRoomCell withContainer:self];
        }
            break;
        case SquareCellTypeSearchTag:{
            /* 已废弃的类型 */
        }
            break;
        case SquareCellTypeOneKeyFollowPrompt:
        {
            SoulTimeLineFollowPromptCell *followPromptCell = (SoulTimeLineFollowPromptCell *)cell;
            followPromptCell.delegate = (id)self;
        }
            break;
        case SquareCellTypePrompt:
        {
            SOSquareTimelinePromptCell *promptCell = (id)cell;
            promptCell.delegate = (id)self;
        }
            break;
        case SquareCellTypeOfficialGuide:
        {
            SOSquareOfficialGuideCell *officalCell = (id)cell;
            officalCell.delegate = (id)self;
        }
            break;
        case SquareCellTypeRecommandTag:
        {
            SquareRecommandCell *recomandCardCell = (id)cell;
            recomandCardCell.delegate = self;
            recomandCardCell.tagListDelegate = self;
        }
            break;
        case SquareCellTypeNormal:
        {
            SOSquareTimelineLayout *lout = (SOSquareTimelineLayout *)layout;
            SOSquareTimelineCell *timelineCell = (id)cell;
            timelineCell.userListView.delegate = (id)self;
            timelineCell.delegate = (id)self;
            lout.delegate = self;
            //播放器标识.
            timelineCell.avMixtureView.videoPlayer = self.player;
            timelineCell.player = self.player;
            
            //在feed广场下进行曝光
            NSString *ratio = [NSString stringWithFormat:@"%.2f",[self calculatePostExposePercent:timelineCell]];
            if (ratio.floatValue > 0) {
                @weakify(timelineCell);
                SOWeakIfy(self);
                [self.exposeManager registerCellExposeByPercent:ratio.floatValue withTarget:timelineCell handler:^(SOBizAction * _Nonnull bizAction) {
                    SOStrongIfy(self);
                    @strongify(timelineCell);
                    SOSquareTimelineLayout *timelayout = timelineCell.layout;
                    
                    if (bizAction.isAnswer && !timelayout.isPercentPostWatch) {
                        //出现了
                        NSLog(@"ratio___%.2f",bizAction.limit);
                        timelayout.isPercentPostWatch = YES;
                        if (self.routerType == SubSquareRouterTypeMainRecommand || self.routerType == SubSquareRouterTypeMainLocation) {
                            [[SOIntelligentModelManager shareInstance] addPostWatchInterfaceType:0 withHandler:^(SOLittleInterfaceModel * _Nonnull actionModel) {
                                actionModel.postJsonStr = [timelayout.post yy_modelToJSONString];
                                actionModel.postId = SOString(timelayout.post.postId);
                                actionModel.sceneType = SOSquareBehaviorSceneTypeHalfPercentWatch;
                            }];
                        }
                        /* 帖子50%曝光曝光埋点 */
                        [SoulEvent eventExpose:@"Square_Post50Watch"
                                        params:@{@"pId": SOTrackString(timelayout.post.postId),@"algExt": SOTrackString(timelayout.post.algExt),
                                                 @"clientTrackInfo": SOTrackString(timelayout.post.clientTrackInfo)
                                               }
                                        pageId: nil pagePrama:nil];
                        NSLog(@"ratio___出现_%@ %ld",timelayout.post.signature,timelayout.indexPath.section);
                    }
                }];
            }
        } break;
            
        case SquareCellTypeRecommandChatRooms:
        {
            SORecommandSquareChatCell *chatRoomsCell = (id)cell;
            chatRoomsCell.indexPath = indexPath;
            chatRoomsCell.delegate = (id)self;
        }
            break;
        case SquareCellTypePrivacyTagSimilar:
        {
            SOSquareTagSimilarCell *tagSimilarCell = (id)cell;
            tagSimilarCell.delegate = (id)self;
        }
            break;
        case SquareCellTypeSearchSquareUserInfo:
        {
            SASquareSearchNewUserCell *newUserCell = (id)cell;
            newUserCell.delegate = self;
        }
            break;
        case SquareCellTypeSearchSquareUserList:
        {
            SASquareSearchUserListCell *userListCell = (id)cell;
            userListCell.delegate = self;
        }
            break;
        case SquareCellTypeSearchSquareNormalUserInfo:
        {
            /* 应为已废弃的类型 */
        }
            break;
        case SquareCellTypeSearchSquareTagInfo:
        {
            SOSearchTagCell *tagInfoCell = (id)cell;
            tagInfoCell.delegate = (id)self;
        }
            break;
        case SquareCellTypeSearchTitle:
        {
            SOSearchSquareTitleCell *titleCell = (id)cell;
            titleCell.delegate = (id)self;
        }
            break;
        case SquareCellTypeSearchTagCombo:
        {
            SOSearchComboTagCell *comboTagCell = (id)cell;
            comboTagCell.delegate = (id)self;
        }
            break;
            
        case SquareCellTypeSearchTagContent:
        {
            SOSearchTagContentCell *contentCell = (id)cell;
            contentCell.delegate = self;
        }
            break;
            
        case SquareCellTypeSearchGroupChatCombo:
        {
            SOSearchGroupChatComboCell *groupChantCell = (id)cell;
            groupChantCell.delegate = (id)self;
        }
            break;
            
        case SquareCellTypeSearchGroupChatSingle:
        {
            SOSearchGroupChatSingleCell *groupChantCell = (id)cell;
            groupChantCell.delegate = (id)self;
        }
            break;
            
        case SquareCellTypeSearchGroupChatSimple://搜索-综合-置顶单个派对卡片
        {
            SOSearchGroupChatSimpleCell *chatCell = (id)cell;
            chatCell.delegate = (id)self;
        }
            break;
            
        case SquareCellTypeSearchMusicComment:
        {
            SOSearchMusicCommentCell *musicCommentCell = (id)cell;
            musicCommentCell.delegate = (id)self;
        }
            break;
        case SquareCellTypeSoulerList:
        {
            SOTopicSquareSoulerTabCell *soulerTabCell = (id)cell;
            soulerTabCell.delegate = (id)self;
        }
            break;
        case SquareCellTypeSearchGroupChat:
        {
            SOSearchTextGroupChatCell *groupChatCell = (id)cell;
            // 9: 搜索
            groupChatCell.source = 9;
            @weakify(self);
            if (groupChatCell.joinInCallBack == nil){
                groupChatCell.joinInCallBack = ^(id<SOGCMainSquareItemModelProtocol>  _Nonnull model) {
                    NSInteger type = 1;
                    if(model.joinStatus == 3 || model.joinStatus == 4) {
                        type = 2;
                    }
                    @strongify(self)
                    /* 搜索结果页-群组申请/加入点击点击埋点 */
                    [SoulEvent eventClick:@"SearchRessultSquare_GroupJoinClk"
                                   params:@{
                        @"GroupId": model.groupId,
                        @"searchId": self.searchId ?: @"",
                        @"position": @(model.position ?: 0),
                        @"pSearch": model.pSearch ?: @"",
                        @"tab_id": @(5),
                        @"Type": @(type)
                    }
                                   pageId:PostSquare_SearchResult pagePrama:nil];
                };
                
            }
            
        }
            break;
            
        default:{
            
        }
            break;
    }
}

- (CGFloat)calculatePostExposePercent:(SOSquareTimelineCell *)cell {
    CGFloat ratio = 0.5;
    SOSquareTimelineLayout *layout = cell.layout;
    if (![cell isKindOfClass:SOSquareTimelineCell.class] || layout.height <= 60) {
        return 0.0;
    }
    //头像的比例
    CGFloat headRatio = 60.0/layout.height;
    ratio += headRatio;
    return ratio;
}

- (void)respondseRoomCellEventControls:(UITableViewCell<SOChatRoomSearchResultCellProtocol> *)chatRoomCell withContainer:(UIViewController *)containerVC {
    SOWeakIfy(containerVC);
    SOWeakIfy(chatRoomCell);
    chatRoomCell.followBtnClickBlock = ^(UIButton * _Nonnull button, SOChatRoomSearchModel * _Nonnull searchModel) {
        SOStrongIfy(chatRoomCell);
        BOOL hasFollow = searchModel.userModel.hasFollow;
        NSString *userId = @(searchModel.userModel.userId).stringValue;
        if (hasFollow) {
            SOEnterPrivateChatModel *chatModel = [[SOEnterPrivateChatModel alloc] init];
            chatModel.chatId = userId;
            chatModel.headIcon = searchModel.userModel.avatarName;
            chatModel.headBGColor = searchModel.userModel.avatarColor;
            chatModel.chatFrom = @"";
            id<SOEnterPrivateChatManagerProtocol> manager = [ZIKRouterToService(SOEnterPrivateChatManagerProtocol) makeDestination];
            [manager enterPrivateChatWithChatModel:chatModel];
        } else {
            [[SOUserFunctionRequest sharedInstance] followUserById:userId
                                                         onSuccess:^{
                searchModel.userModel.hasFollow = !hasFollow;
                [chatRoomCell.userInfoView so_invoke:@selector(configData:) arguments:SOManiFold(searchModel.userModel)];
                [MBProgressHUD showMessage:@"关注成功" toView:nil];
            } onFailure:^(NSInteger statusCode, NSString *msg) {
                
            } onFinish:^{
                
            }];
        }
    };
    
    chatRoomCell.remindBtnClickBlock = ^(UIButton * _Nonnull button, SOChatRoomSearchModel * _Nonnull searchModel) {
        SOStrongIfy(chatRoomCell);
        BOOL hasRemind = searchModel.userModel.hasRemind;
        [SOChatRoomRequest setReminderByRoomId:@""
                                   ownerEcptId:[SoulUserIDUtils getEcptId:@(searchModel.userModel.userId).stringValue]
                                          type:hasRemind ? @"1" : @"0"
                                       success:^(SoulResponseCommonModel * _Nonnull model) {
            if (model.success) {
                NSInteger code = [model.data so_integerForKey:@"code"];
                searchModel.userModel.hasRemind = code != 3;
                [chatRoomCell.userInfoView so_invoke:@selector(configData:) arguments:SOManiFold(searchModel.userModel)];
                if (code != 3) {
                    [MBProgressHUD showMessage:@"你已打开派对提醒，TA下次创建派对时，你会第一时间收到通知" toView:nil];
                }
            }
        } failure:^(NSInteger status, NSString * _Nullable msg) {
            [MBProgressHUD showError:@"请检查你的网络" toView:nil];
        }];
    };
    
    chatRoomCell.tapOnChatRoomBlock = ^(SOChatRoomSearchModel * _Nonnull searchModel) {
        SOStrongIfy(containerVC);
        if (searchModel.roomModel.roomId.length) {
            
            id<ChatRoomLifeUtilProtocol> chatRoomLifeManager = [ZIKRouterToService(ChatRoomLifeUtilProtocol) makeDestination];
            [chatRoomLifeManager enterChatRoomWithRoomId:searchModel.roomModel.roomId optionBlock:^(NSObject<ChatRoomEnterOptionProtocol> *option) {
                option.joinType = SOChatRoomJoinFromTypeDefault;
                option.navVC = containerVC.navigationController;
                option.joinCode = JOINCODE(JC_SQUARE, JCS_POST, nil);
            } completion:nil];
        }
    };
    
    chatRoomCell.tapOnUserAvatarBlock = ^(SOChatRoomSearchModel * _Nonnull searchModel) {
        SOStrongIfy(containerVC);
        if (searchModel.userModel.userId == [SOUserInfoManager sharedInstance].userId.integerValue) {
            UIViewController<FeelingViewControllerProtocol> *targetVC = (UIViewController<FeelingViewControllerProtocol>*)[ZIKRouterToView(FeelingViewControllerProtocol) makeDestination];
            [containerVC.navigationController pushViewController:targetVC animated:YES];
        } else {
            UIViewController<StrangerViewControllerProtocol> *targetVC = (UIViewController<StrangerViewControllerProtocol>*)[ZIKRouterToView(StrangerViewControllerProtocol) makeDestination];
            targetVC.userID = @(searchModel.userModel.userId).stringValue;
            [containerVC.navigationController pushViewController:targetVC animated:YES];
        }
    };
    
}

#pragma mark - SATableViewAdaptorDelegate

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    return [UITableViewCell new];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    SOSquareTimelineBaseLayout *layout = (SOSquareTimelineBaseLayout *)[self.tableViewModel cellModelForIndexPath:indexPath];
    id<SubSquareRouterProtocol> subSquareRouter = [ZIKRouterToService(SubSquareRouterProtocol) makeDestination];
    if (layout.cellType == SquareCellTypeSearchTag) {
        SOTagModel *tag = (SOTagModel *)layout.dataModel;
        
        /* 全部tab-相关标签-tag卡片点击埋点 */
        [SoulEvent eventClick:@"SearchResultSquare_AllRelatedTagCard"
                       params:@{@"searchId" : SOTrackString(tag.pSearch)}
                       pageId:PostSquare_SearchResult pagePrama:nil];
        
        UIViewController *topicVC = [subSquareRouter routeToTopicSquareViewControllerWithTopicName:tag.tagName];
        [self.navigationController pushViewController:topicVC animated:YES];
        return;
    }
    SquareTopicSimilarityLayout * similarityLayout = (SquareTopicSimilarityLayout *)[self.tableViewModel cellModelForIndexPath:indexPath];
    if (layout.cellType == SquareCellTypeSimilarity) {
        SquareTopicSimilarityModel *similarityModel = similarityLayout.similarModel;
        [SoulEvent eventClick:@"TagSquare_RelatedTag" params:@{@"tId" :  similarityModel.tagId ?: @""} pageId:PostSquare_Tag pagePrama:nil];
        /// tagName标记，以待以后使用
        if ([similarityModel.tagName isEqualToString:similarityModel.name]) return;
        UIViewController * targetVC = [subSquareRouter routeToTopicSquareViewControllerWithTopicName:similarityModel.name];
        [self.navigationController pushViewController:targetVC animated:YES];
    }
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    [self.presenter eventCellWillDisplayWithIndexPath:indexPath cell:cell];
    
    BOOL isRecommandScene = self.routerType == SubSquareRouterTypeMainRecommand;
    if (isRecommandScene && [cell isKindOfClass:SOSquareTimelineCell.class] && [[SOHQualitPostApplyManager shareInstance] canApplayQualit] && indexPath.section > 0) {
        NSTimeInterval time = [[MMKV defaultMMKV] getDoubleForKey:@"kPostCanBeRecommendedToastTimeIntervalKey" defaultValue:0];
        NSTimeInterval now = [[NSDate alloc] init].timeIntervalSince1970;
        BOOL dateAvalid = (now - time) > 30 * 24 * 60 * 60;
        if (dateAvalid) {
            id layout = [self.tableViewModel cellModelForIndexPath:indexPath];
            SOSquareTimelineLayout *timelineLayout = nil;
            if ([layout isKindOfClass:SOSquareTimelineLayout.class]) timelineLayout = layout;
            if (timelineLayout == nil) return;
            SOPost *post = timelineLayout.post;
            if (!post.recommendInfo.postQualityModel.highQuality) {
                SOSquareTimelineCell *timeCell = (SOSquareTimelineCell *)cell;
                [[NSNotificationCenter defaultCenter] postNotificationName:@"kPostCanBeRecommendedToastNotification" object:nil userInfo:@{@"cell" : timeCell ?: SOBaseModel.new}];
                [[MMKV defaultMMKV] setDouble:now forKey:@"kPostCanBeRecommendedToastTimeIntervalKey"];
            }
        }
    }
    
    if ([cell isKindOfClass:SOSquareTimelineCell.class] && self.routerType == SubSquareRouterTypeMainRecommand) {
        //推荐广场
        //1.满足点击要求
        BOOL hasNoBehaver = ![self checkHasBehavor];
        SOSquareTimelineCell *postCell = (SOSquareTimelineCell *)cell;
        SOSquareTimelineLayout *layout = postCell.layout;
        if (layout.needLongPressGudide && hasNoBehaver) {
            postCell.longPressGuide.hidden = NO;
            [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(removeLongPressGuide:) object:postCell];
            [self performSelector:@selector(removeLongPressGuide:) withObject:postCell afterDelay:5.0];
        } else {
            postCell.longPressGuide.hidden = YES;
        }
    }
}

- (BOOL)checkHasBehavor {
    BOOL behaver = NO;
    if (self.tableViewModel.sectionModelArray.count < 4) {
        behaver = YES;
        return behaver;
    }
    
    for (int i = 0; i < 3; i++) {
        SOTableViewSectionModel *sectionModel = self.tableViewModel.sectionModelArray[i];
        for (id layout in sectionModel.sectionItems) {
            if (![layout isKindOfClass:SOSquareTimelineLayout.class]) {
                continue;
            }
            SOSquareTimelineLayout *timeLineLayout = (SOSquareTimelineLayout *)layout;
            NSString *postId = SOString(timeLineLayout.post.postId);
            for (SOLittleInterfaceModel *model in [SOIntelligentModelManager shareInstance].interactionActions) {
                BOOL action = model.sceneType == 1 || model.sceneType == 3 || model.sceneType == 5 || model.sceneType == 7 || model.sceneType == 10 || model.sceneType == 11;
                if ([model.postId isEqualToString:postId] && action) {
                    behaver = YES;
                    break;
                }
            }
            if (behaver == YES) {
                break;
            }
        }
        if (behaver == YES) {
            break;
        }
    }
    return behaver;
}

- (void)removeLongPressGuide:(SOSquareTimelineCell *)cell {
    cell.longPressGuide.hidden = YES;
    cell.layout.needLongPressGudide = NO;
    [[MMKV defaultMMKV] setBool:YES forKey:@"long_press_guide_key"];
}

- (void)tableView:(UITableView *)tableView didEndDisplayingCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    [self.presenter eventEndCellDisplayWithIndexPath:indexPath cell:cell];
}

#pragma mark - SquareRecommandCellDelegate
- (void)squareRecommandCell:(SquareRecommandCell *)cell didClickMoreButton:(UIButton *)button {
    if (self.routerType == SubSquareRouterTypeMainRecommand) {
        NSArray *recTags = cell.layout.tags;
        [self gotoRecommandTag:recTags];
    }
}

- (void)gotoRecommandTag:(NSArray *)recTags {
    RecommandTagsViewController *moreRcommandVC = [[RecommandTagsViewController alloc] initWithRecommandTags:recTags];
    [moreRcommandVC setHidesBottomBarWhenPushed:YES];
    [self.navigationController pushViewController:moreRcommandVC animated:YES];
}

#pragma mark - RecommandTagCellDelegate
- (void)tagCellDidClick:(RecommandTagCell *)cell {
    RecommandTagLayout *mediaTagLayout = cell.layout;
    id<SubSquareRouterProtocol> subSquareRouter = [ZIKRouterToService(SubSquareRouterProtocol) makeDestination];
    UIViewController *topicVC = [subSquareRouter routeToTopicSquareViewControllerWithTopicName:mediaTagLayout.mediaTagModel.tagName];
    [self.navigationController pushViewController:topicVC animated:YES];
}

- (void)tagCell:(RecommandTagCell *)cell didClickImageAtIndex:(NSUInteger)index {
    RecommandTagLayout *mediaTagLayout = cell.layout;
    if (mediaTagLayout.tagType == SOTagTypeMediaNoraml) {
        
        NSDictionary *params = @{@"tId"   : mediaTagLayout.mediaTagModel.tagId ?: @"",};
        [SoulEvent eventClick:@"RecommendSquare_CardTagClick" params:params pageId:self.dt_pageId pagePrama:nil];
        id<SubSquareRouterProtocol> subSquareRouter = [ZIKRouterToService(SubSquareRouterProtocol) makeDestination];
        UIViewController *topicVC = [subSquareRouter routeToTopicSquareViewControllerWithTopicName:mediaTagLayout.mediaTagModel.tagName];
        [self.navigationController pushViewController:topicVC animated:YES];
    }
    if (mediaTagLayout.tagType == SOTagTypeAdvertisement) {
        
        NSDictionary *params = @{@"tId"  : @"pic",};
        [SoulEvent eventClick:@"RecommendSquare_CardTagClick" params:params pageId:self.dt_pageId pagePrama:nil];
        
        SOMediaTagAdvertisement *tagAdvertisement = mediaTagLayout.advertiseTag;
        [self routeWithAdvertisementTag:tagAdvertisement];
    }
}

- (void)tagCellDidClickCloseButton:(RecommandTagCell *)cell {
    RecommandTagLayout *mediaTagLayout = cell.layout;
    NSString *tagId = nil;
    BOOL isAdvertiseTag = NO;
    if (mediaTagLayout.tagType == SOTagTypeMediaNoraml) {
        SOMediaTagModel *tagModel = mediaTagLayout.mediaTagModel;
        tagId = tagModel.tagId;
        isAdvertiseTag = NO;
        NSDictionary *params = @{@"tId"  : tagModel.tagId ?: @""};
        [SoulEvent eventClick:@"RecommendSquare_CardTagNegative" params:params pageId:self.dt_pageId pagePrama:nil];
    }
    if (mediaTagLayout.tagType == SOTagTypeAdvertisement) {
        SOMediaTagAdvertisement *tagAdvertisement = mediaTagLayout.advertiseTag;
        tagId = tagAdvertisement.tagAdId;
        isAdvertiseTag = YES;
        NSDictionary *params = @{@"tId"  : @"pic",};
        [SoulEvent eventClick:@"RecommendSquare_CardTagNegative" params:params pageId:self.dt_pageId pagePrama:nil];
    }
    UICollectionView *collectionView = cell.squreRecommandCardcell.tagsView.collectionView;
    SquareRecommandLayout *cardCellLayout = cell.squreRecommandCardcell.layout;
    __weak NSIndexPath *cardIndexPath = [self.tableView indexPathForCell:cell.squreRecommandCardcell];
    @weakify(collectionView);
    @weakify(self);
    [[SoulerThumbUpActionHelper shareInstance] disLikeRecommandTag:tagId isAd:isAdvertiseTag completeion:^(id data) {
        @strongify(collectionView);
        @strongify(self);
        NSMutableArray *tempTagLayouts = [[NSMutableArray alloc] initWithArray:cardCellLayout.tagLayouts];
        NSInteger index = [tempTagLayouts indexOfObject:mediaTagLayout];
        [tempTagLayouts removeObject:mediaTagLayout];
        
        NSMutableArray *tempTags = [cardCellLayout.tags mutableCopy];
        if (index < tempTags.count) {
            [tempTags removeObjectAtIndex:index];
        }
        cardCellLayout.tags = tempTags;
        
        if (tempTagLayouts.count == 0) {
            cardCellLayout.tagLayouts = nil;
            [collectionView reloadData];
            
            if (self.routerType == SubSquareRouterTypePostDetail) {
                self.managerDeleteBlock(cardIndexPath);
            } else {
                if (cardIndexPath.section < self.tableViewModel.sectionModelArray.count) {
                    [self.tableViewModel.sectionModelArray removeObjectAtIndex:cardIndexPath.section];
                }
            }
            [self.tableView reloadData];
        }else{
            cardCellLayout.tagLayouts = tempTagLayouts;
            [collectionView reloadData];
        }
    } failure:^(NSInteger code, NSString *message) {
        [MBProgressHUD showError:message toView:nil];
    }];
}

- (void)tagCellDidClickFollowButton:(RecommandTagCell *)cell {
    RecommandTagLayout *mediaTagLayout = cell.layout;
    if (mediaTagLayout.tagType == SOTagTypeAdvertisement && mediaTagLayout.advertiseTag) {
        [self routeWithAdvertisementTag:mediaTagLayout.advertiseTag];
    }
    if (mediaTagLayout.tagType == SOTagTypeMediaNoraml) {
        SOMediaTagModel *tagModel = mediaTagLayout.mediaTagModel;
        if (tagModel.hasFollow) { // 已经关注之后跳转到广场聚合页
            UIViewController *targetVC = [[SubSquareRouter shareInstance] routeToTopicSquareViewControllerWithTopicName:tagModel.tagName];
            targetVC.hidesBottomBarWhenPushed = YES;
            [self.navigationController pushViewController:targetVC animated:YES];
            return;
        }else{
            [[SoulerThumbUpActionHelper shareInstance] followWithTag:tagModel.tagId ?: @"" isFollow:!tagModel.hasFollow completeion:nil failure:nil];
            if (tagModel.tagId.length > 0) {
                NSDictionary *params = @{@"tId"   : mediaTagLayout.mediaTagModel.tagId ?: @"",
                                         @"action": @"1",};
                [SoulEvent eventClick:@"RecommendSquare_CardTagFollow" params:params pageId:self.dt_pageId pagePrama:nil];
            }
        }
    }
}

- (void)routeWithAdvertisementTag:(SOMediaTagAdvertisement *)advertisement {
    if (advertisement.addressSegueType == SOTagAdvertisementSegueTypeH5) {
        [SOWebManager pushWebVCWithUrl:advertisement.address params:@{} controller:self];
    }
    if (advertisement.addressSegueType == SOTagAdvertisementSegueTypeTagSquare) {
        UIViewController *targetVC = [[SubSquareRouter shareInstance] routeToTopicSquareViewControllerWithTopicName:advertisement.tagName];
        [self.navigationController pushViewController:targetVC animated:YES];
    }
    if (advertisement.addressSegueType == SOTagAdvertisementSegueTypePostDetail) {
        SOTopicInfoViewController *targetVC = [[SOTopicInfoViewController alloc] init];
        targetVC.topicId = advertisement.address;
        targetVC.sourceFrom = self.pageId;
        [self.navigationController pushViewController:targetVC animated:YES];
    }
    if (advertisement.addressSegueType == SOTagAdvertisementSegueTypeUserDetail) {
        id<SoulRootRouterProtocol>destination = [ZIKRouterToService(SoulRootRouterProtocol) makeDestination];
        UIViewController *targetVC = [destination routeToSoulerDetailWithSoulerId:advertisement.address];
        [self.navigationController pushViewController:targetVC animated:YES];
    }
}

#pragma mark - JXPagerViewListViewDelegate

- (UIView *)listView {
    return self.view;
}

- (UIScrollView *)listScrollView {
    return self.tableView;
}

- (void)listViewDidScrollCallback:(void (^)(UIScrollView *))callback {
    self.listScrollViewScrollCallback = callback;
}

#pragma mark - UIScrollView Delegate

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView{
    self.offsetY_last = scrollView.contentOffset.y;
    if ([self.delegate respondsToSelector:@selector(postListVC:scrollViewWillBeginDragging:)]) {
        [self.delegate postListVC:self scrollViewWillBeginDragging:self.tableView];
    }
    [self stopCellPlayEmojiLottie];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    
    !self.listScrollViewScrollCallback ? : self.listScrollViewScrollCallback(scrollView);
    BOOL isDrectionPullDown = [self handleScrollDerectionWithOffset:scrollView.contentOffset.y];
    if ([self.delegate respondsToSelector:@selector(postListVC:scrollViewDidScroll:direction:)]) {
        [self.delegate postListVC:self scrollViewDidScroll:scrollView direction:isDrectionPullDown];
    }
    if (!scrollView.tracking && !scrollView.tracking && !scrollView.decelerating) {
        [self findBestQuickChatViewWithScrollView:scrollView];
    }
    [self.tableViewModel unloadPostAvatarStatusAnimationDidScroll:scrollView];
}

- (BOOL)handleScrollDerectionWithOffset:(CGFloat)offsetY {
    self.currentDerection = (offsetY - self.offsetY_last > 0) ? SOPlayerContainerScrollDerectionUp : SOPlayerContainerScrollDerectionDown;
    self.offsetY_last = offsetY;
    return (self.currentDerection == SOPlayerContainerScrollDerectionDown) ? YES : NO;
}

- (void)scrollViewDidEndScrollingAnimation:(UIScrollView *)scrollView {
    if ([self.delegate respondsToSelector:@selector(postListVC:scrollViewDidEndScrollingAnimation:)]) {
        [self.delegate postListVC:self scrollViewDidEndScrollingAnimation:scrollView];
    }
    /// 滚动停止开始计时打招呼 快捷聊天的展示
    [self findBestQuickChatViewWithScrollView:scrollView];
    
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    [[NSNotificationCenter defaultCenter] postNotificationName:@"PostVideoPlayControlNoti" object:nil userInfo:@{@"state":@1}];
    [self findCellToPlayEmojiLottie];
    /// 滚动停止开始计时打招呼 快捷聊天的展示
    [self findBestQuickChatViewWithScrollView:scrollView];
    if ([self.delegate respondsToSelector:@selector(postListVC:scrollViewDidEndDecelerating:)]) {
        [self.delegate postListVC:self scrollViewDidEndDecelerating:scrollView];
    }
    
    BOOL scrollToScrollStop = !scrollView.tracking && !scrollView.dragging && !scrollView.decelerating;
    if (scrollToScrollStop) {
        if (scrollView.contentOffset.y > 0) {
            [self scrollViewDidEndScroll];
        }
    }
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    if ([self.delegate respondsToSelector:@selector(postListVC:scrollViewDidEndDragging:willDecelerate:)]) {
        [self.delegate postListVC:self scrollViewDidEndDragging:scrollView willDecelerate:decelerate];
    }
    if (decelerate == NO){
        [[NSNotificationCenter defaultCenter] postNotificationName:@"PostVideoPlayControlNoti" object:nil userInfo:@{@"state":@1}];
        [self findCellToPlayEmojiLottie];
    }
    
    if (decelerate == NO) {
        /// 滚动停止开始 计算打招呼的显示 快捷聊天
        [self findBestQuickChatViewWithScrollView:scrollView];
    }
    
    if (!decelerate) {
        BOOL dragToDragStop = scrollView.tracking && !scrollView.dragging && !scrollView.decelerating;
        if (dragToDragStop) {
            if (scrollView.contentOffset.y > 0) {
                [self scrollViewDidEndScroll];
            }
        }
    }
}

- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView withVelocity:(CGPoint)velocity targetContentOffset:(inout CGPoint *)targetContentOffset {
}

- (void)scrollViewDidEndScroll {
    [self.tableViewModel loadPostAvatarStatusAnimation];
}

- (void)mainSquareSelectIndex {
    // 一级广场tab 处理
    SOMainSquareViewController *mainSqaureVC = [self getMainSquareViewController];
    if (mainSqaureVC.segmentView.selectedIndex != 1) {
        [mainSqaureVC.segmentView selectItemAtIndex:1];
    }
}

- (void)tablePanGestureOwnFuction {
    if (self.smoothAnimationChangeVideo) {
        self.smoothAnimationChangeVideo();
        self.smoothAnimationChangeVideo = nil;
    }
}

- (void)scrollToTopWithRefreash:(BOOL)needRefreash {
    if (self.configItem.tabType.integerValue >= 200 && self.configItem.tabType.integerValue < 300) {
        self.isHeaderLoading = NO;
        NSDictionary *userinfo = @{
            @"tabType" : SOString(self.configItem.tabType),
            @"tabName" : SOString(self.configItem.tabName),
            @"pageId" : SOString(self.configItem.pageId),
        };
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kSOTappedSquareTabNotificationKey" object:nil userInfo:userinfo];
    }
    [self.tableViewModel endFooterLoadMore];
    self.tableView.mj_footer.state = MJRefreshStateIdle;
    CGFloat offsetMax = self.tableView.contentOffset.y;
    [self.tableView scrollToTopAnimated:YES];
    
    NSString *logStr = [NSString stringWithFormat:@"scrollToTopWithRefreash isFooterLoading:%@, isHeadRefreshing:%@, needRefresh:%@", @(self.tableViewModel.isFooterLoading), @(self.tableViewModel.isHeadRefreshing), @(needRefreash)];
    [SLogAPI warnWithTag:@"SquareTabRefresh" content:logStr];
    
    if (self.tableViewModel.isFooterLoading || self.tableViewModel.isHeadRefreshing) {
        self.isHeaderLoading = NO;
        return;
    }
    if (!needRefreash){
        self.isHeaderLoading = NO;
        return;
    }
    
    [SLogAPI warnWithTag:@"SquareTabRefresh" content:@"mj_header beginRefreshing"];
    
    if (offsetMax > KScreenHeight && offsetMax <= 3 *KScreenHeight) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self.tableView.mj_header beginRefreshing];
        });
    } else if (offsetMax < KScreenHeight){
        [self.tableView.mj_header beginRefreshing];
    } else {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self.tableView.mj_header beginRefreshing];
        });
    }
}

- (void)scrollToTop {
    [self scrollToTopWithRefreash:YES];
}

- (void)findCellToPlayEmojiLottie {
}

- (void)stopCellPlayEmojiLottie {
}

#pragma mark -- LifeNSNotification

- (void)applicationDidBecomeActiveNotification:(NSNotification *)notification{
    [self resetDisplayTime];
}

- (void)applicationWillResignActiveNotification:(NSNotification *)notification{
    if (!_isNotWatchPost) {
        [self recordDisplayTime];
    }
}


#pragma mark - SOSquareTimelineCellDelegate

- (void)cell:(SOSquareTimelineCell *)cell didClickFilterType:(NSInteger)type itemID:(NSString *)itemID {
    if (!itemID) {
        return;
    }
    
    if (type == 2) {
        self.templateId = itemID;
        [self.photoAlbumView setStartShowWithHalfStart:NO animation:YES];
        [MBProgressHUD showMessage:@"选择照片后，可一键应用美化模板" toView:self.photoAlbumView duration:5.0];
    } else {
        [self.navigationController.view addSubview:[SoulAlertView shareInitWithSoulView]];
    }
    [self.eventHander addClickFastDoorEventWithId:itemID Post:cell.layout.post isFilter:type == 0];
    
    if (type == 0){
        [self.eventExposeManage postEventSameFilterClick:cell.layout.post];
    } else {
        [self.eventExposeManage postEventSameStickerClick:cell.layout.post];
    }
    [self.presenter didClickFilterType:type itemID:itemID];
}

- (void)cellDidClickGiftRecord:(SOSquareTimelineCell*)cell {
    if (![cell checkHitTestPostId]) {
        return;
    }
    SOPost *post = cell.layout.post;
    SOWebModel *model = [[SOWebModel alloc]init];
    SOWebItemModel *itemModel = [[SOWebItemModel alloc]init];
    itemModel.url =  web_url_giftPost;
    itemModel.params = [@{
        @"targetUserIdEcpt" : post.authorIdEcpt ?: @"",
        @"postId" : post.postId ?: @"",
        @"origin" : post.isOneSelf ? @"master" : @"visitor",
        @"avatarName" : post.avatarName ?: @"",
        @"avatarColor" : post.avatarColor ?: @"",
        @"avatarPendant" : post.commodityUrl ?: @"",
        @"signature" : post.signature ?: @"",
    } mutableCopy];
    [model.items addObject:itemModel];
    itemModel.giftBackBlock = ^{
        
    };
    [SOWebManager pushWebVCWithModel:model selectedIndex:0 controller:self];
    [SoulEvent eventClick:@"PostDetail_GiftsAccess" params:nil pageId:nil pagePrama:nil];
}


-(void)cellDidClickVote:(SOSquareTimelineCell *)cell content:(NSString *)content
{
    if (![cell checkHitTestPostId]) {
        return;
    }
    SOPost *post = cell.layout.post;
    [SOSquareTrackHandler eventClickVoteWithPost:post pageInfo:@{@"pageId":self.dt_pageId,@"type":self.dt_pageType}];
    [self.eventExposeManage postEventvoteAreaClick:post];
    NSInteger count = 0;
    for (SOVoteSelectedModel *model in cell.layout.post.voteItemListModel.voteItemModels) {
        count = count + model.selectNum;
    }
    cell.voteCountView.voteCountLabel.text = [NSString stringWithFormat:@"%ld人参与投票",count];
}

- (BOOL)cellBlockClickSchoolBarVote:(SOSquareTimelineCell *)cell content:(NSString *)content {
    return NO;
}

-(void)enterChatRoom:(NSString *)roomId authorId:(NSString *)authorId postId:(nonnull NSString *)postId {
    id<ChatRoomLifeUtilProtocol> chatRoomLifeManager = [ZIKRouterToService(ChatRoomLifeUtilProtocol) makeDestination];
    SOWeakIfy(self);
    [chatRoomLifeManager enterChatRoomWithRoomId:roomId optionBlock:^(NSObject<ChatRoomEnterOptionProtocol> * _Nonnull option) {
        
        NSDictionary *joinRoomExt = @{
            @"shareChannel": @"square",
            @"shareRoomId": roomId ?: @"",
            @"shareUserIdEcpt": authorId ?: @"",
            @"business": @"s-r-j",
        };
        
        option.navVC = self.navigationController;
        option.joinType = SOChatRoomJoinFromTypePostOfRoom;
        option.isRecomandSquare = YES;
        option.joinCode = JOINCODE(JC_SQUARE, JCS_POST, nil);
        option.serverExt = [joinRoomExt jsonStringEncoded];
        option.pid = SOString(postId);
    } completion:^(NSError * _Nonnull error) {
        if (error) {
            SOStrongIfy(self);
            id<SOMediaManagerProtocol> manager = [ZIKRouterToService(SOMediaManagerProtocol) makeDestination];
            if([manager mediaIsBusyingWithFlag:3] || [manager mediaIsBusyingWithFlag:14]) {//视频派对
                DDLogInfo(@"进入聊天室列表错误：%@", error);
                return;
            }
            id<SoulChatOpenUtilsProtocol> openUtils = [ZIKRouterToService(SoulChatOpenUtilsProtocol) makeDestination];
            [openUtils pushToChatMainList:self.navigationController selectFollowTab:NO];
            DDLogInfo(@"进入聊天室列表错误：%@", error);
        }
    }];
}

- (void)cellDidClickToolBar:(SOSquareTimelineCell *)cell {
    
}

- (void)cellDidClick:(SOSquareTimelineCell *)cell {
    if (![cell isKindOfClass:SOSquareTimelineCell.class] ||
        ![cell checkHitTestPostId] ||
        ![cell.layout isKindOfClass:SOSquareTimelineLayout.class]) {
        return;
    }
    SOSquareTimelineLayout *layout = cell.layout;
    [[SOIntelligentModelManager shareInstance] addUserInterfaceModelWithHandler:^(SOLittleInterfaceModel * _Nonnull actionModel) {
        @try {
            actionModel.postJsonStr = [layout.post yy_modelToJSONString];
            actionModel.selected = YES;
            actionModel.sceneType = SOSquareBehaviorSceneTypeEnterPostDetail;
            actionModel.postId = SOString(layout.post.postId);
        } @catch (NSException *exception) {
            NSLog(@"exception_%@",exception);
        }
    }];
    
    [self.eventExposeManage postEventContentClick:layout.post];
    [self gotoTopicDetailVC:cell];
    [self enterHomePageWithPost:layout.post];
}

- (void)cellDidClickFollowUser:(SOSquareTimelineCell *)cell {
    if (![cell isKindOfClass:SOSquareTimelineCell.class] ||
        ![cell checkHitTestPostId] ||
        ![cell.layout isKindOfClass:SOSquareTimelineLayout.class]) {
        return;
    }
    SOSquareTimelineLayout *layout = cell.layout;
    [SOSquareTrackHandler eventFollowWithPost:layout.post
                                     pageInfo:@{@"pageId": SOString(self.pageId),
                                                @"type":self.dt_pageType}];
    __weak typeof(self) wself = self;
    @weakify(self);
    //数据监控
    [[SOBehaviorCollectionManager shareInstance] updateInteractionEventWithType:SOBehaviorActionTypeFollow needPostId:layout.post.postId];
    
    [[SOIntelligentModelManager shareInstance] addUserInterfaceModelWithHandler:^(SOLittleInterfaceModel * _Nonnull actionModel) {
        @try {
            actionModel.postJsonStr = [layout.post yy_modelToJSONString];
            actionModel.selected = YES;
            actionModel.sceneType = SOSquareBehaviorSceneTypeFollow;
            actionModel.postId = SOString(layout.post.postId);
        } @catch (NSException *exception) {
            NSLog(@"exception_%@",exception);
        }
    }];
    
    [[SoulerThumbUpActionHelper shareInstance] followUserWithPost:layout.post isFollow:YES completeion:^(id data) {
        @strongify(self);
        SOSquareTimelineCell *cell = [wself.tableView cellForRowAtIndexPath:layout.indexPath];
        if (![cell isKindOfClass:SOSquareTimelineCell.class]) {
            return;
        }
        layout.post.followed = @(1);
        [layout layout];
        [self.tableView reloadData];
        
        if([self isMemberOfClass:[SoulSquareMainRecommandViewController class]]) {
            // 推荐广场关注成功 提示toast
            [MBProgressHUD showSuccess:@"关注成功" toView:nil];
            [self requestUserListForCell:cell];
        }
    } failure:^(NSInteger code, NSString *message) {
        [MBProgressHUD showError:message toView:nil];
    }];
    return;
}

- (void)cellDidDoubleClick:(SOSquareTimelineCell *)cell {
    if (![cell isKindOfClass:SOSquareTimelineCell.class] ||
        ![cell checkHitTestPostId] ||
        ![cell.layout isKindOfClass:SOSquareTimelineLayout.class]) {
        return;
    }
    SOSquareTimelineLayout *layout = cell.layout;
    
    SOPost *post = layout.post;
    if (!post.id.integerValue) return;
    if (!post.liked.boolValue) {
        [cell doubleClickLike];
    }
    /// 帖子双击点赞
    [SoulEvent eventClick:@"Post_DoubleClick" params:nil pageId:nil pagePrama:nil];
    [SOUserPromptUserDefaut setPostListDoubleClick:YES];
    
    [SOLikeConfigManage.share requestConfigWithDefaultFlag:false resId:post.likeLottieResId successCallBack:^(SOPostLikeLottieConfig * _Nonnull config) {
        LOTAnimationView *view = [[LOTAnimationView alloc] initWithRendingEngine:SOLottieRendeingEngineMainThread];
        view.frame = CGRectMake(0,0, 180, 180);
        view.center = cell.lastTouchPoint;
        [cell addSubview:view];
        @weakify(view);
        [view setAnimationWithZipPath:config.doubleLike.path completeCallback:^(BOOL success) {
            if (!success) return;
            @strongify(view);
            [view playWithCompletion:^(BOOL animationFinished) {
                @strongify(view);
                if (!config.isDefault && post.likeLottieResId) {
                    [SOPostLottieLimitManage.shareInstance addLottiePlayCount:post.likeLottieResId];
                }
                [view removeFromSuperview];
            }];
        }];
    }];
}

- (void)cellDidClickFoldBtnLayout:(SOSquareTimelineCell *_Nullable)cell {
    if (![cell isKindOfClass:SOSquareTimelineCell.class]){
        return;
    }
    SOSquareTimelineLayout *layout = cell.layout;
    SOPost *post = layout.post;
    //订阅号特殊处理
    if (post.local_subscribState) {
        [self gotoTopicDetailVC:cell];
        return;
    }
    
    [SOSquareTrackHandler eventClickPostUnfoldWithPost:post pageInfo:@{@"pageId":self.dt_pageId,@"type" : self.dt_pageType}];
    
    if ([SOABValue(@"216023") isEqualToString:@"c"] || [SOABValue(@"216023") isEqualToString:@"d"]) {
        [self gotoTopicDetailVC:cell sceneType:@"fulltext"];
        return;
    }
    
    layout.fold = !layout.fold;
    
    [[SOIntelligentModelManager shareInstance] addUserInterfaceModelWithHandler:^(SOLittleInterfaceModel * _Nonnull actionModel) {
        @try {
            actionModel.postJsonStr = [layout.post yy_modelToJSONString];
            actionModel.selected = !layout.fold;
            actionModel.sceneType = SOSquareBehaviorSceneTypeUnfoldText;
            actionModel.postId = SOString(layout.post.postId);
        } @catch (NSException *exception) {
            NSLog(@"exception_%@",exception);
        }
    }];
    
    NSIndexPath *indexPath = layout.indexPath;
    @try {
        [self.tableView beginUpdates];
        [self.tableView reloadRowAtIndexPath:indexPath withRowAnimation:UITableViewRowAnimationFade];
        [self.tableView endUpdates];
    } @catch (NSException *exception) {
        [self.tableView reloadData];
    } @finally {
        
    }
}

- (void)cellDidClickBarrage:(SOSquareTimelineCell *)cell {
    /* 音频-封面弹幕点击埋点 */
    [SoulEvent eventClick:@"PostSquare_Recommend_VoiceaddCover_danmu"
                   params:nil
                   pageId:PostSquare_Recommend pagePrama:nil];
    
    
    [self cellDidClickComment:cell];
}

/// 只会出现在推荐广场
- (void)cell:(SOSquareTimelineCell *)cell didClickSouler:(SOPost *)post{
    [self.eventExposeManage postEventTagClick:post type:@"2" tagId:@"-100"];
    /* 推荐广场-带头像Tag点击点击埋点 */
    [SoulEvent eventClick:@"PostSquare_Recommend_UserTag"
                   params:@{@"tId": post.recommendInfo.tagId ?:@"-100",
                            @"tag": post.recommendInfo.tagName ?:@"-100",
                            @"pId": post.postId ?:@"-100",
                            @"algExt" : SOTrackString(post.algExt),
                            @"clientTrackInfo" : SOTrackString(post.clientTrackInfo)
                          }
                   pageId:PostSquare_Recommend pagePrama:nil];
    
    SubSquareTopicViewController *vc = [[SubSquareTopicViewController alloc] initWithTopicName: post.recommendInfo.tagName ?: post.recTag];
    vc.hidesBottomBarWhenPushed = YES;
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)cell:(SOSquareTimelineCell *)cell didClickInLabel:(YYLabel *)label textRange:(NSRange)textRange {
    if (![cell checkHitTestPostId]) {
        return;
    }
    SOSquareTimelineLayout *layout = cell.layout;
    SOPost *post = cell.layout.post;
    NSAttributedString *text = label.textLayout.text;
    if (textRange.location >= text.length) return;
    YYTextHighlight *highlight = [text yy_attribute:YYTextHighlightAttributeName atIndex:textRange.location];
    NSDictionary *info = highlight.userInfo;
    id<SubSquareRouterProtocol> subSquareRouter = [ZIKRouterToService(SubSquareRouterProtocol) makeDestination];
    if (info.count == 0) return;
    DDLogInfo(@"%@",info);
    if (info[kSOCellRouteKeyFoldText]) {
        
        layout.fold = !layout.fold;
        //indexPath有可能为空l
        SOPost *post = cell.layout.post;
        [SOSquareTrackHandler eventClickPostUnfoldWithPost:post pageInfo:@{@"pageId":self.dt_pageId,@"type" : self.dt_pageType}];
        [self.eventExposeManage postEventFoldClick:post isClickOpen:NO];
        
        @try {
            NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
            if (indexPath != nil){
                [self.tableView reloadRowAtIndexPath:indexPath withRowAnimation:UITableViewRowAnimationFade];
            } else {
                [self.tableView reloadData];
            }
        } @catch (NSException *exception) {
            [self.tableView reloadData];
        } @finally {
            
        }
    }
    if ([info[kSOCellRouteKey] isEqualToString:kSOCellRouteKeyTopic]) {
        NSString *tagname = info[kSOCellRouteKeyTopicName];
        tagname = [tagname hasPrefix:@"#"] ? [tagname substringFromIndex:1] : tagname;
        SOPost *post = nil;
        if ([cell isKindOfClass:SOSquareTimelineCell.class]){
            post = cell.layout.post;
        }
        [post.innerTags enumerateObjectsUsingBlock:^(SOInnerTag * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj.name isEqualToString:tagname]) {
                [SOSquareTrackHandler eventClickPostTagWithPost:post pageInfo:@{@"pageId":self.dt_pageId,@"type" : self.dt_pageType,@"tagId" : SOString(obj.tagId)}];
                *stop = true;
            }
        }];
        
        UIViewController *vc = [subSquareRouter routeToTopicSquareViewControllerWithTopicName:tagname];
        [self.navigationController pushViewController:vc animated:YES];
        
    } else if ([info[kSOCellRouteKey] isEqualToString:kSOCellRouteKeyHomePage]) {
        if ([info[kSOCellRouteKeyHomePageName] integerValue] == [[SOUserInfoManager sharedInstance].userId integerValue]||[[SoulUserIDUtils getOriginId:[NSString stringWithFormat:@"%@",info[kSOCellRouteKeyHomePageName]]] integerValue]== [[SOUserInfoManager sharedInstance].userId integerValue]) {
            
            UIViewController<FeelingViewControllerProtocol> *targetVC = (UIViewController<FeelingViewControllerProtocol>*)[ZIKRouterToView(FeelingViewControllerProtocol) makeDestination];
            if ([cell.layout.post.type isEqualToString:@"VIDEO"]) {
                targetVC.selectedTabType = @"VIDEO";
            }
            
            [self.navigationController pushViewController:targetVC animated:YES];
            
        } else {
            UIViewController<StrangerViewControllerProtocol> *otherHomePageVC = (UIViewController<StrangerViewControllerProtocol>*)[ZIKRouterToView(StrangerViewControllerProtocol) makeDestination];
            otherHomePageVC.userID = [SoulUserIDUtils getOriginId:[NSString stringWithFormat:@"%@",info[kSOCellRouteKeyHomePageName]]];
            otherHomePageVC.homepageFrom = @"广场";
            NSString *extMap = [self homePageExtMapWithPost:cell.layout.post];
            if (extMap.length) {
                otherHomePageVC.extMap = extMap;
            }
            
            otherHomePageVC.cTopped = cell.layout.post.cTopped;
            if ([cell.layout.post.type isEqualToString:@"VIDEO"]) {
                otherHomePageVC.selectedTabType = @"VIDEO";
            }
            otherHomePageVC.postId = post.id.stringValue;
            otherHomePageVC.adminTopped = [cell.layout.post.adminTopped boolValue];
            otherHomePageVC.recallSRC = cell.layout.post.recallSRC;
            otherHomePageVC.expIds = cell.layout.post.expIds;
            otherHomePageVC.chatMaidian = [self.tableViewModel getStrangeMaidian];
            otherHomePageVC.post = post;
            [self.navigationController pushViewController:otherHomePageVC animated:YES];
        }
        if ([SOString(info[@"position"]) isEqualToString:@"Text"]){
            [self.eventExposeManage postEventHightTextClick:post hightType:SAPostTextLayoutCompomentType_At atUid:info[kSOCellRouteKeyHomePageName]];
        } else {
            [self.eventExposeManage postEventNickNameClick:post];
        }
    } else if ([info[kSOCellRouteKey] isEqualToString:kSOCellRouteKeyAnonymous]) {
        UIViewController *vc = [subSquareRouter routeToSquareViewControllerWithType:SubSquareRouterTypeNoName];
        [self.navigationController pushViewController:vc animated:YES];
        if ([SOString(info[@"position"]) isEqualToString:@"head"]){
            [self.eventExposeManage postEventNickNameClick:post];
        } else {
            [self.eventExposeManage postEventHightTextClick:post hightType:SAPostTextLayoutCompomentType_Official atUid:nil];
        }
        
    } else if ([info[kSOCellRouteKey] isEqualToString:kAnswerHighlightKey]) {
        SOAnswerSquareViewController *answerSquareViewController = [[SOAnswerSquareViewController alloc] init];
        answerSquareViewController.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:answerSquareViewController animated:YES];
        [self.eventExposeManage postEventHightTextClick:post hightType:SAPostTextLayoutCompomentType_Answer atUid:nil];
        
    } else if ([info[kSOCellRouteKey] isEqualToString:kContributionAssistantHighlightKey]){
        [self showContributionAlert];
        [self.eventExposeManage postEventHightTextClick:post hightType:SAPostTextLayoutCompomentType_ContributionAssistant atUid:nil];
        
    } else if ([info[kSOCellRouteKey] isEqualToString:kSOCellRouteKeyPlanet]) {
        [SOSquareTrackHandler eventClickPlanetNameWithPost:post pageInfo:
         @{@"pageId":self.dt_pageId,
           @"type" : self.dt_pageType
         }];
        UIViewController *vc = [subSquareRouter routeToPlantSquareViewControllerWithPlantName:info[kSOCellRouteKeyPlanetName]];
        [self.navigationController pushViewController:vc animated:YES];
        [self.eventExposeManage postEventNickNameClick:post];
    }
    else if ([info[kSOCellRouteKey] isEqualToString:kSOCellRouteKeyLocation]) {
        UIViewController *vc = [subSquareRouter routeToLocationSquareViewControllerWithLocation:info[kSOCellRouteKeyLocationName] locationStr:post.geoPositionInfo.locationStr];
        [vc setValue:SOString(post.postId) forKey:@"postId"];
        [self.navigationController pushViewController:vc animated:YES];
        [self.eventExposeManage postEventNickNameClick:post];
        
    } else if ([info[kSOCellRouteKey] isEqualToString:kSOCellRouteKeyDiscoverNickName]) {
        if ([SoulUserManager isSelfEcpt:cell.layout.post.authorIdEcpt]) {
            UIViewController<FeelingViewControllerProtocol> *targetVC = (UIViewController<FeelingViewControllerProtocol>*)[ZIKRouterToView(FeelingViewControllerProtocol) makeDestination];
            if ([cell.layout.post.type isEqualToString:@"VIDEO"]) {
                targetVC.selectedTabType = @"VIDEO";
            }
            
            [self.navigationController pushViewController:targetVC animated:YES];
        } else {
            UIViewController<StrangerViewControllerProtocol> *otherHomePageVC = (UIViewController<StrangerViewControllerProtocol>*)[ZIKRouterToView(StrangerViewControllerProtocol) makeDestination];
            otherHomePageVC.userID = [SoulUserIDUtils getOriginId:[NSString stringWithFormat:@"%@",cell.layout.post.authorIdEcpt]];
            otherHomePageVC.postId = [NSString stringWithFormat:@"%@",cell.layout.post.postId];
            otherHomePageVC.homepageFrom = @"广场";
            NSString *extMap = [self homePageExtMapWithPost:cell.layout.post];
            
            if (extMap.length) {
                otherHomePageVC.extMap = extMap;
            }
            
            otherHomePageVC.cTopped = cell.layout.post.cTopped;
            otherHomePageVC.adminTopped = [cell.layout.post.adminTopped boolValue];
            otherHomePageVC.recallSRC = cell.layout.post.recallSRC;
            otherHomePageVC.expIds = cell.layout.post.expIds;
            otherHomePageVC.chatMaidian = [self.tableViewModel getStrangeMaidian];
            otherHomePageVC.post = post;
            if ([cell.layout.post.type isEqualToString:@"VIDEO"]) {
                otherHomePageVC.selectedTabType = @"VIDEO";
            }
            [self.navigationController pushViewController:otherHomePageVC animated:YES];
        }
        [self.eventExposeManage postEventHightTextClick:post hightType:SAPostTextLayoutCompomentType_At atUid:info[kSOCellRouteKeyHomePageName]];
    } else if ([info[kSOCellRouteKey] isEqualToString:kSOCellRouteKeySoulmate]) {
        UIViewController *vc = [subSquareRouter routeToSquareViewControllerWithType:SubSquareRouterTypeSoulmate];
        [self.navigationController pushViewController:vc animated:YES];
        [self.eventExposeManage postEventNickNameClick:post];
    } else if ([info[kSOCellRouteKey] isEqualToString:kSOCellRouteKeyLuckDraw]) {
        // 广场抽奖链接 点击进详情页
        [self cellDidClick:cell];
    } else if ([info[kSOCellRouteKey] isEqualToString:kSOCellRouteKeyIntelligenceWord]) {
        [self cellDidClick:cell];
    } else if ([info[kSOCellRouteKey] isEqualToString:kSOCellRouteKeyLink]) {
        //通用链接
        if (![info[kSOCellRouteKeyLink] isKindOfClass:SOLinkModel.class]) {
            NSAssert(NO, @"link model is error");
        }
        SOLinkModel *linkModel = info[kSOCellRouteKeyLink];
        SOUrlStrRoute(linkModel.linkUrl, nil);
        
        NSMutableDictionary *trackParams = [[NSMutableDictionary alloc] initWithDictionary:@{
            @"pId": SOTrackString(cell.layout.post.postId),
            @"name": SOTrackString(linkModel.linkName),
            @"link_id": SOTrackString(linkModel.linkId)
        }];
        [trackParams addEntriesFromDictionary:linkModel.trackParamDic];
        
        [SoulEvent eventClick:@"officepost_LinkClk"
                       params:trackParams
                       pageId: nil pagePrama:nil];
    }
}

#pragma mark >>>>>>>>>  RouterEventHandler >>>>>>>>>

- (void) __attribute__((annotate("oclint:suppress"))) routerEvent:(NSString *)eventName eventHandler:(id)handler params:(NSDictionary *)params callback:(HanderEvent)handerBlock {
    self.eventBlock = handerBlock;
    
    //新锚点逻辑跳转拦截
    if ([eventName isEqualToString:KCommonAnchorEventIdentifer]) {
        SOSquareTimelineLayout *timeLineLayout = params[@"layout"];
        [self.presenter commonAnrchorClickEventWithLayout:timeLineLayout callBack:^(id  _Nonnull data) {
            
        }];
        return;
    }
    
    //emoji语音同步
    if ([eventName isEqualToString:KSquareAVMixtureViewEventKey]) {
        id layout = params[@"layout"];
        self.eventBlock = handerBlock;
        if (![layout isKindOfClass:SOSquareAVMixtureViewLayout.class]) return;
        SOSquareAVMixtureViewLayout *mixtureLayout = (SOSquareAVMixtureViewLayout *)layout;
        [self cellDidClickAudio:(id)mixtureLayout.cell];
        return;
    }
    
    if ([eventName isEqualToString:KSquareAVMixtureViewSchoolBarAuthenticationEventKey]) {
        self.eventBlock = handerBlock;
        [self schoolBarCellDidClickAudio:nil];
        /// by yuyuan 优化
        return;
    }
    
    if ([eventName isEqualToString:KSquareAVMixtureCoCreateViewEventKey]) {
        id layout = params[@"layout"];
        self.eventBlock = handerBlock;
        if (![layout isKindOfClass:SOSquareAVMixtureViewLayout.class]) return;
        SOSquareAVMixtureViewLayout *mixtureLayout = (SOSquareAVMixtureViewLayout *)layout;
        [self cellDidClickCoCreate:(id)mixtureLayout.cell];
        return;
    }
    if([eventName isEqualToString:SoulAdEvenReloadAd]) {
        return [self handleAdReloadActionWithParams:params];
        
    }
    
    if ([eventName isEqualToString:SoulAdEventDeleteAd]) {
        return [self handleAdDeletedActionWithParams:params];
    }
}

- (void)eventTrackBellLoveAndSoulMatchWithPost:(SOPost *)post type:(NSString *)type {
    
    /* 点击帖子下方推匹配icon点击埋点 */
    [SoulEvent eventClick:@"RecommendSquare_PostMatch"
                   params:@{@"type": type,@"pId": SOString(post.postId)}
                   pageId:PostSquare_Recommend pagePrama:nil];
}


- (void)cell:(SOSquareTimelineCell *)cell didClickTag:(NSString *)tag index:(NSInteger)index {
    SOPost *post = cell.layout.post;
    if (![cell checkHitTestPostId]) {
        return;
    }
    
    TagsModel *tagModel = nil;
    if (index >= 0 && index < post.tags.count) {
        tagModel = post.tags[index];
    }
    
    if (tagModel.isSchoolBar) {
        [self showSchoolBar];
        return;
    }
    tagModel.postId = [NSNumber numberWithString:SOString(post.postId)];
    
    [SOSquareTrackHandler eventClickPostTagWithPost:post pageInfo:@{@"pageId":self.dt_pageId,@"type" : self.dt_pageType,@"tagId" : SOString(tagModel.id.stringValue)}];
    
    [self.eventExposeManage postEventTagClick:post type:@"1" tagId:SOString(tagModel.id.stringValue)];
    
    id<SubSquareRouterProtocol> subSquareRouter = [ZIKRouterToService(SubSquareRouterProtocol) makeDestination];
    NSString *tagName = [tag hasPrefix:@"#"] ? [tag substringFromIndex:1] : tag;
    UIViewController *vc = [subSquareRouter routeToTopicSquareViewControllerWithTopicName:tagName];
    [vc setValue:SOString(tagModel.id) forKey:@"subTagId"];
    
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)schoolBarCellDidClickAudio:(SOSquareTimelineCell * _Nullable)cell{
    if (self.eventBlock) {
        self.eventBlock(nil);
    }
}

- (void)cellDidClickAudio:(SOSquareTimelineCell *)cell {
    if (![cell checkHitTestPostId]) {
        return;
    }
    [self.eventExposeManage postEventAudioClick:cell.layout.post];
    [self.presenter cellDidClickAudio:cell];
}

- (void)cellDidClickSSR:(SOSquareTimelineCell *)cell {
    [self.eventExposeManage postEventSSRIconClick:cell.layout.post];
    [self.presenter cellDidClickSSR:cell];
}

- (void)cellDidClickAnswerTag:(SOSquareTimelineCell *)cell {
    if (![cell checkHitTestPostId]) {
        return;
    }
    SOPost *post = cell.layout.post;
    [SOSquareTrackHandler eventClickImageWithPost:post pageInfo:@{@"pageId":self.dt_pageId,@"type":self.dt_pageType}];
    [self.eventExposeManage postEventImageClick:post];
    NSObject <SoulPostImageBrowserControllerProtocol> *control = KSoulPostImageBrowserController.so_obj;
    
    self.imageBrowserCtrl = control;
    
    NSMutableArray *tempPosts = [NSMutableArray array];
    [tempPosts addObject:post];
    control.showUserDetail = YES;
    [control so_invoke:@selector(loadMorePostWithResult:addUserDetailPage:) arguments:SOManiFold(tempPosts,@(YES))];
    
    NSArray *photos = control.photos;
    SOAttachment *attachment = post.attachments.firstObject;
    if (photos.count <= 0 || !attachment.fileUrl) {
        return;
    }
    //    NSObject <SoulPostImageBrowserViewControllerProtocol>*browserVC = [[KSoulPostImageBrowserViewController.so_class alloc] so_invoke:@selector(initWithController:) arguments:SOManiFold(_imageBrowserCtrl)];
    UIViewController <SoulPostImageBrowserViewControllerProtocol> *browserVC = [ZIKRouterToView(SoulPostImageBrowserViewControllerProtocol) makeDestinationWithConfiguring:^(ZIKViewRouteConfiguration <SoulPostImageBrowserViewControllerModuleRoutable> *_Nonnull config) {
        [config makeDestinationWithController:control];
    }];
    browserVC.isFromSquare = YES;
    browserVC.sourceFrom = self.pageId;
    ///对于部分答案君，answerPicImageView 返回的是部分图片
    if (cell.answerView.answerPicImageView.image && cell.answerView.answerBGImageView.width == cell.answerView.answerPicImageView.width){
        browserVC.sourceAnimationView = cell.answerView.answerPicImageView;
    } else {
        UIImageView *imageV = [cell.answerView viewWithTag:1010] ?: [UIImageView new];
        imageV.tag = 1010;
        imageV.image = [cell.answerView.answerBGImageView snapshotImage];
        imageV.frame  = cell.answerView.answerBGImageView.frame;
        [cell.answerView addSubview:imageV];
        imageV.alpha = 0;
        browserVC.sourceAnimationView = imageV;
    }
    
    browserVC.selectedIndex = 0;
    browserVC.browserDelegate = (id)self;
    if (self.routerType == SubSquareRouterTypeSchoolBarLatest ||
        self.routerType == SubSquareRouterTypeSchoolBarHot ||
        self.routerType == SubSquareRouterTypeSchoolBarActivity) {
        browserVC.isFromSchoolBar = YES;
    }
    SLogInfoWithTag(Square, @"广场帖子列表点击图片进入图片沉浸式 点击答案君 postId:%@", post.postId);
    SONavigationViewController *nav = [[SONavigationViewController alloc] initWithRootViewController:browserVC];
    nav.transitioningDelegate = browserVC;
    nav.modalPresentationStyle = UIModalPresentationOverCurrentContext;
    
    if (self.routerType == SubSquareRouterTypeMainFollow ||
        self.routerType == SubSquareRouterTypeMainRecommand ||
        self.routerType == SubSquareRouterTypeMainRecent || self.routerType == SubSquareRouterTypeMainLocation || self.routerType == SubSquareRouterTypeMainHotTopic) {
        [self.tabBarController presentViewController:nav animated:YES completion:nil];
    }else{
        [self.navigationController presentViewController:nav animated:YES completion:nil];
    }
}

- (void)__attribute__((annotate("oclint:suppress"))) cell:(SOSquareTimelineCell *)cell didClickImageAtIndex:(NSInteger)index {
    SOPost *post = cell.layout.post;
    [self enterHomePageWithPost:post];
    
    if ([post isVideo:index] && post.attachments.count == 1) {
        [self gotoNewFullScreenVideoPlayerWithCell:cell isOpenComment:NO];
        return;
    }
    
    //AI订阅号 到详情
    if (post.local_subscribState) {
        [self gotoTopicDetailVC:cell];
        return;
    }
    
    NSObject <SoulPostImageBrowserControllerProtocol> *control = KSoulPostImageBrowserController.so_obj;
    self.imageBrowserCtrl = control;
    
    BOOL shouldInsert = NO;
    NSMutableArray *tempPosts = [NSMutableArray array];
    BOOL onlyShowCurrentPost = YES;
    if (onlyShowCurrentPost) {
        // 只能查看当前帖子的数据
        [tempPosts addObject:post];
        control.showUserDetail = YES;
        [control so_invoke:@selector(loadMorePostWithResult:addUserDetailPage:) arguments:SOManiFold(tempPosts,@(YES))];
    } else {
        control.showUserDetail = YES;
        // 可以滑动到下一篇帖子
        for (SOTableViewSectionModel *section in self.tableViewModel.sectionModelArray) {
            SOPost *item = section.dataModel;
            if (![item isKindOfClass:[SOPost class]]) continue;
            // 投票类型未处理
            BOOL isImageVote = NO;
            if (item.voteItemListModel && item.voteItemListModel.voteItemModels.count) {
                SOVoteSelectedModel *voteModel = [item.voteItemListModel.voteItemModels firstObject];
                if ([voteModel.type isEqualToString:@"2"]) {
                    isImageVote = YES;
                }
            }
            if (!isImageVote) {
                if (item.attachments.count == 0) continue;
                SOAttachment *attachment = (SOAttachment *)item.attachments.firstObject;
                if (attachment.mediaType == MomentMediaTypeNone || attachment.mediaType == MomentMediaTypeAudio) continue;
            }
            
            if (shouldInsert) {
                [tempPosts addObject:item];
            }else if ([item isEqual:post]) {
                shouldInsert = YES;
                [tempPosts addObject:item];
            }
        }
        [control so_invoke:@selector(loadMorePostWithResult:addUserDetailPage:) arguments:SOManiFold(tempPosts,@(NO))];
    }
    
    if ([control isVideoWithPost:cell.layout.post originalIndex:index]) {
        [self gotoFullScreenVideoPlayer:cell.layout.post withIndex:index];
        return;
    }
    
    if ([SOABValue(@"216023") isEqualToString:@"a"] || [SOABValue(@"216023") isEqualToString:@"d"]) {
        cell.selectedPhotoIndex = index;
        [self gotoTopicDetailVC:cell sceneType:@"image"];
        return;
    }
    
    NSArray *photos = control.photos;
    /** 图片优化需求逻辑 - 投票贴子支持跳转 */
    if (photos.count <= 0) {
        return;
    }
    
    UIViewController <SoulPostImageBrowserViewControllerProtocol> *browserVC = [ZIKRouterToView(SoulPostImageBrowserViewControllerProtocol) makeDestinationWithConfiguring:^(ZIKViewRouteConfiguration <SoulPostImageBrowserViewControllerModuleRoutable> *_Nonnull config) {
        [config makeDestinationWithController:control];
    }];
    //    NSObject <SoulPostImageBrowserViewControllerProtocol>*browserVC = [[KSoulPostImageBrowserViewController.so_class alloc] so_invoke:@selector(initWithController:) arguments:SOManiFold(_imageBrowserCtrl)];
    browserVC.isFromSquare = YES;
    browserVC.sourceFrom = self.pageId;
    if (!cell.mediaView.hidden && index < cell.mediaView.pics.count){
        browserVC.sourceAnimationView = cell.mediaView.pics[index];
        browserVC.selectedIndex = [control getIndexWithPost:cell.layout.post originalIndex:index];
        browserVC.browserDelegate = (id)self;
    } else if (!cell.voteView.hidden && index < cell.voteView.voteView.imageViewArr.count){
        browserVC.sourceAnimationView = cell.voteView.voteView.imageViewArr[index];
        browserVC.selectedIndex = [control getIndexWithPost:cell.layout.post originalIndex:index];
        browserVC.browserDelegate = (id)self;
    }
    
    if (self.routerType == SubSquareRouterTypeSchoolBarLatest ||
        self.routerType == SubSquareRouterTypeSchoolBarHot ||
        self.routerType == SubSquareRouterTypeSchoolBarActivity) {
        browserVC.isFromSchoolBar = YES;
    }
    
    SOWeakIfy(self)
    browserVC.updatelikeBlock = ^{
        SOStrongIfy(self)
        [cell.layout layout];
        if ([self.tableView indexPathForCell:cell]) {
            [self.tableView reloadRowAtIndexPath:[self.tableView indexPathForCell:cell] withRowAnimation:UITableViewRowAnimationFade];
        }else {
            [self.tableView reloadData];
        }
    };
    
    SLogInfoWithTag(Square, @"广场帖子列表点击图片进入图片沉浸式 postId:%@", post.postId);
    SONavigationViewController *nav = [[SONavigationViewController alloc] initWithRootViewController:browserVC];
    
    // 针对iPad iOS18，禁用转场动画，会造成返回后主Tab消失的问题
    if (![self __isIpadIos18]) {
        nav.transitioningDelegate = browserVC;
    }
    
    nav.modalPresentationStyle = UIModalPresentationOverCurrentContext;
    
    if (self.routerType == SubSquareRouterTypeMainFollow ||
        self.routerType == SubSquareRouterTypeMainRecommand ||
        self.routerType == SubSquareRouterTypeMainRecent || self.routerType == SubSquareRouterTypeMainLocation || self.routerType == SubSquareRouterTypeMainHotTopic || self.routerType == SubSquareRouterTypeMainRecCategory || self.routerType == SubSquareRouterTypeFollowed || self.routerType == SubSquareRouterTypeLiked) {
        [self.tabBarController presentViewController:nav animated:YES completion:nil];
    }else{
        [self.navigationController presentViewController:nav animated:YES completion:nil];
    }
    
    [[SOIntelligentModelManager shareInstance] addUserInterfaceModelWithHandler:^(SOLittleInterfaceModel * _Nonnull actionModel) {
        @try {
            actionModel.postJsonStr = [post yy_modelToJSONString];
            actionModel.selected = YES;
            actionModel.sceneType = SOSquareBehaviorSceneTypeEnterPhotoDetail;
            actionModel.postId = SOString(post.postId);
        } @catch (NSException *exception) {
            NSLog(@"exception_%@",exception);
        }
    }];
    
    return;
}

/// 判断是否 iPad + iOS18
- (BOOL)__isIpadIos18 {
    if (@available(iOS 18.0, *)) {
        if ([DeviceInfo getIsIpad]) {
            return YES;
        }
    }
    return NO;
}

///跳转视频沉浸式（带转场）
- (void)gotoNewFullScreenVideoPlayerWithCell:(SOSquareTimelineCell *)cell isOpenComment:(BOOL)isOpenComment {
    SOPost *post = cell.layout.post;
    
    [[SOIntelligentModelManager shareInstance] addUserInterfaceModelWithHandler:^(SOLittleInterfaceModel * _Nonnull actionModel) {
        @try {
            actionModel.postJsonStr = [post yy_modelToJSONString];
            actionModel.selected = YES;
            actionModel.sceneType = SOSquareBehaviorSceneTypeEnterVideoDetail;
            actionModel.postId = SOString(post.postId);
        } @catch (NSException *exception) {
            NSLog(@"exception_%@",exception);
        }
    }];
    
    NSString *requestType = @"RECOMMEND";
    NSString *target = nil;
    if (self.routerType == SubSquareRouterTypeMainRecommand) {
        requestType = @"RECOMMEND";
    } else if (self.routerType == SubSquareRouterTypeMainRecent) {
        requestType = @"RECENT";
    } else if (self.routerType == SubSquareRouterTypeMainFollow) {
        requestType = @"FOLLOW_POST";
    } else if (self.routerType == SubSquareRouterTypeSoulmate) {
        requestType = @"SOULMATE";
    } else if (self.routerType == SubSquareRouterTypeTopic) {
        requestType = @"TAG";
        if ([self.parentViewController isKindOfClass:SubSquareTopicViewController.class]) {
            SubSquareTopicLatestViewModel *model = (SubSquareTopicLatestViewModel *)self.tableViewModel;
            target = SOString(model.tagName);
        }
    }
    
    [[NSNotificationCenter defaultCenter] postNotificationName:@"StopHalfLoveBellAudio" object:nil];
    
    //入参跳转
    NSMutableDictionary *param  = [NSMutableDictionary new];
    if (requestType) {
        param[@"requestType"] = requestType;
    }
    if (target) {
        param[@"target"] = target;
    }
    UIImageView *transitionView = cell.mediaView.currentImageView;
    @weakify(cell);
    SOSquareTimelineLayout *layout = cell.layout;
    id callback = ^(NSDictionary *params) {
        @strongify(cell);
        float progress = [params[@"progress"] floatValue];
        NSString *postId = params[@"postId"];
        SOAttachment *attachment = cell.layout.post.attachments.firstObject;
        SOVideoItem *videoItem = [SOVideoItem new];
        if (post.isLocalModel &&
            ![attachment.fileUrl hasPrefix:@"http"] &&
            ![attachment.fileUrl hasPrefix:@"https"]) {
            videoItem.url = [NSURL fileURLWithPath:SOString(attachment.fileUrl)];
        } else {
            videoItem.url = [NSURL URLWithString:SOString(attachment.fileUrl)];
        }
        
        //同一个视频进度同步
        if ([layout.post.postId isEqual:postId]) {
            layout.transitionsProgress = progress;
        }
    };
    param[@"callback"] = callback;
    param[@"APIScene"] = @"1";
    param[@"progress"] = @(cell.playerProgress);
    param[@"isTransitionAnimation"] = @(1);
    param[@"pageId"] = SOString(self.pageId);
    param[@"isOpenComment"] = @(isOpenComment);
    if (self.routerType == SubSquareRouterTypeTopic) {
        if ([self.parentViewController isKindOfClass:SubSquareTopicViewController.class]) {
            SubSquareTopicViewController *tagTopicVC = (SubSquareTopicViewController*)self.parentViewController;
            param[@"tagId"] = tagTopicVC.tagId;
        }
    }
    [SOCTMediorRoter.new gotoViewControllerWithNavigatior:self.navigationController withModel:post otherParams:param fromTransitionView:transitionView];
    
}

///跳转视频沉浸式
- (void)gotoFullScreenVideoPlayer:(SOPost *)post withIndex:(NSInteger)index {
    
    NSMutableDictionary *param = [NSMutableDictionary new];
    param[@"justOnePlay"] = @(post.attachments.count > 1);
    param[@"selectIndex"] = @(index);
    param[@"pageId"] = SOString(self.pageId);
    
    NSDictionary *tmpParams = @{
        @"navVc": self.navigationController,
        @"post": post,
        @"otherParams": param
    };
    [SOCTMediorCaller soCTMediaPushVc:tmpParams];
    
    [[SOIntelligentModelManager shareInstance] addUserInterfaceModelWithHandler:^(SOLittleInterfaceModel * _Nonnull actionModel) {
        @try {
            actionModel.postJsonStr = [post yy_modelToJSONString];
            actionModel.selected = YES;
            actionModel.sceneType = SOSquareBehaviorSceneTypeEnterVideoDetail;
            actionModel.postId = SOString(post.postId);
        } @catch (NSException *exception) {
            NSLog(@"exception_%@",exception);
        }
    }];
    
    [[SOAudioSessionCategoryManager shareInstance] setCategory:AVAudioSessionCategoryPlayback
                                                         error:nil
                                                     keyObject:nil
                                                      priority:SOAudioSessionCategoryPriorityNormal];
}

- (void)cellDidClickCoCreate:(SOSquareTimelineCell *)cell {
    [self.eventExposeManage postEventCoAuthorIconClick:cell.layout.post];
    [self.presenter cellDidClickCoCreate:cell];
}

- (void)cellDidClickPrivateChat:(SOSquareTimelineCell *)cell {
    [self cellDidClickPrivateChat:cell checkHitTestPostId:YES];
}

//问提贴点击到发布
- (void)cellDidClickQuestionPublish:(SOSquareTimelineCell *)cell {
    SOPost *post = cell.layout.post;
    NSString *url = cell.layout.post.aiAnswerJumpUrl;
    NSString *postAlgExt = [post.algExt stringByURLEncode];
    if (postAlgExt.length > 0) {
        url = [url stringByAppendingFormat:@"&postAlgExt=%@", postAlgExt];
    }
    SOUrlStrRoute(url, nil);
    NSMutableDictionary *params = [NSMutableDictionary initWithExtMap:^(NSMutableDictionary * _Nonnull data) {
        data[@"tUid"] = post.authorIdEcpt;
        data[@"pId"] = post.postId;
        data[@"algext"] = post.algExt;
        data[@"clientTrackInfo"] = post.clientTrackInfo;
    }];
    [SoulEvent eventClick:@"Square_PostAnswer"
                   params:params
                   pageId:nil pagePrama:nil];
}

/// 点击了 拍一拍
- (void)cellDidClickPat:(SOSquareTimelineCell *)cell {
    if (![cell isKindOfClass:SOSquareTimelineCell.class] ||
        ![cell.layout isKindOfClass:SOSquareTimelineLayout.class]) {
        return;
    }
    if (![cell checkHitTestPostId]) {
        return;
    }

    SOSquareTimelineLayout *layout = cell.layout;
    SOPost *post = layout.post;

    if (!post.postId || !post.authorIdEcpt) {
        return;
    }

    // 发送拍一拍轻互动消息
    NSString *targetUserId = [SoulUserIDUtils getOriginId:[NSString stringWithFormat:@"%@", post.authorIdEcpt]];

    // 使用SOPCMessageSender发送轻互动消息
    [[SOPCMessageSender manager] sendCommonMessage:@"IMCartoonNewAction"
                                        contentMap:@{@"pokeState":@0}
                                                to:targetUserId];

    // 发送轻互动提示文案
    [[SOPCMessageSender manager] sendInteractTextWithType:@"clapping_head" to:targetUserId];

    // 埋点统计
    [SoulEvent eventClick:@"Square_PatClick"
                   params:@{@"pId": SOString(post.postId), @"tUid": SOString(post.authorIdEcpt)}
                   pageId:nil pagePrama:nil];
}

- (void)cellDidClickPrivateChat:(SOSquareTimelineCell *)cell checkHitTestPostId:(BOOL)checkHitTestPostId {
    if (![cell isKindOfClass:SOSquareTimelineCell.class] ||
        ![cell.layout isKindOfClass:SOSquareTimelineLayout.class]) {
        return;
    }
    if (checkHitTestPostId) {
        if (![cell checkHitTestPostId]) {
            return;
        }
    }
    SOSquareTimelineLayout *layout = cell.layout;
    
    if (!layout.post.postId) {
        return;
    }
    if (cell.profileView.following) {
        return;
    }
    //帖子详情页
    /* 私聊点击埋点 */
    SOPost *post = layout.post;
    [SOSquareTrackHandler eventClickPostChatWithPost:post pageInfo:@{@"pageId":self.dt_pageId,@"type":self.dt_pageType}];
    [self.eventExposeManage postEventPrivateChatClick:post post_opr_source:@"1"];
    
    cell.profileView.following = YES;
    
    if ([cell isKindOfClass:SOSquareTimelineCell.class]) {
        cell.profileView.following = NO;
    }
    
    //数据监控
    [[SOBehaviorCollectionManager shareInstance] updateInteractionEventWithType:SOBehaviorActionTypePrivateChat needPostId:post.postId];
    
    [[SOIntelligentModelManager shareInstance] addUserInterfaceModelWithHandler:^(SOLittleInterfaceModel * _Nonnull actionModel) {
        @try {
            actionModel.postJsonStr = [post yy_modelToJSONString];
            actionModel.selected = YES;
            actionModel.sceneType = SOSquareBehaviorSceneTypePrivateChat;
            actionModel.postId = SOString(post.postId);
        } @catch (NSException *exception) {
            NSLog(@"exception_%@",exception);
        }
    }];
    
    //数据再次包装,接口未返回algExt字段数据
    NSMutableDictionary *dataDict = [NSMutableDictionary new];
    dataDict[@"post"] = [post transformToDictionary];
    NSMutableDictionary *shareDict = [[NSMutableDictionary alloc]init];
    [shareDict setValue:dataDict forKey:@"sharePost"];
    [shareDict setValue:@"1" forKey:@"LittleAssistant"];
    
    SOEnterPrivateChatModel *chatModel = [[SOEnterPrivateChatModel alloc] init];
    chatModel.chatId = [SoulUserIDUtils getOriginId:[NSString stringWithFormat:@"%@",layout.post.authorIdEcpt]];
    chatModel.headIcon = layout.post.avatarName;
    chatModel.headBGColor = layout.post.avatarColor;
    chatModel.alisas = layout.post.signature;
    chatModel.shareDict = shareDict;
    chatModel.postIdForLimit = [NSString stringWithFormat:@"%@",layout.post.postId];
    chatModel.chatFrom = @"";
    chatModel.fromSquearType = self.routerType;
    
    NSDictionary *map = @{
        @"pId" : SOString(post.postId),
        @"scene" : SOString(self.pageId),
        @"algExt" : SOString(post.algExt),
        @"clientTrackInfo" : SOTrackString(post.clientTrackInfo)
    };
    NSString *extJson = nil;
    if (self.routerType == SubSquareRouterTypeMainRecommand ||
        self.routerType == SubSquareRouterTypeMainLocation ||
        self.routerType == SubSquareRouterTypeMainFollow ||
        self.routerType == SubSquareRouterTypeMainRecCategory
        ) {
        extJson = [map yy_modelToJSONString];
    }
    
    if (extJson.length) {
        chatModel.extJson = extJson;
    }
    if (self.routerType == SubSquareRouterTypeMainRecommand) {
        chatModel.fromPagePathType = SOPCFromPagePathTypeRecommendSquare;
    } else if (self.routerType == SubSquareRouterTypeMainLocation) {
        chatModel.fromPagePathType = SOPCFromPagePathTypeLocationSquare;
    }
    id<SOEnterPrivateChatManagerProtocol> manager = [ZIKRouterToService(SOEnterPrivateChatManagerProtocol) makeDestination];
    [manager enterPrivateChatWithChatModel:chatModel];
    [self enterHomePageWithPost:post];
}

//广告点击事件处理
- (void)tapAdPullMoreActionWithId:(NSString *)adId withIndexPath:(NSIndexPath *)indexPath {
    
}

/// 点击了 更多按钮
- (void)cellDidClickMore:(SOSquareTimelineCell *)cell {
    if (![cell checkHitTestPostId]) {
        return;
    }
    [self.eventExposeManage postEventMoreBtnClick:cell.layout.post];
    [self.presenter cellDidClickMore:cell];
}

- (void)requestUserListForCell:(SOSquareTimelineCell *)cell {
    [self requestUserListForLayout:cell.layout];
}

- (void)cellDidClickAvatar:(SOSquareTimelineCell *)cell {
    [self cellDidClickAvatar:cell checkHitTestPostId:YES];
}

- (void)cellDidClickAvatar:(SOSquareTimelineCell *)cell checkHitTestPostId:(BOOL)checkHitTestPostId {
    if (checkHitTestPostId) {
        if (![cell checkHitTestPostId]) {
            return;
        }
    }
    SOPost *post = cell.layout.post;
    if (post.officialTag == 1) return;
    [self.eventExposeManage postEventUserAvatorClick:post];
    
    [[SOIntelligentModelManager shareInstance] addUserInterfaceModelWithHandler:^(SOLittleInterfaceModel * _Nonnull actionModel) {
        @try {
            actionModel.postJsonStr = [post yy_modelToJSONString];
            actionModel.selected = YES;
            actionModel.sceneType = SOSquareBehaviorSceneTypeEnterHomePage;
            actionModel.postId = SOString(post.postId);
        } @catch (NSException *exception) {
            NSLog(@"exception_%@",exception);
        }
    }];
    
    if (self.routerType == SubSquareRouterTypeMainFollow) {
        NSString *type = (post.sourceType == SOPostTypeFollowRecommand) ? @"1" : @"0";
        [SOSquareTrackHandler eventClickAvatarWithPost:post pageInfo:@{@"pageId":self.dt_pageId,@"type": type}];
    } else if (self.routerType == SubSquareRouterTypeMainLocation) {
        [SOSquareTrackHandler eventClickAvatarWithPost:post pageInfo:@{@"pageId":self.dt_pageId}];
    } else{
        NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
        NSInteger position = indexPath ? indexPath.row : 0;
        NSString *searchId = [self searchId];
        [SOSquareTrackHandler eventClickAvatarWithPost:post pageInfo:
         @{@"pageId":self.dt_pageId,
           @"type": self.dt_pageType,
           @"position": @(position),
           @"searchId": SOString(searchId)
         }];
    }
    
    if (cell.layout.post.soulmate.boolValue) {
        
        UIViewController <SOPostSquareToSoulmateProtocol> *vc = kSOSoulmateViewController.so_obj;
        vc.userId = [SoulUserIDUtils getOriginId:[NSString stringWithFormat:@"%@",cell.layout.post.authorIdEcpt]];
        if ([[SoulUserIDUtils getOriginId:[NSString stringWithFormat:@"%@",cell.layout.post.authorIdEcpt]] integerValue] == [[SOUserInfoManager sharedInstance].userId integerValue])
        {
            vc.isMyself = YES;
        }else{
            vc.isMyself = NO;
        }
        vc.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:vc animated:YES];
        return;
    }
    
    if ([SoulUserManager isSelfEcpt:cell.layout.post.authorIdEcpt]) {
        UIViewController<FeelingViewControllerProtocol> *targetVC = (UIViewController<FeelingViewControllerProtocol>*)[ZIKRouterToView(FeelingViewControllerProtocol) makeDestination];
        if ([cell.layout.post.type isEqualToString:@"VIDEO"]) {
            targetVC.selectedTabType = @"VIDEO";
        }
        [self.navigationController pushViewController:targetVC animated:YES];
    } else {
        UIViewController<StrangerViewControllerProtocol> *otherHomePageVC = (UIViewController<StrangerViewControllerProtocol>*)[ZIKRouterToView(StrangerViewControllerProtocol) makeDestination];
        otherHomePageVC.userID = [SoulUserIDUtils getOriginId:[NSString stringWithFormat:@"%@",cell.layout.post.authorIdEcpt]];
        otherHomePageVC.postId = [NSString stringWithFormat:@"%@",cell.layout.post.id];
        otherHomePageVC.homepageFrom = @"广场";
        NSString *extMap = [self homePageExtMapWithPost:cell.layout.post];
        if (extMap.length) {
            otherHomePageVC.extMap = extMap;
        }
        
        otherHomePageVC.fromSquearType = self.routerType;
        otherHomePageVC.cTopped = cell.layout.post.cTopped;
        otherHomePageVC.adminTopped = [cell.layout.post.adminTopped boolValue];
        otherHomePageVC.recallSRC = cell.layout.post.recallSRC;
        otherHomePageVC.expIds = cell.layout.post.expIds;
        otherHomePageVC.chatMaidian = [self.tableViewModel getStrangeMaidian];
        otherHomePageVC.post = post;
        @weakify(self);
        [otherHomePageVC setBackToSquareHandler:^(SOPost * _Nonnull post) {
            @strongify(self);
            //离开主页做刷新处理
            [self leaveHomePageWithPost:post];
        }];
        if ([cell.layout.post.type isEqualToString:@"VIDEO"]) {
            otherHomePageVC.selectedTabType = @"VIDEO";
        }
        
        otherHomePageVC.updateCell = ^{
            @strongify(self);
            [cell.layout layout];
            if ([self.tableView indexPathForCell:cell]) {
                [self.tableView reloadRowAtIndexPath:[self.tableView indexPathForCell:cell] withRowAnimation:UITableViewRowAnimationFade];
            }
        };
        //进入主页发起请求
        [self enterHomePageWithPost:post];
        [self.navigationController pushViewController:otherHomePageVC animated:YES];
    }
}

- (NSString *)homePageExtMapWithPost:(SOPost *)post {
    NSString *extJson = nil;
    NSDictionary *map = @{
        @"pId" : SOString(post.postId),
        @"scene" : SOString(self.pageId),
        @"algExt" : SOString(post.algExt),
        @"clientTrackInfo" : SOTrackString(post.clientTrackInfo)
    };
    if (self.routerType == SubSquareRouterTypeMainRecommand ||
        self.routerType == SubSquareRouterTypeMainLocation ||
        self.routerType == SubSquareRouterTypeMainFollow ||
        self.routerType == SubSquareRouterTypeMainRecCategory
        ) {
        extJson = [map yy_modelToJSONString];
    }
    return extJson;
}

- (void)cellDidClickSoulmateAvatar:(SOSquareTimelineCell *)cell {
    [self.eventExposeManage postEventUserAvatorClick:cell.layout.post];
    if (cell.layout.post.soulmate.boolValue) {
        UIViewController <SOPostSquareToSoulmateProtocol> *vc = kSOSoulmateViewController.so_obj;
        
        vc.userId = [SoulUserIDUtils getOriginId:[NSString stringWithFormat:@"%@",cell.layout.post.soulmateUserIdEcpt]] ;
        if ([[SoulUserIDUtils getOriginId:[NSString stringWithFormat:@"%@",cell.layout.post.soulmateUserIdEcpt]] integerValue] == [[SOUserInfoManager sharedInstance].userId integerValue])
        {
            vc.isMyself = YES;
        }else{
            vc.isMyself = NO;
        }
        vc.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:vc animated:YES];
        return;
    }
    
    UIViewController<StrangerViewControllerProtocol> *otherHomePageVC = (UIViewController<StrangerViewControllerProtocol>*)[ZIKRouterToView(StrangerViewControllerProtocol) makeDestination];
    otherHomePageVC.post = cell.layout.post;
    otherHomePageVC.homepageFrom = @"广场";
    NSString *extMap = [self homePageExtMapWithPost:cell.layout.post];
    if (extMap.length) {
        otherHomePageVC.extMap = extMap;
    }
    otherHomePageVC.cTopped = cell.layout.post.cTopped;
    otherHomePageVC.adminTopped = [cell.layout.post.adminTopped boolValue];
    otherHomePageVC.recallSRC = cell.layout.post.recallSRC;
    otherHomePageVC.expIds = cell.layout.post.expIds;
    otherHomePageVC.userID = [NSString stringWithFormat:@"%@",[SoulUserIDUtils getOriginId:cell.layout.post.soulmateUserIdEcpt]];
    otherHomePageVC.chatMaidian = [self.tableViewModel getStrangeMaidian];
    if ([cell.layout.post.type isEqualToString:@"VIDEO"]) {
        otherHomePageVC.selectedTabType = @"VIDEO";
    }
    [self.navigationController pushViewController:otherHomePageVC animated:YES];
}

- (void)cellLongPressSuperLike:(SOSquareTimelineCell *)cell {
}

- (void)cellDidClickLike:(SOSquareTimelineCell *)cell {
    if (![cell checkHitTestPostId]) {
        return;
    }
    SOPost *model = cell.layout.post;
    if (self.isLikeRequest) return;
    self.isLikeRequest = YES;
    BOOL isLike = model.isLiked;
    BOOL isDouble = [SOUserPromptUserDefaut postListDoubleClick];
    if (isLike && isDouble) {
        
        [SOUserPromptUserDefaut setPostListDoubleClick:NO];
    }
    NSMutableDictionary *params = [NSMutableDictionary new];
    params[@"type"] = @(model.likeType.integerValue);
    //请求成功会发送刷新状态的通知
    [self superAndCommonPraiseCell:cell model:model isLiked:isLike withParams:params];
    [SOUserKeyBehaviorMonitor.shared stopChecking]; // 识别到关键行为，停止检测
}

- (void)superAndCommonPraiseCell:(SOSquareTimelineCell *)cell model:(SOPost *)model isLiked:(BOOL)isLike withParams:(NSDictionary *)params {
    @weakify(self);
    NSInteger type = [params[@"type"] integerValue]; //type 为0 的时候是普通点赞
    [[SAPostManager shareIntance] post:model isLike:isLike withPrams:params success:^{
        @strongify(self);
        if (self.likePostBlock) {
            self.likePostBlock(isLike);
        }
        self.isLikeRequest = NO;
        [SOSquareTrackHandler eventClickLikeWithPost:model pageInfo:@{@"pageId" : SOString(self.pageId),@"type" : SOString(self.dt_pageType),@"emoji_id" : SOString(@(type))}];
        [self.eventExposeManage postEventPostLikeClick:model isLike:model.liked.boolValue emojId:SOString(@(type))];
    } failuer:^(NSInteger statusCode, NSString *msg) {
        @strongify(self);
        self.isLikeRequest = NO;
        if (![msg isValid]) return;
        [MBProgressHUD showError:msg toView:nil];
    }];
}

- (void)deletePostWithCell:(SOSquareTimelineCell *)cell withAnimation:(BOOL)animationed {
    if (![cell isKindOfClass:SOSquareTimelineCell.class]) return;
    SOSquareTimelineLayout *timelineLayout = cell.layout;
    SOTableViewSectionModel *selectedSectionModel = nil;
    for (SOTableViewSectionModel *sectionModel in self.tableViewModel.sectionModelArray) {
        for (id layout in sectionModel.sectionItems) {
            if (![layout isKindOfClass:SOSquareTimelineLayout.class]) {
                continue;
            }
            
            if ([layout isEqual:timelineLayout]) {
                selectedSectionModel = sectionModel;
                break;
            }
        }
        if (selectedSectionModel) {
            break;
        }
    }
    
    NSInteger selectedIndex = -1;
    selectedIndex = [self.tableViewModel.sectionModelArray indexOfObject:selectedSectionModel];
    if (selectedIndex >= 0 && selectedIndex != NSNotFound) {
        
        [self.tableViewModel.sectionModelArray removeObject:selectedSectionModel];
        UITableViewRowAnimation animationType = animationed ? UITableViewRowAnimationTop : UITableViewRowAnimationNone;
        [self deletePostCell:selectedIndex animationType:animationType];
        
//        //删除关联帖子 因为有动画需要延迟一会
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            SOPost *post = timelineLayout.post;
            [self dealMergeFeedsDeleteByRelatePosts:post];
        });
    }
}

- (void)deletePostWithPostId:(NSString *)postId otherParams:(NSDictionary * _Nullable )params {
    SOTableViewSectionModel *selectedSectionModel = nil;
    SOSquareTimelineLayout *timelineLayout = nil;
    for (SOTableViewSectionModel *sectionModel in self.tableViewModel.sectionModelArray) {
        for (id layout in sectionModel.sectionItems) {
            if (![layout isKindOfClass:SOSquareTimelineLayout.class]) {
                continue;
            }
            timelineLayout = (SOSquareTimelineLayout *)layout;
            if ([timelineLayout.post.postId isEqualToString:postId]) {
                selectedSectionModel = sectionModel;
                break;
            }
        }
        if (selectedSectionModel) {
            break;
        }
    }
    
    NSInteger selectedIndex = -1;
    selectedIndex = [self.tableViewModel.sectionModelArray indexOfObject:selectedSectionModel];
    if (selectedIndex >= 0 && selectedIndex != NSNotFound) {
        
        [self.tableViewModel.sectionModelArray removeObject:selectedSectionModel];
        [self deletePostCell:selectedIndex animationType:UITableViewRowAnimationTop];
    }

//    //删除关联帖子 因为有动画需要延迟一会
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        SOPost *post = timelineLayout.post;
        [self dealMergeFeedsDeleteByRelatePosts:post];
    });
}

- (void)deletePostCell:(NSInteger)section animationType:(UITableViewRowAnimation)animationType {
    @weakify(self);
    @try {
        [self.tableView performBatchUpdates:^{
            @strongify(self);
            [self.tableView deleteSection:section withRowAnimation:animationType];
        } completion:^(BOOL finished) {
            
        }];
    } @catch (NSException *exception) {
        NSLog(@"square_exception: %@",exception);
    }
}

#pragma mark -- 长按负反馈
- (void)cellLongPressFeedBackWithGesture:(UILongPressGestureRecognizer *)gesture withCell:(SOSquareTimelineCell *)cell {
    [self.presenter cellLongPressFeedBackWithGesture:gesture withCell:cell];
}

- (void)cellDidClickCollect:(SOSquareTimelineCell *)cell completionBlock:(nonnull dispatch_block_t)completionBlock {
    [self cellDidClickCollect:cell completionBlock:completionBlock flag:0];
}

- (void)cellDidClickCollect:(SOSquareTimelineCell *)cell completionBlock:(nonnull dispatch_block_t)completionBlock flag:(NSInteger)flag {
    
    if (flag == 0) {
        SOPost *post = cell.layout.post;
        [SOSquareTrackHandler eventClickCollectWithPost:post pageInfo:@{@"pageId":self.dt_pageId,@"type" : self.dt_pageType}];
    }
    
    SOPost *model = cell.layout.post;
    if (self.isCollectRequest) {
        return;
    }
    self.isCollectRequest = YES;
    BOOL isCollect = model.isCollected;
    @weakify(self);
    [[SAPostManager shareIntance] posts:model isCollect:isCollect success:^{
        @strongify(self);
        self.isCollectRequest = NO;
    } failuer:^(NSInteger statusCode, NSString *msg) {
        @strongify(self);
        self.isCollectRequest = NO;
        [MBProgressHUD showError:msg toView:nil];
    }];
}

- (void)cellDidClickFastComment:(SOSquareTimelineCell *)cell completionBlock:(dispatch_block_t)completionBlock {
    SOPost *post = cell.layout.post;
    [SOSquareTrackHandler eventClickCommentBoxWithPost:post pageInfo:@{@"pageId":self.dt_pageId,@"type" : self.dt_pageType}];
    [self popCommentKeyboard:cell replyName:nil];
}

- (void)popCommentKeyboard:(SOSquareTimelineCell*)cell replyName:(NSString *_Nullable)replyName {
    self.keyboardCell = cell;
    SOPost *post = cell.layout.post;
    self.keyboardVC.postModel = post;
    @weakify(self);
    
    [self.keyboardVC setSendBlock:^(NSString *content, id model) {
        
    }];
    
    [self.keyboardVC setSendGift:^{
        @strongify(self);
        [self showGiftVCWithPost:cell.layout.post cell:cell];
    }];
    
    if (self.navigationController.presentedViewController == nil) {
        UINavigationController *nav = self.keyboardVC.navigationController;
        if (!nav) {
            nav = [[UINavigationController alloc] initWithRootViewController:self.keyboardVC];
        }
        self.keyboardVC.modalPresentationStyle = UIModalPresentationOverFullScreen;
        nav.modalPresentationStyle = self.keyboardVC.modalPresentationStyle;
        [self presentViewController:nav animated:NO completion:NULL];
    }
    return;
}

- (void)cellDidClickShare:(SOSquareTimelineCell *)cell {
    if (![cell checkHitTestPostId]) {
        return;
    }
    SOPost *post = cell.layout.post;
    [SOSquareTrackHandler eventClickShareWithPost:post pageInfo:@{@"pageId":self.dt_pageId,@"type" : self.dt_pageType}];
    
    [self.eventExposeManage postEventShareClick:post];
    SOPost *model = cell.layout.post;
    NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
    [self.presenter shareFuncHandler:model.postId withIndexPath:indexPath otherModel:model];
    
    [SOUserKeyBehaviorMonitor.shared stopChecking]; // 识别到关键行为，停止检测
}

- (void)cellDidClickComment:(SOSquareTimelineCell *)cell {
    if (![cell checkHitTestPostId]) {
        return;
    }
    [self.eventExposeManage postEventCommentButtonClick:cell.layout.post];
    
    [self.presenter cellDidClickCommentToTopic:cell routerType:self.routerType];
    
    [SOUserKeyBehaviorMonitor.shared stopChecking]; // 识别到关键行为，停止检测
}

- (void)cellDidClickLocation:(SOSquareTimelineCell *)cell {
    if (![cell checkHitTestPostId]) {
        return;
    }
    SOPost *post = cell.layout.post;
    [SOSquareTrackHandler eventClickLocationWithPost:post pageInfo:@{@"pageId":self.dt_pageId,@"type" : self.dt_pageType}];
    [self.eventExposeManage postEventLocationClick:post];
    
    id<SubSquareRouterProtocol> subSquareRouter = [ZIKRouterToService(SubSquareRouterProtocol) makeDestination];
    UIViewController *vc = [subSquareRouter routeToLocationSquareViewControllerWithLocation:cell.layout.post.geoPositionInfo.position locationStr:post.geoPositionInfo.locationStr];
    [vc setValue:SOString(post.postId) forKey:@"postId"];
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)cellDidClose:(SOSquareTimelinePromptCell *)cell {
    if ([cell.titleLabel.text hasPrefix:@"点击#"]) {
        [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"USER1"];
    }
    NSIndexPath *indexpath = [self.tableView indexPathForCell:cell];
    SOTableViewSectionModel *section = self.tableViewModel.sectionModelArray[indexpath.section];
    
    if (self.routerType == SubSquareRouterTypePostDetail) {
        self.managerDeleteBlock(indexpath);
    } else {
        [section.sectionItems removeObjectAtIndex:indexpath.row];
    }
    [self.tableView reloadData];
}


- (void)cellDidClickAdminTop:(SOSquareTimelineCell *)cell {
    
    SOPost *post = cell.layout.post;
    [SOSquareTrackHandler eventClickTopTextWithPost:post pageInfo:@{@"pageId" : self.dt_pageId}];
    id<SubSquareRouterProtocol> subSquareRouter = [ZIKRouterToService(SubSquareRouterProtocol) makeDestination];
    UIViewController *vc = [subSquareRouter routeToSquareViewControllerWithType:SubSquareRouterTypeHistoryTop];
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)cellDidClickResend:(SOSquareTimelineCell *)cell {
    
    if (self.routerType != SubSquareRouterTypeMainRecommand &&
        self.routerType != SubSquareRouterTypeTopic) {
        return;
    }
    if (![cell.layout.post.localStatus isEqualToString:@"fail"]) {
        return;
    }
    /// 兼容线上用户（v3.58.0），后续可删除 @"relase_post_cache_repeat" key相关的,保留带user信息的即可
    NSDictionary *dict = [[NSUserDefaults standardUserDefaults] objectForKey:@"relase_post_cache_repeat"];
    if (dict) {
        [[NSUserDefaults standardUserDefaults] removeObjectForKey:@"relase_post_cache_repeat"];
    }
    NSString *repeatKey = [NSString stringWithFormat:@"relase_post_cache_repeat_%@", [SOUserInfoManager sharedInstance].userId];
    NSDictionary *repeatDict = [[NSUserDefaults standardUserDefaults] objectForKey:repeatKey];
    if (repeatDict) {
        dict = repeatDict;
    }
    NSString *postCacheKey = [NSString stringWithFormat:@"relase_post_cache_%@", [SOUserInfoManager sharedInstance].userId];
    [[NSUserDefaults standardUserDefaults]setValue:dict forKey:postCacheKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    UIViewController <SOReleaseViewControllerProtocol>*vc = KSOReleaseViewController.so_obj;
    
    UINavigationController* nav = [[UINavigationController alloc] initWithRootViewController:vc];
    nav.modalPresentationStyle = 0;
    [self.tabBarController presentViewController:nav animated:YES completion:nil];
    
    NSIndexPath *indexpath = [self.tableView indexPathForCell:cell];
    SOTableViewSectionModel *section = self.tableViewModel.sectionModelArray[indexpath.section];
    
    if (self.routerType == SubSquareRouterTypePostDetail) {
        self.managerDeleteBlock(indexpath);
    } else {
        [section.sectionItems removeObjectAtIndex:indexpath.row];
    }
    [self.tableView reloadData];
}


// qq 音乐被点击
- (void)cellDidClickMusic:(SOSquareTimelineCell *)cell {
    SLogInfoWithTag(Square, @"帖子列表 qq音乐被点击 postId:%@", cell.layout.post.postId);
    [self.eventExposeManage postEventMusicStoryClick:cell.layout.post isJumpToMusicStoryList:YES];
    SOPost* post = cell.layout.post;
    if ([[SOMediaManager sharedInstance] mediaIsBusyingWithFlag:5]) {
        [MBProgressHUD showError:[[SOMediaManager sharedInstance] getMediaIsBusyingToast] toView:nil];
        return;
    }
    post.shouldShowQQMusicGuide = NO;
    [cell.musicView setLayout:cell.layout];
    [self.eventHander reportMusicBuryingPoint:1 post:post];
    [self jumpImmerasiveVCWithPost:cell.layout.post showCommentList:NO];
}

- (void)cellDidClickGift:(SOSquareTimelineCell *)cell {
    SOPost *post = cell.layout.post;
    [self showGiftVCWithPost:post cell:cell];
    
}

- (void)jumpImmerasiveVCWithPost:(SOPost*)post showCommentList:(BOOL)showCommentList {
    NSString *toast = post.songInfoResModel.toast.length > 0 ? post.songInfoResModel.toast : @"音乐故事已下线，无法为您播放";
    [MBProgressHUD showMessage:toast toView:nil];
}

// qq音乐封面被点击
- (void)cellDidClickThumbImageView:(SOSquareTimelineCell *)cell didPlayBtnClick:(UIImageView *)playImageView {
    [self.eventExposeManage postEventMusicStoryClick:cell.layout.post isJumpToMusicStoryList:NO];
    SOPost *post = cell.layout.post;
    NSString *toast = post.songInfoResModel.toast.length > 0 ? post.songInfoResModel.toast : @"音乐故事已下线，无法为您播放";
    [MBProgressHUD showMessage:toast toView:nil];
}

/// 点击快捷聊天
- (void)profileViewDidClickQuickChatBtn:(SOPost *)post view:(UIView *)view{
    if (self.quickChatManage == nil) {
        self.quickChatManage = [SOVCQuickChatManage new];
    }
    self.quickChatManage.post = post;
    self.quickChatManage.viewController = self;
    self.quickChatManage.sendComplete = ^{
        if ([view isKindOfClass:SOSquareTimelineProfileView.class]) {
            //            SOSquareTimelineProfileView *profileView = (SOSquareTimelineProfileView *)view;
            //            post.hadClickSayHelloType = true;
            //            [profileView hiddenQuickChatView];
        }
    };
    [self.quickChatManage quickReplyClick];
    //用户行为收集
    [[SOBehaviorCollectionManager shareInstance] updateInteractionEventWithType:SOBehaviorActionTypePrivateChat needPostId:SOTrackString(post.postId)];
    
    /* 广场快捷打招呼按钮点击点击埋点 */
    [SoulEvent eventClick:@"Square_PostSayhiClk"
                   params:@{@"tUid": SOTrackString(post.authorIdEcpt),
                            @"pId": SOTrackString(post.postId),
                            @"algExt": SOTrackString(post.algExt),
                            @"clientTrackInfo" : SOTrackString(post.clientTrackInfo)
                          }
                   pageId: nil pagePrama:nil];
    
}
#pragma mark - SATagSquareUserListViewDelegate

- (void)tagSquareUserListViewDidClickChangeButton:(SATagSquareUserListView *)userListView {
    /* 为你推荐卡片-换一批按钮点击埋点 */
    [SoulEvent eventClick:@"TagSquare_RecomUserRenew"
                   params:@{@"type": @"2",}
                   pageId:PostSquare_Tag pagePrama:nil];
    
    SOSquareTimelineCell *cell = userListView.cell;
    [self requestUserListForCell:cell];
}

- (void)tagSquareUserListView:(SATagSquareUserListView *)userListView didClickItemAtIndexPath:(NSIndexPath *)indexPath{
    
}

#pragma mark -- SATagSquareUserListCellDelegate

- (void)tagSquareUserListCellDidClickFollow:(SATagSquareUserListCell *)cell {
    
    /* 为你推荐卡片-关注按钮点击埋点 */
    [SoulEvent eventClick:@"TagSquare_RecomUserFollow"
                   params:@{@"type": @"2",@"tUid": cell.model.userId ? : @"",}
                   pageId:PostSquare_Tag pagePrama:nil];
    
    [[SOUserFunctionRequest sharedInstance] followUserById1:cell.model.userIdEcpt onSuccess:^(NSDictionary * _Nonnull dict) {
        cell.model.follow = YES;
        [cell.userListView reloadData];
        [MBProgressHUD showError:@"关注成功" toView:nil];
    } onFailure:^(NSInteger statusCode, NSString * _Nonnull msg) {
        [MBProgressHUD showError:msg toView:nil];
    } onFinish:^{
        
    }];
}

- (void)tagSquareUserListCellDidClickChat:(SATagSquareUserListCell *)cell {
    
    /* 为你推荐卡片-私聊按钮点击埋点 */
    [SoulEvent eventClick:@"TagSquare_RecomUserChat"
                   params:@{@"type": @"2",@"tUid": cell.model.userId ? : @"",}
                   pageId:PostSquare_Tag pagePrama:nil];
    
    //跳转私聊对话框
    SOEnterPrivateChatModel *chatModel = [[SOEnterPrivateChatModel alloc] init];
    chatModel.chatId = cell.model.userId;
    chatModel.headIcon = cell.model.userModel.avatarName;
    chatModel.headBGColor = cell.model.userModel.avatarColor;
    chatModel.alisas = cell.model.userModel.signature;
    id <SOEnterPrivateChatManagerProtocol> manager = [ZIKRouterToService(SOEnterPrivateChatManagerProtocol) makeDestination];
    [manager enterPrivateChatWithChatModel:chatModel];
    
}

- (void)tagSquareUserListCellDidClickClose:(SATagSquareUserListCell *)cell{
    /* 为你推荐卡片-关闭按钮点击埋点 */
    [SoulEvent eventClick:@"TagSquare_RecomUserCancel"
                   params:@{@"type": @"2",@"tUid": cell.model.userId ? : @"",}
                   pageId:PostSquare_Tag pagePrama:nil];
    
    NSMutableArray *temps = [cell.userListView.models mutableCopy];
    if ([temps containsObject:cell.model]) {
        [temps removeObject:cell.model];
        SOSquareTimelineCell *timeLineCell = cell.userListView.cell;
        timeLineCell.layout.users = temps;
        cell.userListView.models = temps;
        [cell.userListView reloadData];
        if (temps.count == 0) {
            [timeLineCell.layout layout];
        }
        [self.tableView reloadData];
    }
}

- (void)tagSquareUserListCellDidClickHeadView:(SATagSquareUserListCell *)cell{
    
    /* 为你推荐卡片-头像点击埋点 */
    [SoulEvent eventClick:@"TagSquare_RecomUserAvatar"
                   params:@{@"tUid": cell.model.userId,@"type": @"2",}
                   pageId:PostSquare_Tag pagePrama:nil];
    
    UIViewController<StrangerViewControllerProtocol> *otherHomePageVC = (UIViewController<StrangerViewControllerProtocol>*)[ZIKRouterToView(StrangerViewControllerProtocol) makeDestination];
    otherHomePageVC.userID = [SoulUserIDUtils getOriginId:[NSString stringWithFormat:@"%@",cell.model.userIdEcpt]];
    otherHomePageVC.homepageFrom = @"广场";
    [self.navigationController pushViewController:otherHomePageVC animated:YES];
}

#pragma mark - SoulTimeLineFollowPromptCellDelegate
- (void)cell:(SoulTimeLineFollowPromptCell *)cell didClickFollowWithSender:(id)sender {
    // 点击一键关注
    [SoulEvent eventClick:@"FollowSquare_OneKeyFollow" params:nil pageId:PostSquare_Follow pagePrama:nil];
    SOSquareTimelineOneKeyFollowLayout *layout = cell.layout;
    if (layout.recommandPosts.count == 0 || ![layout isKindOfClass:SOSquareTimelineOneKeyFollowLayout.class]) {
        return;
    }
    @weakify(self);
    @weakify(layout);
    [[SoulerThumbUpActionHelper shareInstance] followWithPosts:layout.recommandPosts isFollow:YES completeion:^(id data) {
        @strongify(self);
        @strongify(layout);
        layout.isAllFollowed = YES;
        [self.tableView reloadData];
        
    } failure:^(NSInteger code, NSString *message) {
        if (message.length > 0) {
            [MBProgressHUD showMessage:message toView:nil];
        }
    }];
}

#pragma mark - PhotoBrowserViewControllerDelegate
- (UIView*)photoBrowserViewController:(PhotoBrowserViewController*)vc scrollDidEnd:(NSInteger)index {
    NSArray *photos = vc.controller.photos;
    NSArray* cells = [self.tableView visibleCells];
    SOPostImageBrowserModel *photoModel = photos[index];
    if (![photoModel isKindOfClass:SOPostImageBrowserModel.class]) {
        return nil;
    }
    
    for (SOSquareTimelineCell* cell in cells) {
        if (![cell isKindOfClass:SOSquareTimelineCell.class]) continue;
        SOPost* post =  cell.layout.post;
        SOPost *photoPost = photoModel.post;
        SOAttachment *attachment = photoModel.attachment;
        
        if (post.postId &&  [photoPost.postId isEqualToString:post.postId]) {
            if (photoPost.officialTags && photoPost.officialTags.answerTag) {
                return  cell.answerView.answerBGImageView;
            }
            NSInteger picIndex = index;
            for (int i = 0; i< photoPost.attachments.count; i ++){
                SOAttachment *attachIndex = photoPost.attachments[i];
                if (![attachIndex isKindOfClass:SOAttachment.class]) continue;
                if (attachIndex.fileUrl && attachment.fileUrl && [attachment.fileUrl isEqualToString:attachIndex.fileUrl] ){
                    picIndex = i;
                    break;
                }
                if (attachment.localImage && attachIndex.localImage && attachment.localImage == attachIndex.localImage){
                    picIndex = i;
                    break;
                }
            }
            if (!cell.mediaView.hidden && picIndex < cell.mediaView.pics.count ) {
                return cell.mediaView.pics[picIndex];
            }
            if (!cell.voteView.hidden && picIndex < cell.voteView.voteView.imageViewArr.count){
                return cell.voteView.voteView.imageViewArr[picIndex];
            }
        }
    }
    return nil;
}

#pragma mark SOCommentKeyBoardControllerDelegate
- (void)didUpdateAtFromToolbar:(id)toolbar {
    [self popCommentKeyboard:self.keyboardCell replyName:nil];
}

#pragma mark SOSquareOfficialGuideCellDelegate

- (void)officialGuideCell:(UITableViewCell *)cell didClickBannerPhoto:(SOSquareOfficialGuideCellLayout *)layout {
    [self.eventHander didOfficialClickBannerPhoto];
    SOOfficialMessageViewController *officalVC = [SOOfficialMessageViewController new];
    [self.navigationController pushViewController:officalVC animated:YES];
}

- (void)officialGuideCell:(UITableViewCell *)cell didClickBannerContent:(SOSquareOfficialGuideCellLayout *)layout {
    [self.eventHander didOfficialGuideClick];
    SOUrlStrRoute(layout.jumpUrl, nil);
}

#pragma mark - SOPhotoAlbumDelegate
- (void)photoAlbumSelectAssetModel:(SOPhotoAssetModel *)assetModel {
    if (assetModel.asset) {
        SOPhotoAssetModel * albumModel = [self.photoAlbumView getSelectModelWithAsset:assetModel.asset];
        if (!albumModel) {
            albumModel = [[SOPhotoAssetModel alloc] initWihtAsset:assetModel.asset];
        }
        albumModel.entryType = SOPhotoAssetEntryType_Template;
        SoulCameraRemeberModel *tempRemberModel = [SoulCameraRemeberModel new];
        tempRemberModel.rememberTemplateId = self.templateId;
        albumModel.tempRemberModel = tempRemberModel;
        [self.photoAlbumView hidden];
        [self.photoAlbumView photoAlbumEditclickWithAssetModel:albumModel];
    }
}

- (void)photoAlbumDidSeletDoorItemWithDoorType:(SOPhotoAlbumDoorType)doorType {
    if (doorType == SOPhotoAlbumDoorTypeJumpTakePhoto) {
        id<SOCameraViewControllerProtocol> cameraVc = ZIKRouterToViewModule(SOCameraControllerModuleProtocol).defaultRouteConfiguration.makeDestinationWith(SOCameraTypePublish | SOCameraTypeTopShowClose | SOCameraTypeBottomHideAlbum,@"account");
        cameraVc.saveToAlbumIdentify = self.photoAlbumView.albumIdentify;
        cameraVc.templateId = self.templateId;
        UINavigationController *nav = [[UINavigationController alloc]initWithRootViewController:cameraVc];
        nav.modalPresentationStyle = 0;
        SOWeakIfy(self);
        [self presentViewController:nav animated:YES completion:^{
            SOStrongIfy(self);
            [self.photoAlbumView hidden];
        }];
    }
}

#pragma mark - getter
- (SOPhotoAlbumView *)photoAlbumView {
    if (!_photoAlbumView) {
        SOPhotoAlbumBasicConfig *config = [[SOPhotoAlbumBasicConfig alloc] init];
        config.fetchSourceType = SOPhotoFetchSourceTypeOnlyStaticImage;
        config.selectType = SOPhotoAlbumItemSelectWithoutFunction;
        _photoAlbumView = [[SOPhotoAlbumView alloc] initWithFrame:CGRectMake(0, 0, KScreenWidth, kScreenHeight) fetchConfig:config];
        _photoAlbumView.fromSource = SoulEditMediaSrouceTemplate;
        SOPhotoAlbumDoorModel *takePhotoM = [[SOPhotoAlbumDoorModel alloc] initWithDoorType:SOPhotoAlbumDoorTypeJumpTakePhoto];
        _photoAlbumView.doorModels = @[takePhotoM];
        _photoAlbumView.albumDelegate = self;
        _photoAlbumView.postingReleaseModel = nil;
        dispatch_after(0.25, dispatch_get_main_queue(), ^{
            [self.photoAlbumView reloadVisiableItem];
        });
    }
    return _photoAlbumView;
}

- (SOVideoPlayer *)player {
    if (!_player) { // 注意这个播放器的delegate 是cell
        //TTSDK 播放器 开启配置
        BOOL isTTPermissions = [[MMKV defaultMMKV] getBoolForKey:TTLicenseNotificationLicenseResultKey];
        
        SOVideoCorePlayerType playType = SOVideoPlayerType_EffectPlayer;
        if (isTTPermissions) {
            playType = SOVideoPlayerType_TTPlayer;
        } else {
            NSString *className = NSStringFromClass(self.class);
            NSDictionary *tmpParams = @{
                @"className": className,
                @"switchKey": TTLicenseNotificationLicenseResultKey
            };
            [SoulEvent eventPerform:@"UGC_MatchSLPlayer" params:tmpParams pageId:nil pagePrama:nil];
            SLogInfoWithTag(UGCPlayer, @"广场列表，命中了自研播放器（非期望), 当前类: %@, 开关Key:%@", className, TTLicenseNotificationLicenseResultKey);
        }
        
        _player = [[SOVideoPlayer alloc] initWithType:playType];
        _player.enableSR = NO;//feed流显示尺寸过小，关闭超分，节省性能消耗
        _player.mute = YES;
        _player.repeat = YES;
        _player.enableHDR = YES;
        _player.sceneType = @"feedFlowVideo";
        /// 在广场图片尺寸优化实验中修改了图片和视频的展示尺寸，需要将视频设置为 比例不变。铺满容器显示
        /// 但是对于自研播放器的videoGravity ，设置之后，会对videoGravity 进行转换， 只有other 才会对应 AspectFill
        /// 所以在实验内，如果是自研播放器，将videoGravity 设置为一个不在枚举中的类型
        if (playType == SOVideoPlayerType_EffectPlayer) {
            _player.videoGravity = NSIntegerMax;
        }
    }
    return _player;
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (NSDictionary *)pageParams{
    if (_pageParams) return _pageParams;
    return  @{@"pageId":self.dt_pageId,@"type":self.dt_pageType};
}

- (SOPostDeleteManage *)postDeleteManage {
    if (!_postDeleteManage) {
        _postDeleteManage = [SOPostDeleteManage new];
    }
    return _postDeleteManage;
}
- (SORecomondSquareRefreshManage *)refreshManage{
    if ([self.tableViewModel isKindOfClass:SquareMainRecommandViewModel.class]){
        SORecomondSquareRefreshManage *refreshManage = ((SquareMainRecommandViewModel *)self.tableViewModel).refreshManage;
        return refreshManage;
    }
    return nil;
}

#pragma mark - SOSquareWithHomePageInterfaceProtocol
- (void)enterHomePageWithPost:(SOPost *)post {
    //大于5次 未来七天内不曝光
    if (self.isFailStrategyCondition) {
        return;
    }
    BOOL isRelation =  [[ABTestMultiStrategy multiStrategyForSquare_recSquareRelatePost].value isEqualToString:@"a"];
    if (!isRelation) {return;}
    if (!post) return;
    //处理多次网络请求叠加情况 先赋值一个空数组占位
    if (post.relationPosts) return;
    
    if (post.isRelation) return;
    if (self.routerType != SubSquareRouterTypeMainRecommand && self.routerType != SubSquareRouterTypeMainLocation) {
        return;
    }
    
    SOPostsTableViewModel *tableViewModel = nil;
    if (self.routerType == SubSquareRouterTypeMainRecommand) {
        tableViewModel = (SquareMainRecommandViewModel *)self.tableViewModel;
    } else if (self.routerType == SubSquareRouterTypeMainLocation) {
        tableViewModel = (SORecommandSubViewModel *)self.tableViewModel;
    }
  
    NSIndexPath *indexPath = nil;
    for (SOSquareTimelineCell *cell  in self.tableView.visibleCells) {
        if (![cell isKindOfClass:SOSquareTimelineCell.class]) continue;
        SOPost *currentPost = cell.layout.post;
        if ([currentPost.postId isEqual:post.postId]) {
            indexPath = [self.tableView indexPathForCell:cell];
            //如果异常同步更新下
            if (![indexPath isEqual:cell.layout.indexPath]) {
                cell.layout.indexPath = indexPath;
            }
            break;
        }
    }
    
    @weakify(self);
    NSDictionary *data = @{
        @"userIdEcpt" : SOString(post.authorIdEcpt),
        @"postIdEcpt" : SOString(post.postIdEcpt)
    };
    post.relationPosts = [NSMutableArray new];
    [[[tableViewModel requestRelateFromHomePageWithParams:data] retry:1] subscribeNext:^(NSDictionary *  _Nullable data) {
        @strongify(self);
        NSArray *postList = [NSArray yy_modelArrayWithClass:SOPost.class json:data[@"postList"]];
        SORecUserContainerModel *userCardModel = [SORecUserContainerModel yy_modelWithJSON:data[@"recUserContainer"]];
        
#if DEBUG
        NSDictionary *recUserData = [[MMKV defaultMMKV] getObjectOfClass:NSDictionary.class forKey:@"temp_user_card_cache"];
        userCardModel = [SORecUserContainerModel yy_modelWithJSON:recUserData];
#endif
        if (!postList.count && !userCardModel) {
            post.relationPosts = nil;
            return;
        }
        [self recordRelateRequestTime];
        [post.relationPosts addObjectsFromArray:postList];
        post.relationTitle = SOString(data[@"title"]);
        [self updateRelatePostInfo:post byRelatePosts:post.relationPosts withCardModel:userCardModel];
        //插入标题
        SORelationPostEnterCellLayout *titleLayout = (SORelationPostEnterCellLayout *) [tableViewModel insertRelationFromHomePageEnterSectionWithPosts:post.relationPosts withIndexPath:indexPath byRelatePost:post];
        if (titleLayout.unfold) {
            //直接插入内容
            [self insertImmediateRelatePosts:post.relationPosts byRelatePost:post withPosition:indexPath.section + 2];
        }
        [self.tableView reloadData];
    }];
}

- (BOOL)isFailStrategyCondition {
    NSArray *requestArr = [[MMKV defaultMMKV] getObjectOfClass:NSArray.class forKey:@"relate_post_request_time"];
    
    NSInteger postExposeCount = [[SoulConfigManager.sharedInstance.globalConfig.globalConfigData objectForKey:@"recommend_related_post_expose_count_max"] integerValue] ?: 5;
    
    NSInteger requestDayCount = [[SoulConfigManager.sharedInstance.globalConfig.globalConfigData objectForKey:@"recommend_related_post_request_interval"] integerValue] ?: 7;
    
    long long lastTimeStamps = [requestArr.lastObject integerValue];
    NSTimeInterval current = [[NSDate date] timeIntervalSince1970] * 1000;
    long long currentTimeStamps = [[NSString stringWithFormat:@"%.0f",current] integerValue];
    BOOL conditon = NO;
    if (currentTimeStamps == 0 || lastTimeStamps == 0) {
        conditon = NO;
    }
    if (requestArr.count >= postExposeCount) {
        conditon = (currentTimeStamps - lastTimeStamps) < 1000 * 60 * 60 * 24 * requestDayCount;
        if (!conditon) {
            [[MMKV defaultMMKV] setObject:@[] forKey:@"relate_post_request_time"];
        }
    } else {
        conditon = NO;
    }
    return conditon;
}

- (void)recordRelateRequestTime {
    NSArray *list = [[MMKV defaultMMKV] getObjectOfClass:NSArray.class forKey:@"relate_post_request_time"];
    NSMutableArray *newList = [[NSMutableArray alloc] initWithArray:list];
    NSTimeInterval timeStamps = [[NSDate date] timeIntervalSince1970] * 1000;
    NSString *timeStr = [NSString stringWithFormat:@"%.0f",timeStamps];
    [newList addObject:SOString(timeStr)];
    [[MMKV defaultMMKV] setObject:newList forKey:@"relate_post_request_time"];
}

- (void)updateRelatePostInfo:(SOPost *)post byRelatePosts:(NSMutableArray <SOPost *> *)posts withCardModel:(SORecUserContainerModel * _Nullable)userCardModel {
    if (!posts.count && !userCardModel) return;
    
    //已经有关联数据
    post.isRelation = YES;
    
    //userCard 用 post承载
    if (userCardModel.attachments.count) {
        SOPost *relateCardPost = [SOPost new];
        relateCardPost.postId = SOString(userCardModel.attachments.firstObject.ownerId);
        relateCardPost.relateBizModel = userCardModel;
        NSInteger postion = userCardModel.position;
        if (postion >= posts.count || postion < 0) {
            postion = posts.count;
            [posts addObject:relateCardPost];
        } else {
            [posts insertObject:relateCardPost atIndex:postion];
        }
    }
    //关键主帖子用户删除用
    for (SOPost *subPost in posts) {
        subPost.relatePostId = post.postId;
        subPost.isRelation = YES;
    }
}

- (void)leaveHomePageWithPost:(SOPost *)post {
    if (!post.relationTitle.length) {
        return;
    }
    NSMutableDictionary *data = [NSMutableDictionary new];
    data[@"relateTitle"] = post.relationTitle;
    data[@"rootPostId"] = post.postId;
    [SoulEvent eventExpose:@"PostSquare_SeeMoreUserExpo" params:data pageId:nil pagePrama:nil];
}

- (void)insertRelatePosts:(NSMutableArray *)posts byRelatePost:(SOPost *)post {
    NSIndexPath *indexPath = nil;
    
    for (SOSquareTimelineCell *cell  in self.tableView.visibleCells) {
        if (![cell isMemberOfClass:SOSquareTimelineCell.class]) continue;
        SOPost *currentPost = cell.layout.post;
        if ([currentPost.postId isEqual:post.postId]) {
            indexPath = [self.tableView indexPathForCell:cell];
            //如果异常同步更新下
            if (![indexPath isEqual:cell.layout.indexPath]) {
                cell.layout.indexPath = indexPath;
            }
            break;
        }
    }
    
    if (!indexPath) {
        NSAssert(indexPath != nil, @"indexPath 一定不为nil");
        return;
    }
    
    NSIndexSet *indexSet = [NSIndexSet indexSetWithIndexesInRange:NSMakeRange(indexPath.section + 2, posts.count)];
    NSMutableArray <SOTableViewSectionModel *> *setions = [NSMutableArray new];
    
    SOTimelineCellType type = SOTimelineCellTypeMainRecommend;
    if (self.routerType == SubSquareRouterTypeMainRecommand) {
        type = SOTimelineCellTypeMainRecommend;
    } else {
        type = SOTimelineCellTypeMainLocation;
    }
    
    for (SOPost *subPost in posts) {
        SOTableViewSectionModel *postSection = [[SOTableViewSectionModel alloc] init];
        postSection.dataModel = subPost;
        NSMutableArray *items = [NSMutableArray new];
        postSection.sectionItems = items;
        //是否是最后一个
        if (subPost == posts.lastObject) {
            subPost.configRelationEndColor = YES;
        }
        if (subPost.relateBizModel) {
            postSection = [self generatePushUserCardSectionWithPost:subPost];
        } else {
            SOSquareTimelineLayout *layout = [[SOSquareTimelineLayout alloc] initWithPost:subPost type:type autoLayout:false];
            layout.routerType = self.routerType;
            subPost.displayStartTime = [[NSDate date] timeIntervalSince1970] * 1000;
            layout.cellType = SquareCellTypeNormal;
            layout.cellHeight = @(layout.height);
            layout.cellClass = [SOSquareTimelineCell class];
            layout.cellIdentifer = @"SOSquareTimelineCell";
            [layout layout];
            [items addObject:layout];
        }
        [setions addObject:postSection];
    }
    
    //整体插入进去
    @try {
        [self.tableViewModel.sectionModelArray insertObjects:setions atIndexes:indexSet];
        [self.tableView performBatchUpdates:^{
            [self.tableView insertSections:indexSet withRowAnimation:UITableViewRowAnimationNone];
        } completion:^(BOOL finished) {
            
        }];
    } @catch (NSException *exception) {
        [self.tableView reloadData];
    }
}

//生成关联对象的的 推人卡片section
- (SOTableViewSectionModel * _Nullable)generatePushUserCardSectionWithPost:(SOPost *)subPost {
    if (![subPost isKindOfClass:SOPost.class]) {
        return nil;
    }
    SORecUserContainerModel *model = (SORecUserContainerModel *)subPost.relateBizModel;
    if (!model.attachments.count) {
        return nil;
    }
    SOTableViewSectionModel * pushUserCardSection = [[SOTableViewSectionModel alloc] init];
    NSMutableArray *cellItems = [NSMutableArray new];
    pushUserCardSection.sectionItems = cellItems;

    //插入用户卡片业务
    SOSquarePushUserCardCellLayout *pushUserCardLayout = [[SOSquarePushUserCardCellLayout alloc] init];
    pushUserCardLayout.dataModel = model;
    pushUserCardLayout.post = subPost;
    pushUserCardLayout.routerType = self.routerType;
    pushUserCardLayout.cellStyleType = SquareCellTyReNewUserCardType;

    __weak typeof(self) weakSelf = self;
    __weak typeof(pushUserCardSection) weakPushUserCardSection = pushUserCardSection;
    pushUserCardLayout.deleteCellBlock = ^(SOTableViewCellItem * _Nonnull cellItem) {
        [weakPushUserCardSection.sectionItems removeAllObjects];
        @try {
            [weakSelf.tableView reloadSection:cellItem.indexPath.section withRowAnimation:UITableViewRowAnimationRight];
        } @catch (NSException *exception) {
            [weakSelf.tableView reloadData];
        }
        //延迟删除关联数据
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [weakSelf dealMergeFeedsDeleteByRelatePosts:subPost];
        });
    };
    [cellItems addObject:pushUserCardLayout.layout];
    return pushUserCardSection;
}


//直接插入时候的链路
- (void)insertImmediateRelatePosts:(NSMutableArray *)posts byRelatePost:(SOPost *)post withPosition:(NSInteger)position {
    SOTimelineCellType type = SOTimelineCellTypeMainRecommend;
    if (self.routerType == SubSquareRouterTypeMainRecommand) {
        type = SOTimelineCellTypeMainRecommend;
    } else {
        type = SOTimelineCellTypeMainLocation;
    }
    NSMutableArray *setions = [NSMutableArray new];
    for (SOPost *subPost in posts) {
        SOTableViewSectionModel *postSection = [[SOTableViewSectionModel alloc] init];
        postSection.dataModel = subPost;
        NSMutableArray *items = [NSMutableArray new];
        postSection.sectionItems = items;
        if (subPost == posts.lastObject) {
            subPost.configRelationEndColor = YES;
        }
        
        if (subPost.relateBizModel) {
            postSection = [self generatePushUserCardSectionWithPost:subPost];
        } else {
            SOSquareTimelineLayout *layout = [[SOSquareTimelineLayout alloc] initWithPost:subPost type:type autoLayout:false];
            layout.routerType = self.routerType;
            subPost.displayStartTime = [[NSDate date] timeIntervalSince1970] * 1000;
            layout.cellType = SquareCellTypeNormal;
            layout.cellHeight = @(layout.height);
            layout.cellClass = [SOSquareTimelineCell class];
            layout.cellIdentifer = @"SOSquareTimelineCell";
            [layout layout];
            [items addObject:layout];
        }
        [setions addObject:postSection];
    }
    [self.tableViewModel.sectionModelArray insertObjects:setions atIndex:position];
}

//折叠效果动画处理.
- (void)deleteRelatePosts:(NSArray *)posts byRelatePost:(SOPost *)post {
    NSIndexPath *indexPath = nil;
    for (SOSquareTimelineCell *cell  in self.tableView.visibleCells) {
        if (![cell isMemberOfClass:SOSquareTimelineCell.class]) continue;
        SOPost *currentPost = cell.layout.post;
        if ([currentPost.postId isEqual:post.postId]) {
            indexPath = [self.tableView indexPathForCell:cell];
            //如果异常同步更新下
            if (![indexPath isEqual:cell.layout.indexPath]) {
                cell.layout.indexPath = indexPath;
            }
            break;
        }
    }
    
    if (!indexPath) {
        NSAssert(indexPath != nil, @"indexPath delete 一定不为nil");
        return;
    }
    
    @try {
        //手动曝光 [不走协议API]
        [self trackRelationPostsPostWatchByRootPost:post];
        
        NSIndexSet *indexSet = [NSIndexSet indexSetWithIndexesInRange:NSMakeRange(indexPath.section + 2, posts.count)];
        //整体删除
        [self.tableViewModel.sectionModelArray removeObjectsAtIndexes:indexSet];
        [self.tableView performBatchUpdates:^{
            [self.tableView deleteSections:indexSet withRowAnimation:UITableViewRowAnimationNone];
        } completion:^(BOOL finished) {
    
        }];
    } @catch (NSException *exception) {
        [self.tableView reloadData];
    }
}

- (void)trackRelationPostsPostWatchByRootPost:(SOPost *)post {
    for (UITableViewCell *cell in self.tableView.visibleCells) {
        if (post.relateBizModel) {
            SOSquarePushUserCardCell *otherBizCell = (SOSquarePushUserCardCell *)cell;
            SOPost *currentPost = otherBizCell.layout.post;
            for (SOPost *childPost in post.relationPosts) {
                if (currentPost.postId.integerValue == childPost.postId.integerValue) {
                    [otherBizCell cellEndDisplayed];
                }
            }
        } else {
            if (![cell isKindOfClass:SOSquareTimelineCell.class]) continue;
            SOSquareTimelineCell *lineCell = (SOSquareTimelineCell *)cell;
            SOPost *currentPost = lineCell.layout.post;
            NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
            for (SOPost *childPost in post.relationPosts) {
                if (currentPost.postId.integerValue == childPost.postId.integerValue) {
                    [self.presenter eventEndCellDisplayWithIndexPath:indexPath cell:lineCell];
                }
            }
        }
    }
}

- (void)dealMergeFeedsDeleteByRelatePosts:(SOPost *)post {
    BOOL relationExp =  [[ABTestMultiStrategy multiStrategyForSquare_recSquareRelatePost].value isEqualToString:@"a"];
    if (!relationExp || !post.isRelation) {return;}
    if (self.routerType != SubSquareRouterTypeMainRecommand && self.routerType != SubSquareRouterTypeMainLocation) {
        return;
    }
    NSString *currentPostId = SOString(post.postId);
    BOOL rootPost = post.relationTitle.length && post.isRelation;
    BOOL isRelationPost = post.isRelation && post.relatePostId.length;
#if DEBUG
    NSString *msg1 = rootPost ? @"主贴" : (isRelationPost ? @"关联帖子" : @"普通贴");
    NSString *msg = [NSString stringWithFormat:@"捕获到删除关联贴: %@",post.postId];
    NSLog(@"msg======%@++%@",msg,msg1);
#endif
    
    //确认删除的是rootPost还是 relatePost
    NSMutableArray <SOTableViewSectionModel *> *tmpSections = [NSMutableArray new];
    //如果删除的是rootPost 那关联的relatePosts 三个也要同步删除 包含 标题section
    if (rootPost) {
        for (SOTableViewSectionModel * section in self.tableViewModel.sectionModelArray) {
            id layout = section.sectionItems.firstObject;
            if ([layout isKindOfClass:SORelationPostEnterCellLayout.class]) {
                SORelationPostEnterCellLayout *enterPostLayout = (SORelationPostEnterCellLayout *)layout;
                if (enterPostLayout.relatePost.postId.integerValue == currentPostId.integerValue) {
                    [tmpSections addObject:section];
                    continue;
                }
            }
            //关联的帖子
            if ([layout isKindOfClass:SOSquareTimelineLayout.class]) {
                SOSquareTimelineLayout *cellLayout = (SOSquareTimelineLayout *)layout;
                SOPost *aPost = cellLayout.post;
                if (aPost.isRelation && aPost.relatePostId.integerValue == currentPostId.integerValue) {
                    [tmpSections addObject:section];
                    continue;
                }
            }
            
            //关联的卡片
            if ([layout isKindOfClass:SOSquarePushUserCardCellLayout.class]) {
                SOSquarePushUserCardCellLayout *cellLayout = (SOSquarePushUserCardCellLayout *)layout;
                SOPost *aPost = cellLayout.post;
                if (aPost.isRelation && aPost.relatePostId.integerValue == currentPostId.integerValue) {
                    [tmpSections addObject:section];
                    continue;
                }
            }
        }
        
        for (SOTableViewSectionModel * section in tmpSections) {
            if ([self.tableViewModel.sectionModelArray containsObject:section]) {
                [self.tableViewModel.sectionModelArray removeObject:section];
            }
        }
        [tmpSections removeAllObjects];
        [self.tableView reloadData];
    } else if (isRelationPost) {
        //删除的是关联的帖子
        SOTableViewSectionModel *enterTitleSection = nil;
        SOSquareTimelineLayout *rootLayout  = nil;
        for (SOTableViewSectionModel * section in self.tableViewModel.sectionModelArray) {
            id layout = section.sectionItems.firstObject;
            if ([layout isKindOfClass:SORelationPostEnterCellLayout.class]) {
                SORelationPostEnterCellLayout *enterPostLayout = (SORelationPostEnterCellLayout *)layout;
                if (enterPostLayout.relatePost.postId.integerValue == post.relatePostId.integerValue) {
                    enterTitleSection = section;
                    continue;
                }
            }
            if ([layout isKindOfClass:SOSquareTimelineLayout.class] || [layout isKindOfClass:SOSquarePushUserCardCellLayout.class]) {
                SOPost *aPost = nil;
                if ([layout isKindOfClass:SOSquarePushUserCardCellLayout.class]) {
                    aPost = ((SOSquarePushUserCardCellLayout *)layout).post;
                } else {
                    aPost = ((SOSquareTimelineLayout *)layout).post;
                }
    
                //其他关联贴
                if (aPost.isRelation && aPost.relatePostId.integerValue == post.relatePostId.integerValue) {
                    [tmpSections addObject:section];
                    continue;
                }
                
                if (aPost.isRelation && aPost.postId.integerValue == post.relatePostId.integerValue) {
                    //主贴post 也找到
                    [self deleteRelationPostWithRootPost:aPost byCurrent:post];
                    rootLayout = layout;
                    continue;
                }
            }
        }
        if (tmpSections.count == 0 && enterTitleSection) {
           //说明关联贴删除完了 主贴的样式 以及 标题也要删除 否则可以忽略
            [self.tableViewModel.sectionModelArray removeObject:enterTitleSection];
            rootLayout.post.relationTitle = nil;
            rootLayout.post.isRelation = NO;
            rootLayout.post.configRelationEndColor = NO;
        }
        [self.tableView reloadData];
    }
}

- (void)deleteRelationPostWithRootPost:(SOPost *)rootPost byCurrent:(SOPost *)post {
    //删除关联数据源
    SOPost *relatePost = nil;
    for (SOPost *model in rootPost.relationPosts) {
        if (model.postId.integerValue == post.postId.integerValue) {
            relatePost = model;
            break;
        }
    }

    if (relatePost) {
        BOOL last = rootPost.relationPosts.lastObject == relatePost;
        //可能删除的不是最后一个
        [rootPost.relationPosts removeObject:relatePost];
        if (last) {
            //重新给倒数第一个post 加颜色
            rootPost.relationPosts.lastObject.configRelationEndColor = YES;
        }
    }
}

@end
