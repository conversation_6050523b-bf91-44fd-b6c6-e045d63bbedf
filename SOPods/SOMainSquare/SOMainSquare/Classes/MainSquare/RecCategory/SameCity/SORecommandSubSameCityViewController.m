//
//  SORecommandSubSameCityViewController.m
//  SOMainSquare
//
//  Created by xupeng on 2023/2/10.
//

#import "SORecommandSubSameCityViewController.h"
#import <SOLocation/SOLocationManager.h>
#import <SOLocation/SOLocation-Swift.h>
#import <SOMainSquare/SOMainSquare-Swift.h>
#import <SoulBussinessKit/SoulBussinessKit-Swift.h>
#import <SoulCore/SoulCore-Swift.h>
#import <MMKV/MMKV.h>

static NSString * const kSameCityLocationCloseTimeKey = @"SORecommandSubSameCity_LocationCloseTime";

@interface SORecommandSubSameCityViewController ()

@property (nonatomic, assign) BOOL lastLocationEnabled;
/// 悬浮地理位置权限弹窗
@property (nonatomic, strong) UIView *headerView;

@property (nonatomic, strong) SOSquareCityFilterBanner *filterBanner;

@end

@implementation SORecommandSubSameCityViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.lastLocationEnabled = SoulLocation.shared.isLocationEnable;
    
    [self setupTableHeaderView];
    SOWeakIfy(self);
    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationDidBecomeActiveNotification object:nil] subscribeNext:^(id x) {
        SOStrongIfy(self);
        
        BOOL currentLocationEnabled = SoulLocation.shared.isLocationEnable;
        if (self.lastLocationEnabled == currentLocationEnabled) {
            return;
        }
        self.lastLocationEnabled = currentLocationEnabled;
        [[NSNotificationCenter defaultCenter] postNotificationName:@"KRecCategoryNeedRequestNotification" object:nil];
        
        [self setupTableHeaderView];
    }];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self startBatteryMonitor];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    [self stopBatteryMonitor];
}


- (void)startBatteryMonitor {
    [SOAppBatteryMonitor startSquareEvt:self.acName extMap:@{@"lastLocationEnabled": @(self.lastLocationEnabled)}];
}

- (void)stopBatteryMonitor {
    [SOAppBatteryMonitor stopSquareEvt:self.acName extMap:@{@"lastLocationEnabled": @(self.lastLocationEnabled)}];
}

- (NSString *)acName {
    return @"ac_SORecommandSubSameCityViewController";
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    [super scrollViewDidScroll:scrollView];
    if (scrollView == self.tableView) {
        CGFloat offsetY = scrollView.contentOffset.y;
        if (offsetY > 0) {
            self.headerView.top = 0;
        } else {
            self.headerView.top = -offsetY;
        }
    }
}

- (void)scrollViewDidEndScroll {
    [super scrollViewDidEndScroll];
    [self.tableViewModel predictByAILiteSession];
}

- (void)updateTableHeaderViewNeedPaddingTop:(BOOL)needShowTopPadding {
    SORecommandSubViewModel *vm = (SORecommandSubViewModel *)self.tableViewModel;
    if (![vm isKindOfClass:SORecommandSubViewModel.class]) {
        return;
    }
    if (needShowTopPadding) {
        vm.tableHeaderPaddingTop = 68;
    } else {
        vm.tableHeaderPaddingTop = 0;
    }
    [vm updateTableHeaderView];
}

- (void)setupTableHeaderView {
    SORecommandSubViewModel *vm = (SORecommandSubViewModel *)self.tableViewModel;
    if (![vm isKindOfClass:SORecommandSubViewModel.class]) {
        return;
    }
    vm.tableHeaderPaddingTop = 0;
    [self.headerView removeFromSuperview];
    [self.filterBanner removeFromSuperview];
    if (SoulLocation.shared.isLocationEnable) {
        if ([SOABValue(@"216999") isEqualToString:@"a"]) {
            [self.view addSubview:self.filterBanner];
            [self.view bringSubviewToFront:self.filterBanner];
            [self.filterBanner mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.top.trailing.equalTo(@0);
            }];
            vm.tableHeaderPaddingTop = 68;
        }
    } else {
        if ([self shouldShowHeaderView]) {
            [self.view addSubview:self.headerView];
            [self.view bringSubviewToFront:self.headerView];
            vm.tableHeaderPaddingTop = 68;
        }
    }
    [vm updateTableHeaderView];
}

- (void)filterAction {
    
}

// 是否需要展示定位权限弹窗（未开启定位时才调用）
- (BOOL)shouldShowHeaderView {
    int64_t lastCloseTime = [[MMKV defaultMMKV] getInt64ForKey:kSameCityLocationCloseTimeKey defaultValue:0];
    // 从未关闭过，展示
    if (lastCloseTime <= 0) {
        return YES;
    }
    int64_t now = (int64_t)([[NSDate date] timeIntervalSince1970]);
    return (now - lastCloseTime) >= 604800; //  7 * 24 * 60 * 60 = 7d
}

/// 悬浮地理位置权限弹窗
- (UIView *)headerView {
    if (!_headerView) {
        UIView *headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, KScreenWidth, 68)];
        headerView.backgroundColor = SOColor(0);

        UIView *bgView = [[UIView alloc] initWithFrame:CGRectMake(12, 8, KScreenWidth - 24, 52)];
        bgView.backgroundColor = [SOColorDefine autoColorWithLight:@"#EBFBFB" dark:@"#28283A"];
        bgView.radius = 26;
        [headerView addSubview:bgView];

        UIImageView *iconImageView = [[UIImageView alloc] initWithFrame:CGRectMake(10, 0, 46, 46)];
        iconImageView.image = [UIImage square_imageNamed:@"small_city_location_icon"];
        [bgView addSubview:iconImageView];
        
        // 关闭按钮
        UIButton *closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [closeButton setImage:[UIImage square_imageNamed:@"small_city_location_close"] forState:UIControlStateNormal];
        SOWeakIfy(self);
        [[closeButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            SOStrongIfy(self);
            [self.headerView removeFromSuperview];
            SORecommandSubViewModel *vm = (SORecommandSubViewModel *)self.tableViewModel;
            if ([vm isKindOfClass:SORecommandSubViewModel.class]) {
                vm.tableHeaderPaddingTop = 0;
                [vm updateTableHeaderView];
            }
            // 记录关闭时间
            int64_t closeTime = (int64_t)([[NSDate date] timeIntervalSince1970]);
            [[MMKV defaultMMKV] setInt64:closeTime forKey:kSameCityLocationCloseTimeKey];
        }];
        [bgView addSubview:closeButton];
        [closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.equalTo(@30);
            make.right.equalTo(@-8);
            make.centerY.equalTo(bgView);
        }];
        
        UIButton *confirmButton = [UIButton buttonWithType:UIButtonTypeCustom];
        confirmButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [confirmButton setTitle:@"开启" forState:UIControlStateNormal];
        confirmButton.titleLabel.font = FONT(TextFontName, 14);
        [confirmButton setTitleColor:GET_COLOR(0) forState:UIControlStateNormal];
        confirmButton.backgroundColor = GET_COLOR(1);
        confirmButton.radius = 14;
        [[confirmButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            if (SoulLocation.shared.enable) {
                if (SoulLocation.shared.authorizationStatus == kCLAuthorizationStatusNotDetermined) {
                    [SoulLocation.shared requestAuthorization:^(BOOL isEnabled) {
                        SOStrongIfy(self);
                        self.lastLocationEnabled = isEnabled;
                        if (isEnabled) {
                            [self setupTableHeaderView];
                            
                            SoulLocationObserver* observer = [[SoulLocationObserver alloc] initWithType:SoulLocationObserverTypeOnlyOnce changed:^(BMKLocation * _Nonnull location, BMKReverseGeoCodeSearchResult *result) {
                                [[NSNotificationCenter defaultCenter] postNotificationName:@"KRecCategoryNeedRequestNotification" object:nil];
                            } failed:nil];
                    
                            [SoulLocation.shared requestLocationWithObserver:observer];
                        }
                    }];
                } else {
                    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString] options:@{} completionHandler:nil];
                }
            }else {
                if ([SOLocationManager authorizationStatus] == kCLAuthorizationStatusNotDetermined) {
                    [[SOLocationManager sharedInstance] requestLocationAuthorization:^(BOOL isEnabled) {
                        SOStrongIfy(self);
                        self.lastLocationEnabled = isEnabled;
                        if (isEnabled) {
                            [self setupTableHeaderView];

                            [SOLocationManager sharedInstance].locationModelBlock = ^(SOLocationModel * _Nullable locationModel) {
                                [[NSNotificationCenter defaultCenter] postNotificationName:@"KRecCategoryNeedRequestNotification" object:nil];
                                [[SOLocationManager sharedInstance] stopLocation];
                            };
                            [[SOLocationManager sharedInstance] startLocation];
                        }
                    }];
                } else {
                    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString] options:@{} completionHandler:nil];
                }
            }
        }];
        [bgView addSubview:confirmButton];
        [confirmButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(closeButton.mas_left).offset(-8);
            make.centerY.equalTo(bgView);
            make.width.equalTo(@60);
            make.height.equalTo(@28);
        }];


        UILabel *titleLabel = [[UILabel alloc] init];
        titleLabel.text = @"开启定位，解锁同城瞬间";
        titleLabel.textColor = SOColor(2);
        titleLabel.font = FONT(@"PingFangSC-Regular", 14);
        [bgView addSubview:titleLabel];
        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(iconImageView.mas_right).offset(6);
            make.top.equalTo(@8);
            make.height.equalTo(@20);
            make.right.equalTo(confirmButton.mas_left).offset(-16);
        }];

        UILabel *subTitleLabel = [[UILabel alloc] init];
        subTitleLabel.text = @"发现同城的小美好";
        subTitleLabel.textColor = SOColor(6);
        subTitleLabel.font = FONT(@"PingFangSC-Regular", 10);
        [bgView addSubview:subTitleLabel];
        [subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(titleLabel);
            make.top.equalTo(titleLabel.mas_bottom).offset(2);
            make.height.equalTo(@14);
        }];

        _headerView = headerView;

    }
    return _headerView;

}

- (SOSquareCityFilterBanner *)filterBanner {
    if (!_filterBanner) {
        _filterBanner = [SOSquareCityFilterBanner new];
    }
    return _filterBanner;
}

#pragma mark -- SOCircumRecommendCellDelegate

- (void)didClickRefreshWithLayout:(SOCircumRecommendCellLayout *)layout {
    [self.tableView.mj_header beginRefreshing];
    
    /* 同城tab刷空提示刷新点击点击埋点 */
   [SoulEvent eventClick:@"CitySquare_RecommendEndRefreshClick"
                  params:@{@"city_name" : SOString(layout.cityName)}
                  pageId: nil pagePrama:nil];
 
}

@end
