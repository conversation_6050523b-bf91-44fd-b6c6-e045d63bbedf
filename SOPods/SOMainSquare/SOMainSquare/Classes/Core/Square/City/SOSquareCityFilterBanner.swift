//
//  SOSquareCityFilterBanner.swift
//  SOMainSquare
//
//  Created by al<PERSON> on 2025/9/29.
//

import UIKit
import SnapKit
import SoulUI<PERSON>itExtended

public class SOSquareCityFilterBanner: UIView {
    
    enum ViewState {
        case normal
        case select(String)
    }
    
    var viewState: ViewState = .normal {
        didSet {
            switch viewState {
            case .normal:
                titleLabel.text = "筛选类别更精准推荐"
                titleLabel.textColor = .soul.color(3)
                limitView.isHidden = false
            case .select:
                titleLabel.text = "筛选: 男性  3km  美食"
                titleLabel.textColor = .soul.color(1)
                limitView.isHidden = true
            }
        }
    }
    
    // MARK: - Life Circle
    
    @objc
    public init() {
        super.init(frame: .zero)
        
        setupUI()
        setupUIConstraints()
        setupData()
    }
    
    public required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    // MARK: - Data
    
    func setupData() {
        viewState = .normal
    }
    
    public func update(items: [String]) {
        updateUI(items: items)
        updateUIConstraints()
    }
    
    public func reset() {
        viewState = .normal
    }
    
    // MARK: - UI
    
    private func setupUI() {
        backgroundColor = .soul.color(0)
        addSubview(container)
        container.addSubview(iconView)
        container.addSubview(titleLabel)
        container.addSubview(limitView)
        container.addSubview(button)
    }
    
    private func setupUIConstraints() {
        container.snp.makeConstraints { make in
            make.top.equalTo(8)
            make.bottom.equalTo(-8)
            make.leading.equalTo(12)
            make.trailing.equalTo(-12)
            make.height.equalTo(40)
        }
        iconView.snp.makeConstraints { make in
            make.leading.equalTo(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconView.snp.trailing).offset(2)
            make.centerY.equalToSuperview()
        }
        limitView.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel.snp.trailing).offset(4)
            make.centerY.equalToSuperview()
            make.width.equalTo(28)
            make.height.equalTo(16)
            make.trailing.lessThanOrEqualTo(button.snp.leading).offset(-4)
        }
        button.snp.makeConstraints { make in
            make.trailing.equalTo(-10)
            make.centerY.equalToSuperview()
            make.width.equalTo(56)
            make.height.equalTo(24)
        }
    }
    
    private func updateUI(items: [String]) {
        
    }
    
    private func updateUIConstraints() {
        
    }
    
    private let container = UIView().then {
        $0.backgroundColor = .soul.color(7)
        $0.layer.cornerRadius = 6
        $0.layer.masksToBounds = true
    }
    
    private let iconView = UIImageView()
    private let titleLabel = UILabel().then {
        $0.font = .soul.PingFangSC(size: 13)
        $0.textAlignment = .left
        $0.numberOfLines = 1
        $0.lineBreakMode = .byTruncatingTail
    }
    private let limitView = UIImageView()
    private let button = UIButton().then {
        $0.backgroundColor = .soul.color(1)
        $0.setTitle("筛选", for: .normal)
        $0.setTitleColor(.soul.color(0), for: .normal)
        $0.titleLabel?.font = .soul.PingFangSC(size: 12, type: .medium)
        $0.layer.cornerRadius = 12
        $0.layer.masksToBounds = true
    }
}
