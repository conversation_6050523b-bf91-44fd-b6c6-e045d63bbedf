# 同城筛选悬浮控件与筛选面板设计方案

## 背景与目标
同城广场目前仅在定位未开启时展示权限引导悬浮控件，缺乏精细化筛选能力。本方案拟在同城推荐列表中加入筛选悬浮入口与筛选面板，以满足以下目标：
- 在定位栏位下方增加“筛选”悬浮控件，并与定位权限悬浮控件互斥展示。
- 提供多类别、多选项的筛选面板，最多四列一行，支持单个条件选中/取消、重置与确认。
- 根据筛选结果实时反馈到悬浮控件文案状态并驱动列表刷新。
- 在无后端真实数据时通过 mock 数据驱动 UI，后续仅需替换数据源。

## 角色与术语
- **定位悬浮控件**：现有的定位权限提醒浮层。
- **筛选悬浮控件**：新增的紧贴顶部的筛选入口标签。
- **筛选面板**：点击筛选悬浮控件展开的全屏半透明蒙层或浮层列表，用于条件选择。
- **筛选条件**：按照分类展示的按钮集合。

## 用户体验流程
1. 进入同城页：
   - 若定位权限未开启且满足展示周期，显示定位悬浮控件；否则显示筛选悬浮控件。
2. 筛选悬浮控件默认文案：“筛选分类更精准推荐”；当存在选中条件时文案变成“筛选：男生·3km·美食…”，最多展示若干项并以省略号结尾。
3. 点击筛选悬浮控件：
   - 展开底部浮层面板（覆盖顶部导航以下区域），背景透明遮罩。
   - 面板顶部展示所有分类，分类名称左对齐，选项按最多四个一行自适应换行。
   - 每个选项是圆角 Button，默认浅灰描边，选中高亮背景。
   - 面板底部固定操作栏：左侧“重置”，右侧主按钮“确认”。
4. 选择逻辑：
   - 同一分类内可多选或单选（以需求图显示为单选，默认实现支持多选配置）。Mock 数据中除“更多特征”外均为单选，更多特征可多选。
   - 点击同一分类相同选项再次点击可取消。
5. 点击重置：
   - 清除所有选择，悬浮控件恢复默认文案，列表自动刷新默认数据。
6. 点击确认：
   - 将选中条件集合传递给列表 ViewModel，触发同城数据刷新。
   - 收起面板，更新悬浮控件文案。

## UI 设计细节
- **悬浮容器高度**：与定位控件保持 68pt，总高度包含上下 8pt 间距。
- **背景色**：浅色模式 `#EBFBFB`，深色模式 `#28283A`（沿用现有风格）。
- **筛选入口按钮**：右侧展示带箭头的按钮（图示「筛选」+ icon）。在有筛选结果时改成“修改筛选”，并额外出现“重置”次要按钮。
- **面板容器**：圆角 16pt，白色背景；使用自动布局（Masonry）。顶部留 12pt padding，分类名字体 14/medium，选项字体 14/regular。
- **选项布局**：使用 `UICollectionView` 或自定义 flow layout；单元最小宽度 = `(屏宽 - 左右 24 - (列数-1)*8)/4`。选项宽度根据文本长度 + padding，自适应但不超过四列。

## 数据结构与状态管理
- 本地定义 `SOSameCityFilterCategory` 与 `SOSameCityFilterOption`：
  ```objc
  @interface SOSameCityFilterOption : NSObject
  @property (nonatomic, copy) NSString *identifier; // 与后端字段对齐
  @property (nonatomic, copy) NSString *title;
  @property (nonatomic, assign) BOOL selected;
  @end

  @interface SOSameCityFilterCategory : NSObject
  @property (nonatomic, copy) NSString *identifier;
  @property (nonatomic, copy) NSString *title;
  @property (nonatomic, assign) BOOL multiSelect; // mock 数据控制
  @property (nonatomic, copy) NSArray<SOSameCityFilterOption *> *options;
  @end
  ```
- ViewController 新增属性：
  - `filterEntryView`（悬浮控件）；
  - `filterPanel`（面板容器，含 collection view）；
  - `filterCategories`（数据源数组）。
- 状态切换：
  - `SORecommandSubSameCityViewController` 内维护当前选中条件数组；
  - 通过 block/ delegate 将确认后的条件推送给 `SORecommandSubViewModel`（新建方法 `-updateFilter:`）。
  - 互斥逻辑：`setupTableHeaderView` 中优先判断定位弹窗；当不展示定位弹窗时，插入筛选悬浮控件。

## Mock 数据
`SORecommandSubSameCityViewController` 初始化时注入如下 mock：
```objc
@[
  @{ @"gender" : @[@"男性", @"女性", @"不限"] },
  @{ @"distance" : @[@"3km", @"5km", @"10km", @"不限"] },
  @{ @"status" : @[@"当前在线", @"刚刚在线", @"今日活跃", @"不限"] },
  @{ @"publish_time" : @[@"5min", @"30min", @"1h", @"不限"] },
  @{ @"feature" : @[@"情感", @"健身", @"游戏", @"职场", @"美食", @"不限"] }
]
```
- 其中 `feature` 支持多选，其他单选。
- 将 identifier 与展示 title 解耦，方便后续接入真实接口。

## 交互与埋点建议
- 悬浮控件曝光埋点：`CitySquare_FilterFloat_Show`，参数包含是否有选中条件。
- 面板曝光埋点：`CitySquare_FilterPanel_Show`。
- 选项点击埋点：`CitySquare_FilterOption_Click`，携带分类/选项 id、选中态。
- 确认/重置埋点：`CitySquare_FilterAction_Click`，`action`=confirm/reset。
- 提交后刷新成功/失败已有逻辑可沿用列表请求事件。

## 接入方案
1. **UI 结构改造**
   - 在 VC 中新增 `filterEntryView`，布局与现有 header 一致；使用 `updateTableHeaderViewNeedPaddingTop:` 控制 table header padding。
   - 保留原定位 header 逻辑，通过布尔值控制两者互斥。
2. **面板组件实现**
   - 新建 `SOSameCityFilterPanelView`（UIView 子类），内部使用 collection view + 自定义 cell。通过 `showInView:` `dismiss` 管理展示。
   - 向外暴露 block：`onConfirm`、`onReset`、`onOptionToggle`。
3. **状态同步**
   - VC 内部维护 `selectedOptions` 字典；点击确认时构造参数交还给 ViewModel。
   - ViewModel 增加属性 `filterParams` 和公开方法 `- (void)applyFilterParams:(NSDictionary *)params`；数据请求时附带参数。
   - Mock 阶段调用 `requestSameCityListWithFilter:` 触发刷新（若尚无接口可只刷新 UI）。
4. **互斥与生命周期**
   - `setupTableHeaderView` 先决定是否展示定位 header；否则展示筛选 entry。
   - 列表滚动时保持 entry 悬浮：沿用当前 headerView 的 top 调整逻辑，统一替换为容器 view。
5. **兼容与性能**
   - 保持与深色模式适配（文本/背景/边框颜色通过 `SOColorDefine` 获取）。
   - Panel 出现时禁用 table 滚动，收起后恢复。

## 后续拓展
- 替换 mock 数据为接口返回，增加默认选中状态。
- 支持记忆上次筛选结果（持久化到用户级缓存）。
- 如需多端同步，可通过 SOAB 策略控制默认条件或曝光阈值。

