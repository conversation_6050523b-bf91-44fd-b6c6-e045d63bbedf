//
//  SOInteractionItemView.m
//  SoulUIKit
//
//  Created by summer on 2021/5/17.
//

#import "SOInteractionItemView.h"
#import "SOInteractionShareItemView.h"
#import "SOInteractionLikeItemView.h"
#import "SOInteractionCommitView.h"
#import "SOInteractionInputItemView.h"
#import "SOInteractionCollectItemView.h"
#import "SOInteractionGiftItemView.h"
#import "SOInteractionNewGiftView.h"
#import "SOInteractionFreeGiftView.h"
#import "SOInteractionPostExposeView.h"
#import "SOInteractionChatItemView.h"
#import "SOQuestionAnswerItemView.h"
#import "SOInteractionRecommandUserChatItemView.h"
#import "SOInteractionPatItemView.h"
#import <SOUGCBaseUI/SOUGCBaseUI-Swift.h>
#import <SoulBussinessKit/SoulFontHelper.h>

CGFloat const SOInteractionItemWidth = 72.f;
CGFloat const SOInteractionItemInterval = 8.f;
CGFloat const SOInteractionIconWidth = 24.f;
CGFloat const SOInteractionIconHeight = 24.f;
CGFloat const SOInteractionIntervalIconAndTitle = 4.f;

@implementation SOInteractionItemView

/*
 SOInteractionModuleItemType_Share,
 SOInteractionModuleItemType_Like,
 SOInteractionModuleItemType_Commit
 */

+ (instancetype)componentView:(SOInteractionModuleItemType)type{
    switch (type) {
        case SOInteractionModuleItemType_Share:
            return [[SOInteractionShareItemView alloc] init];
        case SOInteractionModuleItemType_Collect:
            return [[SOInteractionCollectItemView alloc] init];
        case SOInteractionModuleItemType_Like:
            return [[SOInteractionLikeItemView alloc] init];
        case SOInteractionModuleItemType_Commit:
            return [[SOInteractionCommitView alloc] init];
        case SOInteractionModuleItemType_FastCommit:
            return [[SOInteractionInputItemView alloc] init];
        case SOInteractionModuleItemType_Gift:
            return [[SOInteractionGiftItemView alloc] init];
        case SOInteractionModuleItemType_NewGift:
            return [SOInteractionNewGiftView new];
        case SOInteractionModuleItemType_FreeGift:
            return [SOInteractionFreeGiftView new];
        case SOInteractionModuleItemType_PostExpose:
            return [SOInteractionPostExposeView new];
        case SOInteractionModuleItemType_Chat:
            return [SOInteractionChatItemView new];
        case SOInteractionModuleItemType_QuestionToAnswer:
            return [SOQuestionAnswerItemView new];
        case SOInteractionModuleItemType_RecommandUserChat:
            return [SOInteractionRecommandUserChatItemView new];
        case SOInteractionModuleItemType_Pat:
            return [SOInteractionPatItemView new];
        default:
            break;
    }
    return nil;
}

- (instancetype)init{
    if (self = [super init]) {
        _iconTextMargin = SOInteractionIntervalIconAndTitle;
    }
    return self;
}

- (void)setPost:(SOPost *)post placeHolder:(NSString *)placeHolder{
    _post = post;
}

- (void)resetSubviewsFrame{
    
}

- (void)setOnlyDayMode:(BOOL)onlyDayMode{
    _onlyDayMode = onlyDayMode;
    self.textLabel.textColor = self.normalTextColor;
}
#pragma mark - getter
- (UILabel *)textLabel{
    if (!_textLabel) {
        _textLabel = [[UILabel alloc] init];
        _textLabel.userInteractionEnabled = YES;
        _textLabel.textAlignment = NSTextAlignmentLeft;
        _textLabel.textColor = self.normalTextColor;
        _textLabel.font =  self.normalTextFont;
    }
    return _textLabel;
}

/// 通用的文本色
- (UIColor *)normalTextColor{
    NSInteger colorTag = 6;
    return self.onlyDayMode ? [SOColorDefine soLightColor:colorTag] : SOStaticColor(colorTag);
}

- (UIFont *)normalTextFont {
    NSString *fontName = [SOFontDefine fontRegularName];
    return SOAppFont(fontName, 13);
}

@end
