//
//  SOInteractionPatItemView.m
//  SOPostModuleService
//
//  Created by AI Assistant on 2025/01/27.
//

#import "SOInteractionPatItemView.h"
#import <SoulAssets/SoulAssets.h>
#import <SoulBussinessKit/SoulFontHelper.h>
#import <SoulUIKit/UIView+SOAddition.h>

@interface SOInteractionPatItemView ()

@property (nonatomic, strong) UITapGestureRecognizer *tapGesture;

@end

@implementation SOInteractionPatItemView

- (instancetype)init {
    if (self = [super init]) {
        [self _initViews];
    }
    return self;
}

- (void)_initViews {
    [self addSubview:self.patIconImageView];
    [self addSubview:self.textLabel];
    
    self.tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapGestureAction:)];
    [self addGestureRecognizer:self.tapGesture];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    [self resetSubviewsFrame];
}

- (void)resetSubviewsFrame {
    CGFloat iconWidth = SOInteractionIconWidth;
    CGFloat iconHeight = SOInteractionIconHeight;
    CGFloat totalWidth = self.frame.size.width;
    CGFloat totalHeight = self.frame.size.height;
    
    // 计算文本尺寸
    CGSize textSize = [self.textLabel.text sizeWithAttributes:@{NSFontAttributeName: self.textLabel.font}];
    
    // 计算总内容宽度
    CGFloat contentWidth = iconWidth + self.iconTextMargin + textSize.width;
    CGFloat startX = (totalWidth - contentWidth) / 2.0;
    
    // 设置图标位置
    self.patIconImageView.frame = CGRectMake(startX, (totalHeight - iconHeight) / 2.0, iconWidth, iconHeight);
    
    // 设置文本位置
    CGFloat textX = startX + iconWidth + self.iconTextMargin;
    self.textLabel.frame = CGRectMake(textX, (totalHeight - textSize.height) / 2.0, textSize.width, textSize.height);
}

- (void)setPost:(SOPost *)post placeHolder:(NSString *)placeHolder {
    [super setPost:post placeHolder:placeHolder];
    
    // 更新图标状态
    [self updatePatIcon];
    
    // 更新文本
    NSString *countText = post.patCount && post.patCount.integerValue > 0 ? post.patCountStr : @"拍一拍";
    self.textLabel.text = countText;
    self.textLabel.textColor = [self normalTextColor];
    self.textLabel.font = [self normalTextFont];
    
    [self resetSubviewsFrame];
}

- (void)updatePatIcon {
    if (self.post.hasPatted) {
        // 使用现有的私聊图标作为已拍一拍状态
        self.patIconImageView.image = [UIImage square_imageNamed:@"private_chat_selected"];
    } else {
        // 使用现有的私聊图标作为未拍一拍状态
        self.patIconImageView.image = [UIImage square_imageNamed:@"private_chat_normal"];
    }
}

- (void)pat {
    if (!self.post.hasPatted) {
        // 更新本地状态
        self.post.hasPatted = YES;
        NSInteger currentCount = self.post.patCount ? self.post.patCount.integerValue : 0;
        self.post.patCount = @(currentCount + 1);
        
        // 更新UI
        [self updatePatIcon];
        self.textLabel.text = self.post.patCountStr;
        [self resetSubviewsFrame];
    }
}

- (void)tapGestureAction:(UITapGestureRecognizer *)tap {
    [self pat];

    // 延迟调用回调，确保UI更新完成
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(callTapActionBlock) object:nil];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self callTapActionBlock];
    });
}

- (void)callTapActionBlock {
    if (self.tapActionBlock) {
        self.tapActionBlock();
    }
}

#pragma mark - Lazy Loading

- (UIImageView *)patIconImageView {
    if (!_patIconImageView) {
        _patIconImageView = [[UIImageView alloc] init];
        _patIconImageView.contentMode = UIViewContentModeScaleAspectFit;
        _patIconImageView.image = [UIImage square_imageNamed:@"private_chat_normal"];
    }
    return _patIconImageView;
}

@end
