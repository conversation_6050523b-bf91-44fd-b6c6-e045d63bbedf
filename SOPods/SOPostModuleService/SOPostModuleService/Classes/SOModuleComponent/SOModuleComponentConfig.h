//
//  SOModuleComponentConfig.h
//  SoulUIKit
//
//  Created by summer on 2021/5/14.
//

#import <Foundation/Foundation.h>
#import <SoulCore/SOPost.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, SOInteractionModuleComponentType) {
    SOInteractionModuleComponent_Unknown = 0,
    SOInteractionModuleComponent_Square,
    SOInteractionModuleComponent_Discover,
    SOInteractionModuleComponent_PostDetail,
    SOInteractionModuleComponent_MediaWithoutGift,
    SOInteractionModuleComponent_MediaWithGift,
    SOInteractionModuleComponent_SquareB,
};

typedef NS_ENUM(NSUInteger, SOInteractionModuleItemType) {
    SOInteractionModuleItemType_Unknown = 0,
    SOInteractionModuleItemType_Share,
    SOInteractionModuleItemType_Collect,
    SOInteractionModuleItemType_Like,
    SOInteractionModuleItemType_Commit,
    SOInteractionModuleItemType_FastCommit,
    SOInteractionModuleItemType_Gift,
    SOInteractionModuleItemType_NewGift,
    SOInteractionModuleItemType_FreeGift,
    SOInteractionModuleItemType_PostExpose,
    SOInteractionModuleItemType_Chat,
    SOInteractionModuleItemType_QuestionToAnswer,
    SOInteractionModuleItemType_RecommandUserChat,
    SOInteractionModuleItemType_Pat, // 拍一拍类型
};

@interface SOInteractionModuleComponentData : NSObject
/// 场景type
@property (nonatomic, assign) SOInteractionModuleComponentType sceneType;
@property(nonatomic, strong) SOPost *post;
@property(nonatomic, assign) SOInteractionModuleItemType type;
@property(nonatomic, copy) void(^tapBlock)(void);
@property(nonatomic, copy) void(^doubleClickBlock)(void);
@property(nonatomic, copy) void(^longPressBlock)(void);

/// 普通图片
@property(nonatomic, strong, nullable) UIImage *normalImg;
/// 选中图片
@property(nonatomic, strong, nullable) UIImage *selectedImg;
/// 播放lottie 替换
@property(nonatomic, strong, nullable) NSString *lottieName;

@end

@interface SOInteractionModuleComponentCommentData: SOInteractionModuleComponentData

/// 默认文案
@property(nonatomic, copy, nullable) NSString *placeHolder;
/// 默认字体颜色
@property(nonatomic, copy, nullable) UIColor *textColor;

@end
NS_ASSUME_NONNULL_END
