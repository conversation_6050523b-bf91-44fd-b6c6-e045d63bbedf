//
//  SOSquareTimelineCell.h
//  Soul_New
//
//  Created by fang on 2019/3/15.
//  Copyright © 2019 Soul. All rights reserved.
//
#import <SoulBussinessKit/SoulBussinessKit.h>
#import <SOUGCBaseUI/SOSquareTimelineBaseCell.h>
#import "SATagSquareUserListView.h"
#import "SOSquareTimelineMediaViewProtocol.h"
//重构之后的 view
#import <SoulProtocols/SOSquareTimelineCellProtocal.h>
#import <SOVideoPlayer/SOVideoKit.h>
#import "SOSquareTimelineCellDelegate.h"
#import "SOSquareTimelineProfileView.h"
#import "SOSquareMusicView.h"
#import "SOSquareTimelineAudioView.h"
#import "SOSquareTimelineTextView.h"
#import "SOSquareTimelineVoteView.h"
#import "SOSquareTimelineVoteCountView.h"
#import "SOSquareTimelineLocationView.h"
#import "SOSquareTimelineLayout.h"
#import "SOSquareTimelineAnswerView.h"
#import "SOSquareAVMixtureView.h"
#import <SoulBussinessKit/SORoomCardView.h>
#import "SOSquareTimelineNewToolbarView.h"
#import "SOInteractionLikeItemView.h"
#import "SOSquareTimelinePicsView.h"
#import "SOSquareHotCommentContainer.h"

#import <SoulProtocols/SoulProtocols.h>
#import <SOPostModuleService/SOCommonAnchorContainer.h>
#import <SOPostModuleService/SOPostCommonTopProfileView.h>

@class SOHotRankTagView, AIGCRegulationView;

@interface SOSquareTimelineCell : SOSquareTimelineBaseCell<UIGestureRecognizerDelegate,SoulUIActorDelegate, SOSquareTimelinePicsViewDelegate>

@property (nonatomic, copy) void (^stopAnima)(BOOL isStop);

@property (nonatomic, copy) void (^musicChange)(SOSquareTimelineCell *squareCell);

@property (nonatomic, strong) SOSquareTimelineProfileView *profileView;
@property (nonatomic, strong) SOPostCommonTopProfileView *topNewProfileView;
@property (nonatomic, strong) SATagSquareUserListView *userListView;
@property (nonatomic, strong) SOSquareMusicView* musicView;
@property (nonatomic, strong) SOSquareTimelinePicsView *mediaView;
@property (nonatomic, strong) SOSquareTimelineAudioView *audioView;
@property (nonatomic, strong) SOSquareTimelineTextView *textView;
@property (nonatomic, strong) SOSquareTimelineVoteView *voteView;
@property (nonatomic, strong) SOSquareTimelineVoteCountView *voteCountView;
@property (nonatomic, strong) SOPostLocationStackView *locationView;

//新/老 问题卡片样式
@property (nonatomic, strong) SOQuestionAnswerCardContainer *questionAnswerContainer;
@property (nonatomic, strong) SOSquareNewQuestionAnswerContainer *questionAnswerNewContainer;

@property (nonatomic, strong, nullable) UIView<SOCommonBusinessViewProtocol> *businessView;
//公共关系
@property (nonatomic, strong) SOSquareRecommadConfigView *recommandRelationView;

@property (nonatomic, strong) UIButton *toolCoverBtn;
@property (nonatomic, strong) SOSquareTimelineNewToolbarView *toolBarNewView;

@property (nonatomic, strong) SOSquareTimelineAnswerView * answerView;

//音视混合视图
@property (nonatomic, strong) SOSquareAVMixtureView *avMixtureView;

@property (nonatomic, strong) SORoomCardView *roomCardView;
//AI生成提示
@property (nonatomic, strong) AIGCRegulationView * aiRegulationView;
//通用锚点区域
@property (nonatomic, strong) SOCommonAnchorContainer *anchorContainer;

//置顶区域
@property (nonatomic, strong) SOSquareTopView *topView;
/// 顶部背景图
@property (nonatomic, strong) UIImageView *bgTopImageView;

//神评 暖评 外漏
@property (nonatomic, strong) SOSquareHotCommentContainer *hotCommentContainer;

//推荐引导
@property (nonatomic, strong) SOSquareGudieTopView *gudieTopView;

@property (nonatomic, strong) UIImageView *longPressGuide;

//是否是来自热评, 如果是热评的话需要偏移到热评区
@property (nonatomic, assign) BOOL isFromHotComment;
@property (nonatomic, strong) SOVideoPlayer *player;

@property (nonatomic, strong) NSString *fileUrl;
@property (nonatomic) NSInteger audioDuration;
@property (nonatomic) NSInteger remainTime;
@property (nonatomic, assign) CGPoint lastTouchPoint;
@property (nonatomic, strong) NSTimer *timer;
@property (nonatomic, assign) BOOL m_bIsPlay;
@property (nonatomic, strong) NSMutableArray *barrage;

@property (nonatomic, assign) CGFloat playerProgress;

@property (nonatomic, strong) SOSquareTimelineLayout *layout;

@property (nonatomic, weak) id<SOSquareTimelineCellDelegate> delegate;
@property (nonatomic, copy) dispatch_block_t shareAnimationBlock;

@property (nonatomic, assign) BOOL emojiPlaying;

@property (nonatomic, strong) UIView *topLineView;

@property (nonatomic, strong) SORiskWarningContainer *riskContainer;

@property (nonatomic, strong, nullable) SOPostHeatingBannerView *heartingBannerView;


@property (nonatomic, strong) NSString * _Nullable hitTestPostId;

@property (nonatomic, assign) NSInteger selectedPhotoIndex;
/// viewPreView 曝光市场计算开始时间
@property (nonatomic, assign) NSTimeInterval videoPreViewBeginTime;
/// 是否完全播放结束结束 时间
@property (nonatomic, assign) NSTimeInterval videPlayCompleteTime;

/// 在tableView 上注册cell
+ (void)registeCellForTableView:(UITableView *)tableView;
+ (UITableViewCell *)dequeueReusableCellForTableView:(UITableView *)tableView layout:(SOSquareTimelineLayout *)layout;

- (void)layoutBottomPartSubViews;//点击展开 刷新高度(外部手动调用，避免影响视频播放器的播放)
- (void)willDisplay;
- (void)didEndDisplaying;

- (void)doubleClickLike;

//音乐
- (void)startTimerTime;
- (void)endTimer;

- (void)setTapActionForPostDetail:(UIView *)view;
- (void)setDoubleAction:(UIView *)view;

- (void)reloadData;

- (void)autoPlay;

- (void)showPlayIcon;

/// 检查和hitTest 对应的post 是否一致
- (BOOL)checkHitTestPostId;

/// 处理拍一拍动作
- (void)handlePatAction;

@end

