//
//  SOSquareTimelineCell.m
//  Soul_New
//
//  Created by fang on 2019/3/15.
//  Copyright © 2019 Soul. All rights reserved.
//
#import "SOSquareTimelineCell.h"
#import <SoulUIKit/UIView+SOAddition.h>
#import "SOInteractionLikeItemView.h"
#import <SoulCoreBase/SoulCoreBase.h>
#import <AFNetworking/AFNetworkReachabilityManager.h>
#import <SoulABStrategy/SoulConfigManager.h>
#import <SoulABStrategy/ABTestMultiStrategy.h>
#import <SoulAssets/UIImage+SoulAssets.h>
#import <SoulCoreBase/SOAudioSessionCategoryManager.h>
#import "SOSquareTimelineCellProtocol.h"
#import <SoulEffectPlayer/SoulEffectPlayerVideoCache.h>
#import "SONetworkPromptsView.h"
#import "SOPostEventExposeManage.h"
#import <SoulProtocols/SoulReleaseSkipManagerProtocol.h>
#import <SOPostModuleService/SOPostModuleService-swift.h>
#import <SoulBussinessKit/SoulBussinessKit-Swift.h>
#import <SOPostModuleService/SOInteractionCommitView.h>
#import <SOUGCBaseUI/SOUGCBaseUI.h>
#import <SoulCoreBase/SOViewDisplayLinkManager.h>
#import <SODarkMode/SODarkModeManager.h>
#import <SoulLog/SLogAPI.h>
#import <SakuraKit/TXSakuraKit.h>
#import <SOUGCBaseUI/SOSquareLinkMonitor.h>
#import <SoulBussinessKit/SOCommonPostHandler.h>
#import <SoulUserModule/SoulUserIDUtils.h>

@interface SOSquareTimelineCell ()<SOVideoPlayerDelegate,SOSquareTimelineAudioViewDelegate>

@property (nonatomic, strong) UIImageView *topBackGroundView;
@property (nonatomic, strong) UIImageView *bottomBackGroundView;
@property(nonatomic, assign) BOOL isVideoViewVisable;
@property(nonatomic, strong) UILongPressGestureRecognizer *longGesture;

@property (nonatomic, strong) UIView *relationLine;

@property (nonatomic, strong) SOSquareAudioTranslationView *audioTranslationView;

@end

@implementation SOSquareTimelineCell

+ (void)registeCellForTableView:(UITableView *)tableView{
    [tableView registerClass:self forCellReuseIdentifier:Cell_Noraml];
    [tableView registerClass:self forCellReuseIdentifier:Cell_Descovery];
    [tableView registerClass:self forCellReuseIdentifier:Cell_Answer];
    [tableView registerClass:self forCellReuseIdentifier:Cell_MUSIC_STORY];
    [tableView registerClass:self forCellReuseIdentifier:Cell_AudioMoji];
    [tableView registerClass:self forCellReuseIdentifier:Cell_TEXT];
    [tableView registerClass:self forCellReuseIdentifier:Cell_AUDIO];
    [tableView registerClass:self forCellReuseIdentifier:Cell_IMG];
    [tableView registerClass:self forCellReuseIdentifier:Cell_VIDEO];
    [tableView registerClass:self forCellReuseIdentifier:Cell_IMG_VDO_MIX];
}

+ (UITableViewCell *)dequeueReusableCellForTableView:(UITableView *)tableView layout:(SOSquareTimelineLayout *)layout {
    NSString *identifier = Cell_Noraml;
    SOPost *post = layout.post;
//    if (layout.post.globalViewModel.bizType.integerValue > 0) { //动态注册commonbusiness
//        identifier = [NSString stringWithFormat:@"%@_%@",identifier,layout.post.globalViewModel.bizType];
//        UITableViewCell * cell = [tableView dequeueReusableCellWithIdentifier:identifier];
//        if (cell == nil){
//            cell = [[SOSquareTimelineCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identifier];
//        }
//        return cell;
//    }
    if (layout.musicHeight > 0){
        identifier = Cell_MUSIC_STORY;
    } else if (post.officialTags.answerTag){
        identifier = Cell_Answer;
    } else if (layout.mixtureAudioLayout.height > 0){
        identifier = Cell_AudioMoji;
    } else if ([post.type isEqualToString:@"TEXT"]){
        identifier = Cell_TEXT;
    } else if ([post.type isEqualToString:@"IMAGE"]) {
        identifier = Cell_IMG;
    } else if ([post.type isEqualToString:@"IMG_VDO_MIX"]){
        identifier = Cell_IMG_VDO_MIX;
    } else if ([post.type isEqualToString:@"VIDEO"] ){
        identifier = Cell_VIDEO;
    } else if ([post.type isEqualToString:@"AUDIO"] ) {
        identifier = Cell_AUDIO;
    }
    UITableViewCell * cell = [tableView dequeueReusableCellWithIdentifier:identifier];
    if (cell == nil){
        cell = [[SOSquareTimelineCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:Cell_Noraml];
    }
    return cell;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    self.backgroundColor = GET_COLOR(0);
    //这三个层级必须最下面
    [self.contentView addSubview:self.topBackGroundView];
    [self.contentView addSubview:self.bottomBackGroundView];
    [self.contentView addSubview:self.bgTopImageView];
    [self.contentView addSubview:self.relationLine];
    
    [self layoutCustomSubviews];
    [self.contentView bringSubviewToFront:self.musicView];
    [self addLongPressGesture];
    [self addNotification];
    return self;
}

- (void)prepareForReuse {
    [super prepareForReuse];
    NSString *fileUrl = [self.layout.post.attachments firstObject].fileUrl;
    NSString *videoUrl = [self.so_videoPlayer.player.url absoluteString];
    if(self.so_videoPlayer && ![fileUrl isEqualToString:videoUrl]){
        [self.so_videoPlayer stop];
    }
    self.isVideoViewVisable = NO;//cell复用时，重置可见标记
    self.playerProgress = 0;
}

- (void)layoutCustomSubviews {
    //必存在的UI元素
    
    [self.contentView addSubview:self.topNewProfileView];
    [self.contentView addSubview:self.profileView];
    
    [self.contentView addSubview:self.textView];
   if ([self.reuseIdentifier hasPrefix:Cell_MUSIC_STORY]){
        [self.contentView addSubview:self.musicView];
    
    } else if ([self.reuseIdentifier hasPrefix:Cell_AudioMoji]){
        [self.contentView addSubview:self.avMixtureView];
    } else if ([self.reuseIdentifier hasPrefix:Cell_Answer]){
        [self.contentView addSubview:self.answerView];
        
    } else if ([self.reuseIdentifier hasPrefix:Cell_AUDIO]){
        [self.contentView addSubview:self.audioView];
    } else if ([self.reuseIdentifier hasPrefix:Cell_IMG] || [self.reuseIdentifier  hasPrefix:Cell_VIDEO] || [self.reuseIdentifier  hasPrefix:Cell_IMG_VDO_MIX]){
        [self.contentView addSubview:self.mediaView];
    }
    [self.contentView addSubview:self.audioTranslationView];
    
    //公共业务的注册
//    if ([self.reuseIdentifier hasPrefix:[NSString stringWithFormat:@"%@_",Cell_Noraml]]) {
//        NSArray <NSString *>*arr = [self.reuseIdentifier  componentsSeparatedByString:@"_"];
//        if (arr.lastObject.integerValue > 0) {
//            self.businessView = (UIView <SOCommonBusinessViewProtocol>*) [SOCommonBusinessManger makeDestinationWithType:arr.lastObject.integerValue];
//            if (self.businessView) {
//                [self.contentView addSubview:self.businessView];
//            }
//        }
//    }
    [self.contentView addSubview:self.toolCoverBtn];
    [self.contentView addSubview:self.toolBarNewView];
    //长按引导
    [self.contentView addSubview:self.longPressGuide];
}

- (void)addLongPressGesture {
    @weakify(self);
    UILongPressGestureRecognizer *longGesture = [[UILongPressGestureRecognizer alloc] init];
    self.longGesture = longGesture;
    longGesture.minimumPressDuration = 0.5;
    [[longGesture rac_gestureSignal] subscribeNext:^(__kindof UIGestureRecognizer * _Nullable x) {
        @strongify(self);
        switch (x.state) {
            case UIGestureRecognizerStateBegan: {
                if (self.delegate && [self.delegate respondsToSelector:@selector(cellLongPressFeedBackWithGesture:withCell:)]) {
                    [self.delegate cellLongPressFeedBackWithGesture:longGesture withCell:self];
                }
            }
                break;
                
            case UIGestureRecognizerStateEnded: {
              
            }
                break;
                
            default:
                break;
        }
                
    }];
    [self addGestureRecognizer:longGesture];
}

- (void)setCellItem:(id<SOTableviewCellItemProtocol>)cellItem {
    if (![cellItem isKindOfClass:SOSquareTimelineLayout.class]) {
        return;
    }
    [super setCellItem:cellItem];
    if (cellItem.delegate) {
        self.delegate = cellItem.delegate;
    }

    NSTimeInterval beginTime = [[NSDate date] timeIntervalSince1970] * 1000;
    self.indexPath = cellItem.indexPath;
    [self setLayout:(SOSquareTimelineLayout *)cellItem];
    
    NSTimeInterval endTime = [[NSDate date] timeIntervalSince1970] * 1000;
    NSInteger margin = endTime - beginTime;
    if (cellItem.indexPath.section == 0) {//统计第一个cell的组装时间
        [[MMKV defaultMMKV] setString:SOString(@(margin)) forKey:@"load_square_present_time"];
    }
    [self.contentView bringSubviewToFront:self.longPressGuide];
}

- (void)setLayout:(SOSquareTimelineLayout *)layout {
    _layout = layout;
    SOPost *post = layout.post;
    //关注广场没有长按disLike
    if ([self ignoreDisLikeBorad]) {
        [self removeGestureRecognizer:self.longGesture];
    } else {
        [self addGestureRecognizer:self.longGesture];
    }
    
    SOSquareLinkMonitor *linkMonitor = nil;
    if ([self.delegate respondsToSelector:@selector(getLinkMonitor)]) {
        linkMonitor = [self.delegate getLinkMonitor];
        if (!layout.isDisplay) {
            /* 内容广场性能性能埋点 */
            [linkMonitor performWithEventName:KSquarePerformance step:@"100040" withExtMap:@{
                @"postId" : SOTrackString(post.postId),
                @"position" : SOString(@(layout.indexPath.section))
            }];
        }
    } else {
        NSAssert(NO, @"linkMonitor is nil");
    }
    
    @weakify(self);
    if ([post isKindOfClass:SOPost.class] && post.isGetSessionEnable && post.getSessionIdBlock == nil) {
        post.getSessionIdBlock = ^NSString * _Nonnull{
            @strongify(self);
            if ([self.viewController conformsToProtocol:@protocol(SOPostListViewControllerProtocol)]) {
                id<SOPostListViewControllerProtocol> vc = (id<SOPostListViewControllerProtocol>)self.viewController;
                if ([vc respondsToSelector:@selector(sessionId)]) {
                    return [vc sessionId];
                }
            }
            return nil;
        };
    }
    if (post.indexPath == nil) {
        post.indexPath = layout.indexPath;
    }
    
    //长按引导区域
    CGFloat guideLeft = self.layout.config.contentLeft;
    self.longPressGuide.frame = CGRectMake(guideLeft + ((self.layout.config.contentWidth - 186)/2.0), (guideLeft + self.layout.height - 101)/2.0, 186, 101);
    self.longPressGuide.hidden = YES;
    
    [layout.extendsLayout enumerateObjectsUsingBlock:^(id<SOCommonViewLayoutProtocol>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (!obj.isShow){
            UIView<SOCommonViewProtocol> *view = [self.contentView subViewForLayout:obj];
            view.hidden = true;
            return;
        }
        UIView<SOCommonViewProtocol> *view = [self.contentView addSubViewWithLayout:obj];
        view.frame = obj.itemFrame;
        view.hidden = !obj.isShow;
        if (obj.isShow) {
            if([view respondsToSelector:@selector(setDelegate:)]) {
                [view setDelegate:self];
            }
            if ([view respondsToSelector:@selector(setViewLayout:)]) {
                [view setViewLayout:obj];
            }
            if (obj == layout.voteTitleLayout) {
                [self setTapActionForPostDetail:view];
            }
        }
    }];
    
    
    //push进来的添加一个背景
    self.topBackGroundView.frame = CGRectMake(0, 0, KScreenWidth, 102 * KScreenWidth/375.0);
    self.bottomBackGroundView.frame = CGRectMake(0, layout.height - 82, KScreenWidth, 60);
    
    if ([layout.post.fromSource isEqualToString:@"PUSH"]) {
        self.topBackGroundView.hidden = NO;
        self.bottomBackGroundView.hidden = NO;
        _bottomBackGroundView.image = [UIImage square_imageNamed:@"push_v2_bg_bottom"];
    } else {
        self.topBackGroundView.hidden = YES;
        if (layout.post.configRelationEndColor) {
            self.bottomBackGroundView.hidden = NO;
            _bottomBackGroundView.image = [UIImage square_imageNamed:@"relate_post_separator"];
        } else {
            self.bottomBackGroundView.hidden = YES;
        }
    }
    
    //置顶区域
    if (self.layout.topLayout.isShow) {//线和顶部区域是同时存在的 精华 和置顶
        if (!self.topView.superview) {
            [self.contentView addSubview:self.topView];
        }
        [_topView assiginTopViewWithLayout:self.layout.topLayout];
        self.topView.hidden = NO;
        /// 通栏试验隐藏 精选标志 帖子下的横线
        if (!self.topLineView.superview) {
            [self.contentView addSubview:self.topLineView];
        }
        self.topLineView.frame = CGRectMake(68, layout.height - 21.5, KScreenWidth - 68, 0.5);
        self.topLineView.hidden = NO;
    } else {
        _topView.hidden = YES;
        [_topView removeFromSuperview];
        _topLineView.hidden = YES;
    }
    
    //gudieTopView 引导到推荐
    if (_layout.gudieTopViewLayout.isShow) {
        if (!self.gudieTopView.superview) {
            [self.contentView addSubview:self.gudieTopView];
        }
        _gudieTopView.frame = CGRectMake(0, _layout.gudieTopViewLayout.PointY, kScreenWidth, _layout.gudieTopViewLayout.height);
        [self.gudieTopView setLayout:_layout.gudieTopViewLayout];
    } else {
        [_gudieTopView removeFromSuperview];
    }
    _gudieTopView.hidden = !_layout.gudieTopViewLayout.isShow;
    
    //用户信息区域
    _topNewProfileView.hidden = !layout.profileNewLayout.isShow;
    _topNewProfileView.delegate = (id)self.delegate;
    _profileView.hidden = !(layout.profileLayout.profileHeight > 0);
    if (layout.profileNewLayout.isShow) {
        _topNewProfileView.frame = CGRectMake(0, layout.profileNewLayout.PointY, KScreenWidth, layout.profileNewLayout.height);
        [_topNewProfileView setLayout:layout.profileNewLayout];
    } else if (layout.profileLayout.profileHeight) {
        _profileView.frame = CGRectMake(0, layout.profileLayout.profileY, KScreenWidth, layout.profileLayout.profileHeight);
        [_profileView setLayout:layout.profileLayout];
    }
    
    if (layout.userListHeight) {
        if (!self.userListView.superview) {
            [self.contentView addSubview:self.userListView];
        }
        CGFloat top = 0;
        if (layout.profileNewLayout.isShow) {
            top = _topNewProfileView.bottom;
        } else {
            top = _profileView.bottom;
        }
        _userListView.frame = CGRectMake(0, top, KScreenWidth, layout.userListHeight);
        [_userListView setModels:layout.users];
        _userListView.cell = self;
        _userListView.hidden = NO;
    } else {
        _userListView.hidden = YES;
        [_userListView removeFromSuperview];
    }
    
    _musicView.frame = CGRectMake(0, layout.musicY, KScreenWidth, layout.musicHeight);
    _musicView.leftMargin = self.layout.config.contentLeft;
    [_musicView setLayout:layout];
    
    _avMixtureView.frame = CGRectMake(0, self.layout.mixtureAudioLayout.PointY, kScreenWidth, self.layout.mixtureAudioLayout.height);
    [_avMixtureView reloadDataWithLayout:self.layout.mixtureAudioLayout];
    
    if (self.layout.commonBusinessLayout.type > 0) {
        [self.businessView removeFromSuperview];
        self.businessView  = nil;
        self.businessView = (UIView <SOCommonBusinessViewProtocol>*) [SOCommonBusinessManger makeDestinationWithType:self.layout.commonBusinessLayout.type];
        [self.contentView addSubview:self.businessView];
        
        self.businessView.frame = CGRectMake(self.layout.config.contentLeft, self.layout.commonBusinessLayout.PointY, self.layout.commonBusinessLayout.boxSize.CGSizeValue.width, self.layout.commonBusinessLayout.boxSize.CGSizeValue.height);
        [self.businessView setDataModel:self.layout.commonBusinessLayout];
    } else {
        [_businessView removeFromSuperview];
    }
    self.answerView.hidden = !layout.answerViewLayout.isShowAnswerView;
    self.mediaView.hidden = YES;
    
    /// 答案君
    if (layout.answerViewLayout.isShowAnswerView){
        [self.answerView setLayout:layout.answerViewLayout];
        self.answerView.frame = layout.answerViewLayout.itemFrame;
    } else {
        /// 图片视频
        self.mediaView.hidden = NO;
        [self.mediaView setLayout:layout.mediaPicsViewLayout];
        self.mediaView.frame = layout.mediaPicsViewLayout.itemFrame;
    }
    if (layout.audioViewLayout.itemHeight > 0){
        self.audioView.hidden = NO;
        self.audioView.frame = layout.audioViewLayout.itemFrame;
        self.audioView.layout = layout.audioViewLayout;
    } else {
        self.audioView.hidden = YES;
    }
    [self updateAudioTranslationViewWithLayout:layout];
    
    //问答卡片
    if (layout.questionAnswerLayout.isShow) {
        if (!self.questionAnswerContainer.superview) {
            [self.contentView addSubview:self.questionAnswerContainer];
        }
        _questionAnswerContainer.frame = CGRectMake(layout.questionAnswerLayout.PointX, layout.questionAnswerLayout.PointY, layout.questionAnswerLayout.width, layout.questionAnswerLayout.height);
        [_questionAnswerContainer setLayout:layout.questionAnswerLayout];
    } else {
        [_questionAnswerContainer removeFromSuperview];
    }
    _questionAnswerContainer.hidden = !layout.questionAnswerLayout.isShow;
    
    if (layout.questionAnswerNewLayout.isShow) {
        if (!self.questionAnswerNewContainer.superview) {
            [self.contentView addSubview:self.questionAnswerNewContainer];
        }
        _questionAnswerNewContainer.frame = CGRectMake(layout.questionAnswerNewLayout.PointX, layout.questionAnswerNewLayout.PointY, layout.questionAnswerNewLayout.width, layout.questionAnswerNewLayout.height);
        [_questionAnswerNewContainer setLayout:layout.questionAnswerNewLayout];
    } else {
        [_questionAnswerNewContainer removeFromSuperview];
    }
    _questionAnswerNewContainer.hidden = !layout.questionAnswerNewLayout.isShow;
   
    //风险提示
    if (layout.riskContainerLayout.isShow) {
        if (!self.riskContainer.superview) {
            [self.contentView addSubview:self.riskContainer];
        }
    } else {
        [_riskContainer removeFromSuperview];
    }
    _riskContainer.hidden = !layout.riskContainerLayout.isShow;
    [_riskContainer setLayout:layout.riskContainerLayout];

    // AI生成提示
    if (layout.aiRegulationLayout.isShow) {
        if (!self.aiRegulationView.superview) {
            [self.contentView addSubview:self.aiRegulationView];
        }
        self.aiRegulationView.hidden = NO;
        //setlayout:
        [self.aiRegulationView configViewWithLayout:layout.aiRegulationLayout];
    } else {
        [self.aiRegulationView removeFromSuperview];
        self.aiRegulationView.hidden = YES;
    }

    //通用锚点区域
    if (self.layout.anchorContainerLayout.height > 0) {
        if (!self.anchorContainer.superview) {
            [self.contentView addSubview:self.anchorContainer];
        }
    } else {
        [_anchorContainer removeFromSuperview];
    }
    _anchorContainer.hidden = self.layout.anchorContainerLayout.height == 0;
    [_anchorContainer setLayout:self.layout.anchorContainerLayout];
    
    //热评区域
    if (_layout.hotCommentLayout.height > 0) {
        if (!self.hotCommentContainer.superview) {
            [self.contentView addSubview:self.hotCommentContainer];
        }
        self.hotCommentContainer.hidden = NO;
        self.hotCommentContainer.frame = CGRectMake(0,_layout.hotCommentLayout.PointY , kScreenWidth, _layout.hotCommentLayout.height);
        
        //handler 赋值处理事件
        SOSquareHotCommentHandler *handler = [[SOSquareHotCommentHandler alloc] init];
        handler.container = _hotCommentContainer;
        _hotCommentContainer.handler = handler;
        
        [self.hotCommentContainer setLayout:_layout.hotCommentLayout];
    } else {
        _hotCommentContainer.hidden = YES;
        [_hotCommentContainer removeFromSuperview];
    }
    
    [self layoutBottomPartSubViews];
    [self.contentView bottomBorderWithWidth:self.layout.config.borderBottom];
    [self autoPlayFeedList];
    
    //关联对象的线
    if ([SOABValue(@"216026") isEqualToString:@"d"]) {
        /// relationLine 保持原本的层级
    } else {
        [self.contentView bringSubviewToFront:self.relationLine];
    }
    self.relationLine.frame = CGRectMake(36, 68, 2, layout.height - 68 - 15);
    if (post.isRelation && !post.relatePostId) {
        self.relationLine.hidden = NO;
    } else {
        self.relationLine.hidden = YES;
    }
    
    // 客户端皮肤
    if ([SOAppSkinManager.shared isSkinIdValid:layout.post.postSkinData.skinId]) {
        NSString *bgImage = SOString(layout.post.postSkinData.postBgUrl);
        NSString *bgImageNight = SOString(layout.post.postSkinData.postBgUrlNight);
        NSString *urlStr = [SODarkModeManager isDarkMode] ? bgImageNight: bgImage;
        NSURL *imageUrl = [SOAppSkinManager.shared getIncreaseSourceUrl:urlStr];
        if (imageUrl) {
            [self.bgTopImageView so_setImageWithURL:imageUrl];
            self.bgTopImageView.hidden = false;
        } else {
            _bgTopImageView.hidden = true;
        }
    } else {
        _bgTopImageView.hidden = true;
    }
    
    if (!layout.isDisplay) {
        /* 内容广场性能性能埋点 */
        [linkMonitor performWithEventName:KSquarePerformance step:@"100050" withExtMap:@{
            @"postId" : SOTrackString(post.postId),
            @"position" : SOString(@(layout.indexPath.section))
        }];
    }
    
    
}

- (void)updateAudioTranslationViewWithLayout:(SOSquareTimelineLayout *)layout {
    if ([layout.audioTranslationLayout isShow]) {
        self.audioTranslationView.hidden = NO;
        self.audioTranslationView.frame = layout.audioTranslationLayout.itemFrame;
        [self.audioTranslationView setViewLayout:layout.audioTranslationLayout];
    } else {
        self.audioTranslationView.hidden = YES;
    }
}

- (void)autoPlayFeedList {
    [[SOViewDisplayLinkManager shareInstance] unregisterView:self.mediaView];
    if([self.layout.post.type containsString:@"VIDEO"]) {
        @weakify(self);
        SOBizAction *visibleAction = [SOBizAction actionWithLimit:0.99 handler:^(SOBizAction * _Nonnull action) {
            @strongify(self);
            if(action.isAnswer) {
                self.isVideoViewVisable = YES;
            }else {
                self.isVideoViewVisable = NO;
                [self autoPlayAndStopVideo];
            }
        }];
        [[SOViewDisplayLinkManager shareInstance] registerView:self.mediaView actions:@[visibleAction]];
    }
}

- (void)layoutBottomPartSubViews {
    _textView.frame = CGRectMake(0, _layout.textY, KScreenWidth, _layout.textViewLayout.textHeight);
    [_textView setLayout:_layout];

    if (_layout.post.voteItemListModel) {
        if (!self.voteView.superview) {
            [self.contentView addSubview:self.voteView];
        }
        
        if (!self.voteCountView.superview) {
            [self.contentView addSubview:self.voteCountView];
        }
        self.voteView.hidden = NO;
        _voteView.frame = CGRectMake(0, _layout.voteY, KScreenWidth, _layout.voteHeight);
        [_voteView setLayout:_layout];
        _voteCountView.frame = CGRectMake(0, _layout.voteCountY, KScreenWidth, _layout.voteCountHeight);
        self.voteCountView.hidden = NO;
        [_voteCountView setLayout:_layout];
    } else {
        _voteView.hidden = YES;
        _voteCountView.hidden = YES;
        [_voteView removeFromSuperview];
        [_voteCountView removeFromSuperview];
    }
    
    if(_layout.post.postRoomProfileModel) {
        if (!self.roomCardView.superview) {
            [self.contentView addSubview:self.roomCardView];
        }
        self.roomCardView.hidden = NO;
        CGFloat width = MIN(280, KScreenScale(280));
        self.roomCardView.frame = CGRectMake(_layout.config.contentLeft, _layout.roomCardY, width, 150);
        _layout.post.postRoomProfileModel.btnEnable = YES;
        [self.roomCardView setupUIWithModel:_layout.post.postRoomProfileModel];
    } else {
        _roomCardView.hidden = YES;
        [_roomCardView removeFromSuperview];
    }
    
    if (_layout.locationHeight > 0) {
        self.locationView.layoutMargins = UIEdgeInsetsMake(0, self.layout.config.contentLeft, 0, 0);
        if (!self.locationView.superview) {
            [self.contentView addSubview:self.locationView];
        }
        _locationView.frame = CGRectMake(0, _layout.locationY, KScreenWidth, _layout.locationHeight);
        _locationView.post = _layout.post;
        _locationView.hidden = false;
    } else {
        [_locationView removeFromSuperview];
        _locationView.hidden = true;
    }
    
    //帖子加热banner
    if (self.layout.heartingBannerLayout.isShow) {
        if (!_heartingBannerView.superview) {
            [self.contentView addSubview:self.heartingBannerView];
        }
        [_heartingBannerView setLayout:self.layout.heartingBannerLayout];
    } else {
        [_heartingBannerView removeFromSuperview];
    }
    _heartingBannerView.hidden = !self.layout.heartingBannerLayout.isShow;
    
    //用户关系推荐
    if (self.layout.chatConfigLayout.isShow) {
        if (!self.recommandRelationView.superview) {
            [self.contentView addSubview:self.recommandRelationView];
        }
        _recommandRelationView.frame = CGRectMake(0, self.layout.chatConfigLayout.PointY, KScreenWidth, self.layout.chatConfigLayout.height);
        [_recommandRelationView setLayout:self.layout.chatConfigLayout];
    } else {
        [_recommandRelationView removeFromSuperview];
    }
    _recommandRelationView.hidden = !self.layout.chatConfigLayout.isShow;

    if (_layout.toolbarHeight) {
        _toolBarNewView.frame = CGRectMake(0, _layout.toolbarY, KScreenWidth, _layout.toolbarHeight);
        _toolCoverBtn.frame = CGRectMake(0, _layout.toolbarY, KScreenWidth, _layout.toolbarHeight);
        if (_layout.ignorePlayLikeAnimation) {//不要执行点赞动画
            _toolBarNewView.userInteractionEnabled = NO;
            _toolCoverBtn.hidden = NO;
        } else {
            _toolBarNewView.userInteractionEnabled = YES;
            _toolCoverBtn.hidden = YES;
        }
        [_toolBarNewView setLayout:_layout];
        SOInteractionLikeItemView *likeView = (SOInteractionLikeItemView *)_toolBarNewView.likeView;
        if ([likeView isKindOfClass:SOInteractionLikeItemView.class]){
            likeView.aniamtionContainerView = self.contentView;
            [likeView syncLikeIcon];
        }
    }
}

- (void)addNotification {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updatePostCommentCount:) name:@"USER_HAS_COMMENT_POST" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(videoPlayControl:) name:@"PostVideoPlayControlNoti" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(autoPlayAndStopVideo) name:@"MainSquareBecomeActiveNoti" object:nil];
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch {
    self.lastTouchPoint = [touch locationInView:self.contentView];
    if ([touch.view isKindOfClass:[YYLabel class]]) {
        if ([gestureRecognizer isKindOfClass:[UITapGestureRecognizer class]]) {
            UITapGestureRecognizer *tap = (UITapGestureRecognizer *)gestureRecognizer;
            if (tap.numberOfTapsRequired == 2) {
                return YES;
            }
        }
        YYLabel *label = (YYLabel *)touch.view;
        NSRange highlightRange;
        YYTextHighlight *highlight = [label _getHighlightAtPoint:[touch locationInView:label] range:&highlightRange];
        if (highlight) {
            return NO;
        }
        return YES;
    }
    return YES;
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super touchesBegan:touches withEvent:event];
    UITouch *touch = touches.allObjects.firstObject;
    self.lastTouchPoint = [touch locationInView:self.contentView];
}

- (void)setPlayer:(SOVideoPlayer *)player {
    SOAttachment *attachment = _layout.post.attachments.firstObject;
    BOOL isMixAudioVD = attachment.audioMojiUrl.length && [_layout.post.type isEqualToString:@"AUDIO"];
    
    if ([_layout.post.type containsString:@"VIDEO"] || isMixAudioVD) {
        _player = player;
        _player.obsevserProgress = YES;
        
        if (!isMixAudioVD) {
            _player.repeat = YES;
        } else {
            _player.repeat = NO;
        }
        self.so_videoPlayer = _player;
        
        if (isMixAudioVD) {
            self.so_playView = self.avMixtureView.videoView;
        } else {//常规视频
            self.so_playView = self.mediaView;
        }
        if ([_layout.post.type containsString:@"VIDEO"]) {
            _player.delegate = self;
        }
    } else {
        if (_player) {
            [_player stop];
            self.so_videoPlayer = nil;
            self.so_playView = nil;
            _player = nil;
        }
    }
}

- (BOOL)ignoreDisLikeBorad {
    //排除 关注下的 推荐列表
    BOOL reFollowSquare = _layout.routerType == SubSquareRouterTypeMainFollow && _layout.type == SOTimelineCellTypeFollowRecommend;
    
    if (_layout.post.specialBizModel.type == SOPostSpecialBizType_Top) {
        return YES;
    }
    
    if (_layout.routerType == SubSquareRouterTypeMainRecommand || _layout.routerType == SubSquareRouterTypeMainLocation || reFollowSquare) {
        return NO;
    }
    return YES;
}

#pragma mark -- SOVideoPlayerDelegate

- (void)videoPlayerDidUpdateProgress:(SOVideoPlayer *)player progress:(long)currentDuration {
    CGFloat totalDuration = CMTimeGetSeconds(self.player.player.totalDuration);
    CGFloat current = CMTimeGetSeconds(self.player.player.currentTime);
    
    CGFloat progress = 0.0;
    if (totalDuration > 0) {
        progress = current / totalDuration;
        progress = MIN(progress+0.05,1.0);
    }
    
    //进度条
    SOSquareTimeLinePicItemView *darwPlayerView = (SOSquareTimeLinePicItemView *)self.mediaView.pics.firstObject;
    [darwPlayerView updateProgress:progress];
    
    self.playerProgress = currentDuration;
    //cell 复用隐藏icon
    for (UIView *view in self.mediaView.playIcons) {
        view.hidden = YES;
    }
    
    static BOOL isSeekComplete = NO;
    NSInteger currentSeconds = (NSInteger)currentDuration;
    /// 根据实验配置相关的循环播放秒数
    CGFloat timeConfig = totalDuration;
    if (isSeekComplete == NO && currentSeconds < timeConfig){
        isSeekComplete = YES;
    }
    if (currentSeconds >= timeConfig && isSeekComplete) {
        [SOSquareTrackHandler postVideoStopPlay:self.layout.post player:player isComplete:YES view:self];
        [self.player seekTime:0];
        isSeekComplete = NO;
        [SOSquareTrackHandler postVideoBeginPlay:self.layout.post player:player];
    }
}

- (void)videoPlayerReadyToPlay:(SOVideoPlayer *)player {
    for (UIView *view in self.mediaView.playIcons) {
        view.hidden = YES;
    }
}

- (void)videoPlayerDidPlay:(SOVideoPlayer *)player {
    self.layout.playerBeginPlayTime = [[NSDate date] timeIntervalSince1970];
    DDLogInfo(@"square feed videoPlayerDidPlay ");
    NSTimeInterval current = [NSDate date].timeIntervalSince1970;
    if (self.videPlayCompleteTime <= 0 || (current - self.videPlayCompleteTime > 2) ){
        self.videoPreViewBeginTime = [[NSDate date] timeIntervalSince1970];
    }
    self.videPlayCompleteTime = 0;
    if (self.layout.transitionsProgress > 0) {
        [self.player seekTime:self.layout.transitionsProgress];
        self.layout.transitionsProgress = 0;
    }
    [SOSquareTrackHandler postVideoBeginPlay:self.layout.post player:player];
}

- (void)videoPlayerDidStop:(SOVideoPlayer *)player {
    if (player.player.currentTime.timescale > 0){
        [SOSquareTrackHandler postVideoStopPlay:self.layout.post player:player isComplete:NO view:self];
    }
    DDLogInfo(@"square feed videoPlayerDidStop ");
    ///结束时上报
    [self videoPreViewPlayerExposeDuration];
}

- (void)videoPlayerDidPause:(SOVideoPlayer *)player{
    if ( player.player.totalDuration.value == player.player.currentTime.value){
        return;
    }
    if (player.player.currentTime.timescale > 0) {
        [SOSquareTrackHandler postVideoStopPlay:self.layout.post player:player isComplete:NO view:self];
    }
    DDLogInfo(@"square feed videoPlayerDidPause ");
    ///结束时上报
    [self videoPreViewPlayerExposeDuration];
}

- (void)videoPlayerWillReplay:(SOVideoPlayer *)player {
    [SOSquareTrackHandler postVideoBeginPlay:self.layout.post player:player];
}

- (void)videoPlayerBeginBuffering:(SOVideoPlayer *)player {
    
}

- (void)videoPlayerEndBuffering:(SOVideoPlayer *)player {
    SOPost *post = self.layout.post;
    post.cattonCount ++;
}

- (void)videoPlayerCurrentDidPlayCompleted:(SOVideoPlayer *)player{

    DDLogInfo(@"square feed videoPlayerCurrentDidPlayCompleted ");
    self.videPlayCompleteTime = [NSDate date].timeIntervalSince1970;
//    ///结束时上报
//    [self videoPreViewPlayerExposeDuration];
    if (player.player.currentTime.timescale > 0){
        [SOSquareTrackHandler postVideoStopPlay:self.layout.post player:player isComplete:YES view:self];
        [SOSquareTrackHandler postVideoBeginPlay:self.layout.post player:player];
    }
}

/// 计算播放时间 并上报
- (void)exposePlayEndEvent:(NSTimeInterval)duration endType:(NSString *)endType{
    if (duration <= 0) {
        return;
    }
    NSMutableDictionary *params = [SOSquareTrackHandler eventParamForPost:self.layout.post];
    params[@"duration"] = @(duration);
    params[@"type"] = SOString(endType).length > 0 ? SOString(endType) : @"0";

    NSString *pageId = self.viewController.pageId;
    if ([pageId isEqualToString:PostSquare_Tag]) {
        [SoulEventManager eventExpose:@"TagSquare_VideoPlayOneEnd" params:params pageId:nil pagePrama:nil];
    } else if ([pageId isEqualToString:PostSquare_Recommend]) {
        [SoulEventManager eventExpose:@"Square_VideoPlayOneEnd" params:params pageId:nil pagePrama:nil];
    } else if ([pageId isEqualToString:PostSquare_Follow]) {
        [SoulEventManager eventExpose:@"FollowSquare_VideoPlayOneEnd" params:params pageId:nil pagePrama:nil];
    } else {
        [SoulEventManager eventExpose:@"Square_VideoPlayOneEnd" params:params pageId:pageId pagePrama:nil];

    }

    self.layout.playerBeginPlayTime = 0;
}

- (void)doubleClickLike {
    [_toolBarNewView like];
    [_toolBarNewView beginShareAnimation];
    //动画预先显示
    if ([self.delegate respondsToSelector:@selector(cellDidClickLike:)]) {
        [self.delegate cellDidClickLike:self ];
    }
}

///热soul点击
- (void)hotSoulRankViewTapped {
    if (self.layout.post.hotSoulInfo.hotSoulTagName.length > 0 &&
        self.viewController.navigationController) {
        NSDictionary *params = @{@"pId": SOString(self.layout.post.postId),
                                @"tagid": SOString(self.layout.post.hotSoulInfo.hotSoulTagId)};
        [SoulEventManager eventClick:@"hotsoul_bubble" params:params pageId:nil pagePrama:nil];
        
        NSString *tagName = self.layout.post.hotSoulInfo.hotSoulTagName;
        id<SubSquareRouterProtocol> subSquareRouter = [ZIKRouterToService(SubSquareRouterProtocol) makeDestination];
        UIViewController *vc = [subSquareRouter routeToTopicSquareViewControllerWithTopicName:tagName];
        [self.viewController.navigationController pushViewController:vc animated:YES];
    }
}

- (void)startTimerTime {
    if (_timer) {
        [self endTimer];
    }
    _timer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(updateTime:) userInfo:nil repeats:YES];
}

- (void)endTimer {
    [_timer invalidate];
    _timer = nil;
}

- (void)updateTime:(NSTimer *)timeCount {
    if (_timer) {
        self.remainTime --;
        if (self.remainTime < 1) {
            [self endTimer];
        }
    }
}
#pragma mark  audio 音频的代理 点击播放音乐 & 共创音频

- (void)audioView:(SOSquareTimelineAudioView *)audioView cocreateClick:(SOPost *)post{
    if ([self.delegate respondsToSelector:@selector(cellDidClickCoCreate:)]) {
        [self.delegate cellDidClickCoCreate:self];
    }
}
- (void)audioView:(SOSquareTimelineAudioView *)audioView playMusic:(SOPost *)post{
    if ([self.delegate respondsToSelector:@selector(cellDidClickAudio:)]) {
        [self.delegate cellDidClickAudio:self];
    }
}


#pragma mark  -- private method
- (void)autoPlay {
    BOOL isVideo = [_layout.post.type isEqualToString:@"VIDEO"];
    
    SOAttachment *audioAttachment = _layout.post.attachments.firstObject;
    BOOL isMixAudioVD = audioAttachment.audioMojiUrl.length && [_layout.post.type isEqualToString:@"AUDIO"];
    
    if (isMixAudioVD) {
        if (_layout.post.isLocalModel) return;
        SOVideoItem *videoItem = [SOVideoItem new];
        videoItem.url = [NSURL URLWithString:audioAttachment.audioMojiUrl];
        UIImageView *playerView = self.avMixtureView.videoView;
        _player.delegate = nil;
        [_player startPlayWithVideoItem:videoItem view:playerView];
        _player.mute = YES;
        [_player seekTime:self.avMixtureView.layout.progress];
        return;
    }
    
    //一般视频和 混合视频起播实现
    SOAttachment *videoAttachment;
    if (isVideo) {
        SOVideoItem *videoItem = [SOVideoItem new];
        UIImageView *imageView;
        if ([_layout.post.type isEqualToString:@"VIDEO"] && _layout.post.attachments.count > 0 ) {
            if (self.mediaView.pics.count > 0) {
                imageView = self.mediaView.pics[0];
            }
            videoAttachment = _layout.post.attachments.firstObject;
        }
        if (!videoAttachment || !imageView) {
            return;
        }
        videoItem.url = [NSURL URLWithString:videoAttachment.fileUrl];
        //取消当前视频的预下载
        [[SoulEffectPlayerVideoCache sharedInstance] shutdownClientByUrl:videoAttachment.fileUrl];
        NSString *settes = [[MMKV defaultMMKV] getStringForKey:@"kAutoplayConfigKey"];
        
        SOAttachment *attachment = _layout.post.attachments.firstObject;
        BOOL autoSetHDR = [[ABTestMultiStrategy multiStrategyForSquare_hdrAutoSet].value isEqualToString:@"a"];
        if (autoSetHDR) {
            _player.enableHDR = attachment.hdrFlag.boolValue;
        } else {
            _player.enableHDR = true;
        }
        
        if ([settes integerValue]) {
            if ([settes integerValue] == 1 || ([settes integerValue] == 2 && [AFNetworkReachabilityManager sharedManager].networkReachabilityStatus == AFNetworkReachabilityStatusReachableViaWiFi)) {
                _player.delegate = self;
                [_player startPlayWithVideoItem:videoItem view:imageView];
                _player.mute = YES;
            }
        } else if (settes && ![settes integerValue]) {
            if ([AFNetworkReachabilityManager sharedManager].networkReachabilityStatus == AFNetworkReachabilityStatusReachableViaWiFi) {
                //WIFI情况下才自动播放
                _player.delegate = self;
                [_player startPlayWithVideoItem:videoItem view:imageView];
                _player.mute = YES;
            } else {
                if ([videoAttachment.fileDuration integerValue] <= 15) {
                    if ([AFNetworkReachabilityManager sharedManager].networkReachabilityStatus == AFNetworkReachabilityStatusReachableViaWWAN) {
                        BOOL isPresented = [[MMKV defaultMMKV] getBoolForKey:@"kAutoRePlayPromptsKey"];
                        if (!isPresented) {
                            [[MMKV defaultMMKV] setBool:YES forKey:@"kAutoRePlayPromptsKey"];
                            [[SONetworkPromptsView new] showHintWithView:nil];
                        }
                    }
                    _player.delegate = self;
                    [_player startPlayWithVideoItem:videoItem view:imageView];
                    _player.mute = YES;
                }
            }
        } else {
            //从来没有设置 settings 的时候逻辑
            BOOL isWIFI = [AFNetworkReachabilityManager sharedManager].networkReachabilityStatus == AFNetworkReachabilityStatusReachableViaWiFi;
            if (isWIFI) {
                _player.delegate = self;
                [_player startPlayWithVideoItem:videoItem view:imageView];
                _player.mute = YES;
            }
        }

        id <SOMusicPlayerManagerProtocol> musicManager = [ZIKRouterToService(SOMusicPlayerManagerProtocol) makeDestination];
        // 解决广场听派对时，自动播放视频贴如果修改AudioSession，会打断听派对的RTC声音的问题
        if (![[SOMediaManager sharedInstance] mediaIsBusyingWithFlag:5] &&
            !musicManager.isListenPartyPlaying) {
               BOOL isSoulMusicPlay = [[SOMediaManager sharedInstance] mediaIsBusyingWithFlag:2];
               if (isSoulMusicPlay) {
                   [[SOAudioSessionCategoryManager shareInstance] setCategory:AVAudioSessionCategoryPlayback error:nil];
               }else {
                   [[SOAudioSessionCategoryManager shareInstance] setCategory:AVAudioSessionCategoryAmbient error:nil];
               }
        }
        return;
    }
    
}

- (void)showPlayIcon
{
    SOAttachment *audioAttachment = _layout.post.attachments.firstObject;
    NSURL *videoUrl = nil;
    if ([_layout.post.type containsString:@"VIDEO"]) {
        videoUrl = [NSURL URLWithString:audioAttachment.fileUrl];
        if (![_player.currentVideoItem.url isEqual:videoUrl]) {
            for (UIView *view in self.mediaView.playIcons) {
                view.hidden = NO;
            }
        }
    }
}

- (void)setTapActionForPostDetail:(UIView *)view {
    view.userInteractionEnabled = YES;
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] init];
    tap.numberOfTapsRequired = 1;
    tap.delegate = self;
    [view addGestureRecognizer:tap];
    __weak typeof(self) _self = self;
    [[tap rac_gestureSignal] subscribeNext:^(id x) {
        __strong typeof(_self) self = _self;
        [self.delegate cellDidClick:self];
    }];
}

- (void)setDoubleAction:(UIView *)view {
    view.userInteractionEnabled = YES;
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] init];
    tap.delegate = self;
    tap.numberOfTapsRequired = 2;
    tap.name = [NSString stringWithFormat:@"%@_2",view.className];
    [view addGestureRecognizer:tap];
    for (UIGestureRecognizer *gesture in view.gestureRecognizers) {
        if ([gesture isKindOfClass:[UITapGestureRecognizer class]] && ((UITapGestureRecognizer *)gesture).numberOfTapsRequired == 1) {
            [gesture requireGestureRecognizerToFail:tap];
        }
    }
    __weak typeof(self) _self = self;
    [[tap rac_gestureSignal] subscribeNext:^(id x) {
        __strong typeof(_self) self = _self;
        [self.delegate cellDidDoubleClick:self];
    }];
}

- (void)willDisplay {
    
}

- (void)didEndDisplaying {
    
}

- (void)reloadData{
    [self setLayout:_layout];
}

- (void)updatePostCommentCount:(NSNotification *)notification{
    
}

- (void)updatePostCollectCount:(NSNotification *)notification{

}

- (void)videoPlayControl:(NSNotification *)notification {
    NSInteger state = [[notification.userInfo objectForKey:@"state"] intValue];
    if(state == 0) {//强制停止
        [self forceStopVideo];
    } else if (state == 1) {//自动播放/停止
        [self autoPlayAndStopVideo];
    } else if (state == 2) {//延时自动播放
        [self forceStopVideo];
        [self autoPlayAndStopVideo];
    }
}

- (void)autoPlayAndStopVideo {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if([self.layout.post.type isEqualToString:@"VIDEO"] && self.so_videoPlayer) {
            SOAttachment *videoAttachment = self.layout.post.attachments.firstObject;
            NSString *fileUrl = videoAttachment.fileUrl;
            if(self.isVideoViewVisable) {
                if(![fileUrl isEqualToString:[self.player.currentVideoItem.url absoluteString]] && !self.player.isPlaying) {
                    [self autoPlay];
                }
            } else {
                if([fileUrl isEqualToString:[self.player.currentVideoItem.url absoluteString]]) {
                    [self.player stop];
                }
            }
        }
    });
}

- (void)forceStopVideo {
    if(!self.isVideoViewVisable) {
        [self.player stop];
    }
}

- (void)didPressedToolBar:(id)sender{
    if ([self.delegate respondsToSelector:@selector(cellDidClickToolBar:)]) {
        [self.delegate cellDidClickToolBar:self];
    }
}

#pragma mark - SOSquareTimelinePicsViewDelegate
- (void)picsView:(id<SOSquareTimelinePicsViewProtocol>)picsView doubleTap:(SOPost *)post{
    if (!self.checkHitTestPostId) {
        return;
    }
    if ([self.delegate respondsToSelector:@selector(cellDidDoubleClick:)]){
        [self.delegate cellDidDoubleClick:self];
    }
}
- (void)picsView:(id<SOSquareTimelinePicsViewProtocol>)picsView tapMedia:(SOPost *)post index:(NSInteger)index{
    if (!self.checkHitTestPostId) {
        return;
    }
    if ([self.delegate respondsToSelector:@selector(cell:didClickImageAtIndex:)]){
        [self.delegate cell:self didClickImageAtIndex:index];
    }
//    self.layout.tmpNeedShowCommentGuide = true;
}
- (void)picsView:(id<SOSquareTimelinePicsViewProtocol>)picsView tapContent:(SOPost *)post{
    if (!self.checkHitTestPostId) {
        return;
    }
    if ([self.delegate respondsToSelector:@selector(cellDidClick:)]){
        [self.delegate cellDidClick:self];
    }
}
#pragma mark 新增埋点
- (void)videoPreViewPlayerExposeDuration {
    if (self.videoPreViewBeginTime <= 0) { return; }
    NSMutableDictionary *params = [NSMutableDictionary new];
    NSTimeInterval current = [NSDate date].timeIntervalSince1970;
    SOPost *post = self.layout.post;
    //取整形
    NSString *durationStr = [NSString stringWithFormat:@"%.0f",(current - self.videoPreViewBeginTime) * 1000];
    NSTimeInterval duration = durationStr.integerValue;
    params[@"duration"] = @(duration);
    params[@"pId"] = post.postId ?: @"-100";
    params[@"algExt"] = post.algExt ?: @"-100";
    params[@"tUid"] = post.authorIdEcpt ?: @"-100";
    params[@"clientTrackInfo"] = SOTrackString(post.clientTrackInfo);
    if ([post.type isEqualToString:@"VIDEO"]) {
        params[@"hdrFlag"] = SOStringWith(post.attachments.firstObject.hdrFlag, @"0");
    }
    params[@"fileUrl"] = SOString(post.attachments.firstObject.fileUrl);
    params[@"cattonCount"] = @(post.cattonCount);
    
    if (duration > 7200 * 1000) {
        //异常逻辑处理 默认最大发至 7200s
        NSString *begin = [NSString stringWithFormat:@"%.0f",self.videoPreViewBeginTime * 1000];
        NSString *end = [NSString stringWithFormat:@"%.0f",current * 1000];
        /* 广场异常时间间隔性能埋点 */
       [SoulEvent eventPerform:@"Square_Exception_Interval_track"
                        params:@{@"duration": SOTrackString(durationStr),
                                 @"eventName": @"Square_VideoPreview",
                                 @"startTimeStamps": SOTrackString(begin),
                                 @"endTimeStamps": SOTrackString(end)
                               }
                        pageId:nil pagePrama:nil];
                        
    } else {
        [SoulEvent eventExpose:@"Square_VideoPreview" params:params pageId:nil pagePrama:nil];
        //卡顿数量重置
        post.cattonCount = 0;
    }
    DDLogInfo(@"square feed Square_VideoPreview %@", @(current - self.videoPreViewBeginTime));
    self.videoPreViewBeginTime = 0;
}

#pragma mark -- 买点
- (SOPostEventExposeManage *)eventExposeManage{
    UIViewController<SOPostEventExposeManageVCProtocol> *viewController = (UIViewController<SOPostEventExposeManageVCProtocol> *)self.viewController;
    if ([viewController respondsToSelector:@selector(eventExposeManage)]){
        return [viewController eventExposeManage];
    }
    return nil;
}

#pragma mark 预防reuserd 导致的点击问题
- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event{
    UIView *view = [super hitTest:point withEvent:event];
    if (view) {
        self.hitTestPostId = self.layout.post.postId;
    } else {
        self.hitTestPostId = nil;
    }
    return view;
}

- (BOOL)checkHitTestPostId{
    if (self.hitTestPostId.length > 0 && [self.hitTestPostId isEqualToString:self.layout.post.postId]){
        return true;
    }
    return false;
}

#pragma mark --- property lazy load

- (SOSquareTimelineProfileView *)profileView {
    if (!_profileView) {
        _profileView = [SOSquareTimelineProfileView new];
        _profileView.cell = self;
        _profileView.hidden = YES;
        [_profileView setGesture];
    }
    return _profileView;
}

- (SOPostCommonTopProfileView *)topNewProfileView {
    if (!_topNewProfileView) {
        _topNewProfileView = [SOPostCommonTopProfileView new];
        _topNewProfileView.hidden = YES;
        _topNewProfileView.cell = self;
        //[_topNewProfileView setGesture];
    }
    return _topNewProfileView;
}

- (SATagSquareUserListView *)userListView {
    if (!_userListView) {
        _userListView = [SATagSquareUserListView new];
    }
    return _userListView;
}

- (SOQuestionAnswerCardContainer *)questionAnswerContainer {
    if (!_questionAnswerContainer) {
        _questionAnswerContainer = [[SOQuestionAnswerCardContainer alloc] init];
        _questionAnswerContainer.hidden = YES;
    }
    return _questionAnswerContainer;
}

- (SOSquareNewQuestionAnswerContainer *)questionAnswerNewContainer {
    if (!_questionAnswerNewContainer) {
        _questionAnswerNewContainer = [SOSquareNewQuestionAnswerContainer new];
        _questionAnswerNewContainer.hidden = YES;
    }
    return _questionAnswerNewContainer;
}

- (SOSquareMusicView *)musicView {
    if (!_musicView  && [self.reuseIdentifier hasPrefix:Cell_MUSIC_STORY]) {
        _musicView = [SOSquareMusicView new];
        _musicView.cell = self;
        [_musicView setGesture];
    }
    return _musicView;
}

- (SOSquareTimelinePicsView *)mediaView {
    if (!_mediaView) {
        if ([self.reuseIdentifier hasPrefix:Cell_VIDEO] || [self.reuseIdentifier hasPrefix:Cell_IMG] || [self.reuseIdentifier hasPrefix:Cell_IMG_VDO_MIX]){
            SOSquareTimelinePicsView *picsView = [SOSquareTimelinePicsView new];
            picsView.delegate = self;
            _mediaView = picsView;
        } else {
            _mediaView = nil;
        }
    }
    return _mediaView;
}

- (SOSquareTimelineAudioView *)audioView {
    if (!_audioView && [self.reuseIdentifier hasPrefix:Cell_AUDIO]) {
        _audioView = [[SOSquareTimelineAudioView alloc] init];
        _audioView.delegate = self;
        [self setTapActionForPostDetail:_audioView];
        [self setDoubleAction:_audioView];
        [self setDoubleAction:_audioView.noNameView];
        [self setDoubleAction:_audioView.audioCoverView];

    }
    return _audioView;
}

- (SOSquareAudioTranslationView *)audioTranslationView {
    if (!_audioTranslationView) {
        _audioTranslationView = [SOSquareAudioTranslationView new];
    }
    return _audioTranslationView;
}

- (SOSquareTimelineAnswerView *)answerView {
    if (!_answerView && [self.reuseIdentifier hasPrefix:Cell_Answer]) {
        _answerView = [SOSquareTimelineAnswerView new];
        _answerView.cell = self;
        _answerView.hidden = YES;
        [_answerView setGesture];
    }
    return _answerView;
}

- (UIImageView *)longPressGuide {
    if (!_longPressGuide) {
        _longPressGuide = [[UIImageView alloc] init];
        _longPressGuide.userInteractionEnabled = YES;
        _longPressGuide.image = [UIImage square_imageNamed:@"longPress_guide"];
        _longPressGuide.hidden = YES;
        @weakify(self);
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] init];
        [[tap rac_gestureSignal] subscribeNext:^(__kindof UIGestureRecognizer * _Nullable x) {
            @strongify(self);
            self.layout.needLongPressGudide = NO;
            self.longPressGuide.hidden = YES;
            [[MMKV defaultMMKV] setBool:YES forKey:@"long_press_guide_key"];
        }];
        [_longPressGuide addGestureRecognizer:tap];
        
    }
    return _longPressGuide;
}

- (UIImageView *)topBackGroundView {
    if (!_topBackGroundView) {
        _topBackGroundView = [[UIImageView alloc] init];
        _topBackGroundView.userInteractionEnabled = YES;
        _topBackGroundView.image = [UIImage square_imageNamed:@"push_v2_bg_top"];
    }
    return _topBackGroundView;
}

- (UIImageView *)bottomBackGroundView {
    if (!_bottomBackGroundView) {
        _bottomBackGroundView = [[UIImageView alloc] init];
        _bottomBackGroundView.userInteractionEnabled = YES;
        _bottomBackGroundView.image = [UIImage square_imageNamed:@"push_v2_bg_bottom"];
    }
    return _bottomBackGroundView;
}

- (UIView *)relationLine {
    if (!_relationLine) {
        _relationLine = [[UIView alloc] init];
        _relationLine.backgroundColor = GET_COLOR(4);
        _relationLine.layer.masksToBounds = YES;
        _relationLine.layer.cornerRadius = 1.0;
        _relationLine.hidden = YES;
    }
    return _relationLine;
}

- (SOSquareTimelineTextView *)textView {
    if (!_textView) {
        _textView = [SOSquareTimelineTextView new];
        _textView.cell = self;
        [_textView setGesture];
    }
    return _textView;
}

- (SOSquareHotCommentContainer *)hotCommentContainer {
    if (!_hotCommentContainer) {
        _hotCommentContainer = [[SOSquareHotCommentContainer alloc] init];
        _hotCommentContainer.hidden = YES;
    }
    return _hotCommentContainer;
}

- (SOSquareTimelineVoteView *)voteView {
    if (!_voteView) {
        _voteView = [SOSquareTimelineVoteView new];
        _voteView.cell = self;
        [_voteView setGesture];
    }
    return _voteView;
}

- (UIView *)topLineView {
    if (!_topLineView) {
        _topLineView = [[UIView alloc] init];
        _topLineView.backgroundColor = GET_COLOR(4);
        _topLineView.hidden = YES;
    }
    return _topLineView;
}

- (SOSquareTimelineVoteCountView *)voteCountView {
    if (!_voteCountView) {
        _voteCountView = [SOSquareTimelineVoteCountView new];
        _voteCountView.cell = self;
        [_voteCountView setGesture];
    }
    return _voteCountView;
}

/// app皮肤，瞬间卡片背景图 （只有顶部一块）
- (UIImageView *)bgTopImageView {
    if (!_bgTopImageView) {
        _bgTopImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, 124)];
        _bgTopImageView.contentMode = UIViewContentModeScaleAspectFill;
        _bgTopImageView.layer.masksToBounds = true;
        _bgTopImageView.userInteractionEnabled = true;
    }
    return _bgTopImageView;
}

- (SOSquareTopView *)topView {
    if (!_topView) {
        _topView = [[SOSquareTopView alloc] init];
    }
    return _topView;
}

- (SOPostHeatingBannerView *)heartingBannerView {
    if (!_heartingBannerView) {
        _heartingBannerView = [[SOPostHeatingBannerView alloc] init];
        @weakify(self);
        [_heartingBannerView setEventAction:^(SOPostHeatingBannerViewLayout * _Nonnull bannerLayout, NSInteger scene) {
            @strongify(self);
            if (scene == 1) {//点击banner
                SOPostHeartingBannerModel *model = bannerLayout.vasAdPostModel;
                SOUrlStrRoute(model.jumpButtonUrl, nil);
                return;
            }
            NSTimeInterval current = [[NSDate date] timeIntervalSince1970] * 1000;
            [[MMKV defaultMMKV] setInt64:current forKey:@"square_post_hearting_delete_key"];
            UITableView *tableView = (UITableView *)self.superview;
            [tableView performBatchUpdates:^{
                //处理删除逻辑
                self.layout.closeHeartingBanner = YES;
                [self.layout layout];
                [self setLayout:self.layout];
            } completion:^(BOOL finished) {
                
            }];
        }];
    }
    return _heartingBannerView;
}

- (SOPostLocationStackView *)locationView {
    if (!_locationView && self.layout.locationHeight > 0) {
        _locationView = [[SOPostLocationStackView alloc] init];
        _locationView.layoutMargins = UIEdgeInsetsMake(0, 68, 0, 0);
        __weak typeof(self) wself = self;
        _locationView.contentTapBlock = ^(SOGeoPositionInfoModel * _Nonnull model) {
            if ([wself.delegate respondsToSelector:@selector(cellDidClickLocation:)]){
                [wself.delegate cellDidClickLocation:wself];
            }
        };
        [self setTapActionForPostDetail:_locationView];
        [self setDoubleAction:_locationView];

    }
    return _locationView;
}

- (SORiskWarningContainer *)riskContainer {
    if (!_riskContainer) {
        _riskContainer = [[SORiskWarningContainer alloc] init];
        [_riskContainer setRiskEventBlock:^(SORiskWarningContainerLayout * layout) {
            [SOCommonPostHandler jumpRiskWarningHandlerWithPost:layout.post];
        }];
    }
    return _riskContainer;
}

-(SORoomCardView *)roomCardView {
    if (!_roomCardView && self.layout.post.postRoomProfileModel) {
        CGFloat width = MIN(280, KScreenScale(280));
        _roomCardView = [[SORoomCardView alloc] initWithMaxSpace:16 andPicWidth:70 andCardWidth:width];
        SOWeakIfy(self);
        _roomCardView.JoinRoom = ^{
            SOStrongIfy(self);
            if([self.delegate respondsToSelector:@selector(enterChatRoom:authorId:postId:)]) {
                NSString *authorIdEcpt = self.layout.post.authorIdEcpt;

                [self.delegate enterChatRoom:self.layout.post.postRoomProfileModel.roomId authorId:authorIdEcpt postId:self.layout.post.postId];
            }
        };
    }
    return _roomCardView;
}

- (SOSquareRecommadConfigView *)recommandRelationView {
    if (!_recommandRelationView) {
        _recommandRelationView = [[SOSquareRecommadConfigView alloc] init];
        _recommandRelationView.cell = self;
    }
    return _recommandRelationView;
}

- (SOSquareTimelineNewToolbarView *)toolBarNewView {
    if (!_toolBarNewView) {
        _toolBarNewView = [SOSquareTimelineNewToolbarView new];
        _toolBarNewView.cell = self;
    }
    return _toolBarNewView;
}

- (UIButton *)toolCoverBtn{
    if (!_toolCoverBtn) {
        _toolCoverBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _toolCoverBtn.backgroundColor = [UIColor clearColor];
        _toolCoverBtn.hidden = YES;
        [_toolCoverBtn addTarget:self action:@selector(didPressedToolBar:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _toolCoverBtn;
}

- (SOSquareAVMixtureView *)avMixtureView {
    if (!_avMixtureView && [self.reuseIdentifier hasPrefix:Cell_AudioMoji]) {
        _avMixtureView = [[SOSquareAVMixtureView alloc] init];
        _avMixtureView.cell = self;
        [_avMixtureView setGesture];
    }
    return _avMixtureView;
}

- (NSMutableArray *)barrage {
    if (!_barrage) {
        _barrage = @[].mutableCopy;
    }
    return _barrage;
}

- (AIGCRegulationView *)aiRegulationView {
    if (!_aiRegulationView) {
        _aiRegulationView = [[AIGCRegulationView alloc] init];
        _aiRegulationView.hidden = YES;
        _aiRegulationView.scene = 1;
    }
    return _aiRegulationView;
}

- (SOCommonAnchorContainer *)anchorContainer {
    if (!_anchorContainer) {
        _anchorContainer = [[SOCommonAnchorContainer alloc] init];
        _anchorContainer.delegate = self;
        _anchorContainer.hidden = YES;
        @weakify(self);
        [_anchorContainer setAnchorEventBlock:^(SOCommonAnchorContainerLayout *  _Nonnull layout, NSInteger selectedIndex) {
            @strongify(self);
            [self routerEvent:KCommonAnchorEventIdentifer eventHandler:self.viewController params:@{@"layout" : self.layout} callback:^(id  _Nonnull data) {
                
            }];
        }];
    }
    return _anchorContainer;
}

- (SOSquareGudieTopView *)gudieTopView {
    if (!_gudieTopView) {
        _gudieTopView = [[SOSquareGudieTopView alloc] init];
        _gudieTopView.hidden = YES;
    }
    return _gudieTopView;
}

- (void)setController:(UIViewController *)vc {
}


@end

@interface SOSquareTimelineCell (SOSquareCellExposeProtocol) <SOSquareCellExposeProtocol>

@end

@implementation SOSquareTimelineCell (SOSquareCellExposeProtocol)

- (void)cellValidExposeBegin:(NSIndexPath *)indexPath{
}


- (void)cellValidExposeEnd:(NSIndexPath *)indexPath{
}

#pragma mark 埋点曝光相关方法
- (NSString *)postInfo{
#ifdef DEBUG
    NSString *content = self.layout.post.content ?: @"-空-";
    if (content.length > 3) {
        return [content substringToIndex:2];
    }
    return content;
#endif
    return self.layout.post.postId ?: @"";
}
/// 刚开始显示， 1 table reload 2 viewdidappear，3 进后台
/// 开始曝光
- (void)cellWillDisplay {
    if (self.layout.profileLayout.isShowQuickChatView) {
        SOPost *post = self.layout.post;
        /* 广场快捷打招呼按钮曝光曝光埋点 */
       [SoulEvent eventExpose:@"Square_PostSayhiExpo"
                       params:@{@"tUid": SOTrackString(post.authorIdEcpt),
                                @"pId": SOTrackString(post.postId),
                                @"algExt": SOTrackString(post.algExt),
                                @"clientTrackInfo" : SOTrackString(post.clientTrackInfo)
                              }
                       pageId: nil pagePrama:nil];

    }

    //通用锚点曝光
    SOPost *post = self.layout.post;
    SOCommonAnchorModel *dataModel = post.tailAnchors.firstObject;
    BOOL hasAnchor = post.tailAnchors.count > 0 && dataModel;
    if (hasAnchor) {
        NSDictionary *params = [NSDictionary so_dictionaryWithJSON:dataModel.trackParam];
        NSString *anchor_type = SOTrackString(params[@"anchor_type"]);
        NSString *pId = SOTrackString(post.postId);
        NSString *name = SOTrackString(dataModel.title.content);
        NSString *anchor_id = SOTrackString(dataModel.bizId);
        
        NSDictionary *trackParams = @{@"pid": pId,
                                 @"name" : name,
                                 @"anchor_type" : anchor_type,
                                 @"anchor_id" : anchor_id};
        NSMutableDictionary *bizParams = [NSMutableDictionary new];
        [bizParams addEntriesFromDictionary:trackParams];
        [bizParams addEntriesFromDictionary:params];
        
        [SoulEvent eventExpose:@"Anchor_expo" params:bizParams
                        pageId:nil pagePrama:nil];
    }
    
    if (self.layout.post.promoteGiftModel) {
        [SoulEvent eventExpose:@"follow_momentpush_exp" params:@{
            @"abtest_id": self.layout.post.promoteGiftModel.expGroup ?: @"c",
            @"pid": self.layout.post.postId ?: @"",
            @"status_id" : @(self.layout.post.promoteGiftModel.status)
        } pageId:nil pagePrama:nil];
    }
    
    //虚拟人评论曝光
    if (self.layout.hotCommentLayout.height > 0) {
        SOPost *post = self.layout.post;
        SOTopicInfoModel *model = post.squareCommentNestedDto;
        NSString *postId = post.postId ?: model.ownerId;
        NSDictionary *data = @{
            @"cid": SOString(model.commentId),
            @"pid": SOString(postId),
            @"tUid" : SOString(model.authorIdEcpt)
        };
        [SoulEvent eventExpose:@"CommentExpo"
                                  params:data
                                  pageId:nil pagePrama:nil];
    }
}
/// 结束曝光
- (void)cellWillEndDisplay {

}

#pragma mark - 拍一拍相关方法

- (void)handlePatAction {
    // 立即更新UI
    SOPost *post = self.layout.post;
    if (!post.hasPatted) {
        post.patCount = @(post.patCount.integerValue + 1);
        post.hasPatted = YES;

        // 刷新工具栏显示
        [self.toolBarNewView setLayout:self.layout];

        // 通过委托处理拍一拍消息发送
        if ([self.delegate respondsToSelector:@selector(cellDidClickPat:)]) {
            [self.delegate cellDidClickPat:self];
        }
    }
}

@end

