//
//  SOSquareTimelineNewToolbarView+Square.m
//  SOPostModuleService
//
//  Created by summer on 2021/5/20.
//

#import "SOSquareTimelineNewToolbarView+Square.h"
#import "SOSquareTimelineCell.h"
#import "SOSquareTimelineCellDelegate.h"
#import <SOUGCBaseUI/SOUGCBaseUI-Swift.h>
#import <SoulBussinessKit/SOBehaviorCollectionManager.h>
#import <SoulBussinessKit/SOIntelligentModelManager.h>

@implementation SOSquareTimelineNewToolbarView (Square)

- (void)configSquareComponentView:(SOSquareTimelineLayout *)layout {
    NSMutableArray<SOInteractionModuleComponentData *> *items = [NSMutableArray new];
    BOOL chatEnable = [self chatEnableWithPost:layout.post];
    layout.post.isPrivateChatEnable = chatEnable;
    if (layout.post.aiQuestionState.integerValue == 1 && ![self isMySelf:layout.post]) {
        [items appendObject:[self toolDataForQuestionByAnswer]];
    } else {
        // 将私聊按钮替换为拍一拍按钮
        [items appendObject:[self toolDataForPat]];
    }
    [items appendObject:[self toolDataForLike]];
    [items appendObject:[self toolDataForComment]];
    [self.toolBarComponentView setItems:items];
}

- (BOOL)isMySelf:(SOPost *)post {
    BOOL isMe = false;
    if ([post.authorIdEcpt isEqualToString: SOUserInfoManager.sharedInstance.userIdEcpt] ||
        post.authorId.intValue == SOUserInfoManager.sharedInstance.userId.intValue ) {
        isMe = true;
    }
    return isMe;
}

- (BOOL)chatEnableWithPost:(SOPost *)post {
    if (![ABTestMultiStrategy multiStrategyForSquareFeedChatEntryEnable]) return NO;
    if (post.officialTag == 1) return NO;
    if ([post.authorIdEcpt isEqualToString:SOUserInfoManager.sharedInstance.userIdEcpt]) return NO;
    return YES;
}

/// 分享
- (SOInteractionModuleComponentData *)toolDataForShare{
    //分享
    SOInteractionModuleComponentData *shareData = [[SOInteractionModuleComponentData alloc] init];
    shareData.type = SOInteractionModuleItemType_Share;
    shareData.post = self.timeLineLayout.post;
    @weakify(self);
    shareData.tapBlock = ^{
        @strongify(self);
        //链路监控
        [[SOBehaviorCollectionManager shareInstance] updateInteractionEventWithType:SOBehaviorActionTypeShare needPostId:self.timeLineLayout.post.postId];
        
        [[SOIntelligentModelManager shareInstance] addUserInterfaceModelWithHandler:^(SOLittleInterfaceModel * _Nonnull actionModel) {
            @strongify(self);
            actionModel.selected = YES;
            actionModel.postId = SOString(self.timeLineLayout.post.postId);
            actionModel.sceneType = SOSquareBehaviorSceneTypeShare;
            @try {
                actionModel.postJsonStr = [self.timeLineLayout.post yy_modelToJSONString];
            } @catch (NSException *exception) {
                NSLog(@"exception_%@",exception);
            }
        }];
        
        if ([self.cell.delegate respondsToSelector:@selector(cellDidClickShare:)]) {
            [self.cell.delegate cellDidClickShare:self.cell];
        }
    };
    return shareData;
}

//AI 问答区域
- (SOInteractionModuleComponentData *)toolDataForQuestionByAnswer {
    SOInteractionModuleComponentData *questionData = [[SOInteractionModuleComponentData alloc] init];
    questionData.type = SOInteractionModuleItemType_QuestionToAnswer;
    questionData.post = self.timeLineLayout.post;
    @weakify(self);
    questionData.tapBlock = ^{
        @strongify(self);
        if ([self.cell.delegate respondsToSelector:@selector(cellDidClickQuestionPublish:)]) {
            [self.cell.delegate cellDidClickQuestionPublish:self.cell];
        }
    };
    return questionData;
}

/// 私聊
- (SOInteractionModuleComponentData *)toolDataForChat {
    SOInteractionModuleComponentData *shareData = [[SOInteractionModuleComponentData alloc] init];
    shareData.type = SOInteractionModuleItemType_Chat;
    shareData.post = self.timeLineLayout.post;
    @weakify(self);
    shareData.tapBlock = ^{
        @strongify(self);
        //链路监控
        [[SOBehaviorCollectionManager shareInstance] updateInteractionEventWithType:SOBehaviorActionTypePrivateChat needPostId:self.timeLineLayout.post.postId];
        
        [[SOIntelligentModelManager shareInstance] addUserInterfaceModelWithHandler:^(SOLittleInterfaceModel * _Nonnull actionModel) {
            @strongify(self);
            actionModel.selected = YES;
            actionModel.postId = SOString(self.timeLineLayout.post.postId);
            actionModel.sceneType = SOSquareBehaviorSceneTypePrivateChat;
            @try {
                actionModel.postJsonStr = [self.timeLineLayout.post yy_modelToJSONString];
            } @catch (NSException *exception) {
                NSLog(@"exception_%@",exception);
            }
        }];
        
        if ([self.cell.delegate respondsToSelector:@selector(cellDidClickPrivateChat:)]) {
            [self.cell.delegate cellDidClickPrivateChat:self.cell];
        }
    };
    return shareData;
}

/// 点赞
- (SOInteractionModuleComponentData *)toolDataForLike{
    SOWeakIfy(self);
    //点赞
    SOInteractionModuleComponentData *likeData = [[SOInteractionModuleComponentData alloc] init];
    likeData.type = SOInteractionModuleItemType_Like;
    likeData.post = self.timeLineLayout.post;
    likeData.tapBlock = ^{
        SOStrongIfy(self)
        //链路监控
        [[SOBehaviorCollectionManager shareInstance] updateInteractionEventWithType:SOBehaviorActionTypeLike needPostId:self.timeLineLayout.post.postId];
        
        [[SOIntelligentModelManager shareInstance] addUserInterfaceModelWithHandler:^(SOLittleInterfaceModel * _Nonnull actionModel) {
            @strongify(self);
            actionModel.selected = !self.timeLineLayout.post.liked.boolValue;
            actionModel.sceneType = SOSquareBehaviorSceneTypeLike;
            actionModel.postId = SOString(self.timeLineLayout.post.postId);
            @try {
                actionModel.postJsonStr = [self.timeLineLayout.post yy_modelToJSONString];
            } @catch (NSException *exception) {
                NSLog(@"exception_%@",exception);
            }
        }];
        
        [self.toolBarComponentView beginShareAnimation];
        if ([self.cell.delegate respondsToSelector:@selector(cellDidClickLike:)]) {
            [self.cell.delegate cellDidClickLike:self.cell];
        }
    };
    likeData.longPressBlock = ^{
        SOStrongIfy(self)
        if (self.cell.delegate && [self.cell.delegate respondsToSelector:@selector(cellLongPressSuperLike:)]) {
            [self.cell.delegate cellLongPressSuperLike:self.cell];
        }
    };
    return likeData;
}
/// 收藏
- (SOInteractionModuleComponentData *)toolDataForCollect{
    SOWeakIfy(self);
    SOInteractionModuleComponentData *collect = [[SOInteractionModuleComponentData alloc] init];
    collect.type = SOInteractionModuleItemType_Collect;
    collect.post = self.timeLineLayout.post;
    collect.tapBlock = ^{
        SOStrongIfy(self);
        //链路监控
        [[SOBehaviorCollectionManager shareInstance] updateInteractionEventWithType:SOBehaviorActionTypeCollection needPostId:self.timeLineLayout.post.postId];
        
        NSString *postId = self.timeLineLayout.post.postId;
        if (self.timeLineLayout.post.isCollected) { /// 动画时已经更新状态
            [[SOPostInfoRequest shareInstance] collectPostById:postId onSuccess:^{
                [MBProgressHUD showError:@"收藏成功" toView:self.viewController.view];
            } onFailure:^(NSInteger status, NSString * _Nullable msg) {} onFinish:^{}];
        } else {
            [[SOPostInfoRequest shareInstance] unCollectPostById:postId onSuccess:^{
            } onFailure:^(NSInteger status, NSString * _Nullable msg) {} onFinish:^{}];

        }
    };
    return collect;
}

/// 评论
- (SOInteractionModuleComponentData *)toolDataForComment{
    //评论
    SOWeakIfy(self)
    SOInteractionModuleComponentCommentData *commitData = [[SOInteractionModuleComponentCommentData alloc] init];
        commitData.placeHolder = @"评论";
    commitData.type = SOInteractionModuleItemType_Commit;
    commitData.post = self.timeLineLayout.post;
    commitData.tapBlock = ^{
        SOStrongIfy(self)
        //链路监控
        [[SOBehaviorCollectionManager shareInstance] updateInteractionEventWithType:SOBehaviorActionTypeComment needPostId:self.timeLineLayout.post.postId];
        
        [[SOIntelligentModelManager shareInstance] addUserInterfaceModelWithHandler:^(SOLittleInterfaceModel * _Nonnull actionModel) {
            SOStrongIfy(self)
            @try {
                actionModel.postJsonStr = [self.timeLineLayout.post yy_modelToJSONString];
                actionModel.selected = YES;
                actionModel.postId = SOString(self.timeLineLayout.post.postId);
                actionModel.sceneType = SOSquareBehaviorSceneTypeComment;
            } @catch (NSException *exception) {
                NSLog(@"exception_%@",exception);
            }
        }];
        
        if ([self.cell.delegate respondsToSelector:@selector(cellDidClickComment:)]) {
            [self.cell.delegate cellDidClickComment:self.cell];
        }
    };
    return commitData;
}

/// 拍一拍
- (SOInteractionModuleComponentData *)toolDataForPat {
    SOInteractionModuleComponentData *patData = [[SOInteractionModuleComponentData alloc] init];
    patData.type = SOInteractionModuleItemType_Pat;
    patData.post = self.timeLineLayout.post;
    @weakify(self);
    patData.tapBlock = ^{
        @strongify(self);
        //链路监控
        [[SOBehaviorCollectionManager shareInstance] updateInteractionEventWithType:SOBehaviorActionTypePrivateChat needPostId:self.timeLineLayout.post.postId];

        [[SOIntelligentModelManager shareInstance] addUserInterfaceModelWithHandler:^(SOLittleInterfaceModel * _Nonnull actionModel) {
            @strongify(self);
            actionModel.selected = YES;
            actionModel.postId = SOString(self.timeLineLayout.post.postId);
            actionModel.sceneType = SOSquareBehaviorSceneTypePrivateChat;
            @try {
                actionModel.postJsonStr = [self.timeLineLayout.post yy_modelToJSONString];
            } @catch (NSException *exception) {
                NSLog(@"exception_%@",exception);
            }
        }];

        if ([self.cell.delegate respondsToSelector:@selector(cellDidClickPat:)]) {
            [self.cell.delegate cellDidClickPat:self.cell];
        }
    };
    return patData;
}


@end
