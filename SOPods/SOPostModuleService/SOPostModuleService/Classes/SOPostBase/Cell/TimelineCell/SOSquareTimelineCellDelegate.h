//
//  SOSquareTimelineCellDelegate.h
//  Soul_New
//
//  Created by willson.yi on 2020/9/30.
//  Copyright © 2020 Soul. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "SOPostHeader.h"
#import <SOUGCBaseUI/SOSquareLinkMonitor.h>

@class SOSquareTimelineCell;
@class SOSquareTimelineLayout;
NS_ASSUME_NONNULL_BEGIN



@protocol SOSquareTimelineCellDelegate <NSObject>
@optional
/// 点击了 Cell
- (void)cellDidClick:(SOSquareTimelineCell *)cell;
/// 双击了 Cell
- (void)cellDidDoubleClick:(SOSquareTimelineCell *)cell;
/// 点击了昵称
- (void)cellDidClickName:(SOSquareTimelineCell *)cell;
/// 点击了头像
- (void)cellDidClickAvatar:(SOSquareTimelineCell *)cell;
- (void)cellDidClickAvatar:(SOSquareTimelineCell *)cell checkHitTestPostId:(BOOL)checkHitTestPostId;
/// 点击了soulmate头像
- (void)cellDidClickSoulmateAvatar:(SOSquareTimelineCell *)cell;
/// 点击了图片
- (void)cell:(SOSquareTimelineCell *)cell didClickImageAtIndex:(NSInteger)index;
/// 点击了答案君
- (void)cellDidClickAnswerTag:(SOSquareTimelineCell *)cell;
/// 点击了视频
- (void)cell:(SOSquareTimelineCell *)cell didClickVideoAtIndex:(NSInteger)index;
/// 点击了音频
- (void)cellDidClickAudio:(SOSquareTimelineCell *)cell;
- (void)schoolBarCellDidClickAudio:(SOSquareTimelineCell * _Nullable)cell;
/// 点击展开按钮
- (void)cellDidClickFoldBtnLayout:(SOSquareTimelineCell *_Nullable)cell;
/// 点击了 Label 的链接
- (void)cell:(SOSquareTimelineCell *)cell didClickInLabel:(YYLabel *)label textRange:(NSRange)textRange;
/// 点击了 推荐souler
- (void)cell:(SOSquareTimelineCell *)cell didClickSouler:(SOPost *)post;

/// 点击了同款滤镜或同款帖子
///type: 0, filter, 1,stiker
- (void)cell:(SOSquareTimelineCell *)cell didClickFilterType:(NSInteger)type itemID:(NSString *)itemID;
/// 点击了 Tag
- (void)cell:(SOSquareTimelineCell *)cell didClickTag:(NSString *)tag index:(NSInteger)index;

/// 点击了 共创按钮
- (void)cellDidClickCoCreate:(SOSquareTimelineCell *)cell;

- (void)cellDidClickFollowUser:(SOSquareTimelineCell *)cell;
/// 点击了 拍一拍
- (void)cellDidClickPat:(SOSquareTimelineCell *)cell;
- (void)cellDidClickQuestionPublish:(SOSquareTimelineCell *)cell;
- (void)cellDidClickPrivateChat:(SOSquareTimelineCell *)cell checkHitTestPostId:(BOOL)checkHitTestPostId;
/// 点击了 更多按钮
- (void)cellDidClickMore:(SOSquareTimelineCell *)cell;
// 点击了工具栏cover
- (void)cellDidClickToolBar:(SOSquareTimelineCell *)cell;
/// 点击了 收藏
- (void)cellDidClickCollect:(SOSquareTimelineCell *)cell completionBlock:(dispatch_block_t)completionBlock;
/// 点击了 点赞
- (void)cellDidClickLike:(SOSquareTimelineCell *)cell;

- (void)cellLongPressSuperLike:(SOSquareTimelineCell *)cell;
/// 点击了 快速评论
- (void)cellDidClickFastComment:(SOSquareTimelineCell *)cell completionBlock:(dispatch_block_t)completionBlock;
/// 点击了 评论
- (void)cellDidClickComment:(SOSquareTimelineCell *)cell;
/// 点击了 分享
- (void)cellDidClickShare:(SOSquareTimelineCell *)cell;
/// 点击了 地理位置
- (void)cellDidClickLocation:(SOSquareTimelineCell *)cell;
/// 点击了 置顶
- (void)cellDidClickAdminTop:(SOSquareTimelineCell *)cell;
/// 点击了 重发
- (void)cellDidClickResend:(SOSquareTimelineCell *)cell;
/// 点击了 投票
-(BOOL)cellBlockClickSchoolBarVote:(SOSquareTimelineCell *)cell content:(NSString *)content;
-(void)cellDidClickVote:(SOSquareTimelineCell *)cell content:(NSString *)content;
// qq音乐被点击
- (void)cellDidClickMusic:(SOSquareTimelineCell*)cell;
- (void)cellDidClickSSR:(SOSquareTimelineCell*)cell;
// qq音乐封面被点击
- (void)cellDidClickThumbImageView:(SOSquareTimelineCell*)cell didPlayBtnClick:(UIImageView*)playImageView;
// 送礼按钮被点击
- (void)cellDidClickGift:(SOSquareTimelineCell*)cell;
// 送礼按钮被点击
- (void)cellDidClickGiftRecord:(SOSquareTimelineCell*)cell;

// 加热标签被点击
- (void)cellDidClickWarmTag:(SOSquareTimelineCell*)cell;
/// 进入房间
/// @param roomId 房间ID
-(void)enterChatRoom:(NSString *)roomId authorId:(NSString *)authorId postId:(NSString *)postId;
/// 点击打招呼按钮 快捷聊天
- (void)profileViewDidClickQuickChatBtn:(SOPost *)post view:(UIView *)view;

- (void)cellLongPressFeedBackWithGesture:(UILongPressGestureRecognizer *)gesture withCell:(SOSquareTimelineCell *)cell;

//获取对应工具类
- (SOSquareLinkMonitor *)getLinkMonitor;

@end

NS_ASSUME_NONNULL_END
