//
//  SOACCPostRequest.h
//  ACC
//
//  Created by 刘强 on 2020/12/14.
//

#import <Foundation/Foundation.h>
#import "SOACCHeader.h"


NS_ASSUME_NONNULL_BEGIN

@interface SOACCPostRequest : NSObject

+ (instancetype)shareInstance;

//点赞帖子
- (void)likePostById:(NSString *)postId
           onSuccess:(NoParamSuccessHander)succeedHandler
           onFailure:(FailureHandler)failureHandler
            onFinish:(FinishHandler)finishHandler;

//取消点赞帖子
- (void)disLikePostById:(NSString *)postId
              onSuccess:(NoParamSuccessHander)succeedHandler
              onFailure:(FailureHandler)failureHandler
               onFinish:(FinishHandler)finishHandler;

//删除帖子
- (void)deletePostById:(NSString *)postId
             onSuccess:(NoParamSuccessHander)succeedHandler
             onFailure:(FailureHandler)failureHandler
              onFinish:(FinishHandler)finishHandler;

- (void)modifyMood:(NSString *)mood
               pid:(NSString *)postId
         onSuccess:(NoParamSuccessHander)succeedHandler
         onFailure:(FailureHandler)failureHandler
          onFinish:(FinishHandler)finishHandler;

@end

NS_ASSUME_NONNULL_END
