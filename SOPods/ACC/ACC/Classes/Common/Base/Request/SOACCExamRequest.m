//
//  SOACCExamRequest.m
//  ACC
//
//  Created by 刘强 on 2020/12/15.
//

#import "SOACCExamRequest.h"

@implementation SOACCExamRequest

+ (instancetype)sharedInstance {
    static SOACCExamRequest *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[SOACCExamRequest alloc] init];
    });
    return instance;
}

- (void)getTestResultByUserId:(NSString *)userId
                    onSuccess:(SoulAPIResponseCommonBlock)succeedHandler
                    onFailure:(FailureHandler)failureHandler
                     onFinish:(FinishHandler)finishHandler {
    NSDictionary *param = @{@"userIdEcpt": [SoulUserIDUtils getEcptId:userId]};

    [[SoulAPIManager sharedInstance] requestGet:@"/MeasureResult/New" baseUrl:SOPostBaseDomain parameters:param success:^(SoulResponseCommonModel *_Nonnull responseModel) {
        if (succeedHandler) {
            succeedHandler(responseModel);
        }
    }                                   failure:failureHandler finish:finishHandler];
}


@end
