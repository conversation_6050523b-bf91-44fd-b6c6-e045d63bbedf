//
//  SOACCPostRequest.m
//  ACC
//
//  Created by 刘强 on 2020/12/14.
//

#import <SoulBussinessKit/SoulBussinessKit.h>
#import <SoulBussinessKit/SoulBussinessKit-Swift.h>
#import "SOACCPostRequest.h"

@implementation SOACCPostRequest

+ (instancetype)shareInstance {
    static SOACCPostRequest *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[SOACCPostRequest alloc] init];
    });
    return instance;
}

- (void)likePostById:(NSString *)postId
           onSuccess:(NoParamSuccessHander)succeedHandler
           onFailure:(FailureHandler)failureHandler
            onFinish:(FinishHandler)finishHandler {
    [self likePostById:postId needNotification:YES onSuccess:succeedHandler onFailure:failureHandler onFinish:finishHandler];
}

- (void)likePostById:(NSString *)postId
    needNotification:(BOOL)needNotification
           onSuccess:(NoParamSuccessHander)succeedHandler
           onFailure:(FailureHandler)failureHandler
            onFinish:(FinishHandler)finishHandler {
    if (!postId.length) return;
    NSDictionary *param = @{@"postId": postId};

    [SOPostInfoRequest.shareInstance likePostById:postId withParams:param needNotification:needNotification onSuccess:succeedHandler onFailure:failureHandler onFinish:finishHandler];
    
}

- (void)disLikePostById:(NSString *)postId
              onSuccess:(NoParamSuccessHander)succeedHandler
              onFailure:(FailureHandler)failureHandler
               onFinish:(FinishHandler)finishHandler {
    [self disLikePostById:postId needNotification:YES onSuccess:succeedHandler onFailure:failureHandler onFinish:finishHandler];
}

- (void)disLikePostById:(NSString *)postId
       needNotification:(BOOL)needNotification
              onSuccess:(NoParamSuccessHander)succeedHandler
              onFailure:(FailureHandler)failureHandler
               onFinish:(FinishHandler)finishHandler {
    if (!postId.length) {
        return;
    }
    NSDictionary *param = @{@"postId": postId};
    [SOPostInfoRequest.shareInstance disLikePostById:postId withParams:param needNotification:needNotification onSuccess:succeedHandler onFailure:failureHandler onFinish:finishHandler];
}

- (void)deletePostById:(NSString *)postId
             onSuccess:(NoParamSuccessHander)succeedHandler
             onFailure:(FailureHandler)failureHandler
              onFinish:(FinishHandler)finishHandler {
    if (!postId.length) {
        return;
    }
    NSDictionary *param = @{@"postId": postId};

    [[SoulAPIManager sharedInstance] requestPost:@"/v3/post/delete" baseUrl:SONewPostBaseDomain parameters:param success:^(SoulResponseCommonModel *_Nonnull responseModel) {
        if (succeedHandler) {
            succeedHandler();
        }
        [[NSNotificationCenter defaultCenter] postNotificationName:@"USER_HAS_DELETE_POST" object:nil userInfo:@{@"postid": postId}];
    }                                    failure:failureHandler finish:finishHandler];


}

- (void)modifyMood:(NSString *)mood
               pid:(NSString *)postId
         onSuccess:(NoParamSuccessHander)succeedHandler
         onFailure:(FailureHandler)failureHandler
          onFinish:(FinishHandler)finishHandler {
    NSMutableDictionary *map = [[NSMutableDictionary alloc] init];
    if (mood.length) {
        [map setValue:mood forKey:@"weather"];
    }
    if (postId) {
        [map setValue:postId forKey:@"postId"];
    }
    [[SoulAPIManager sharedInstance] requestPost:@"/v3/post/weather" baseUrl:SONewPostBaseDomain parameters:map success:^(SoulResponseCommonModel *_Nonnull responseModel) {
        if (succeedHandler) {
            succeedHandler();
        }
    }                                    failure:failureHandler finish:finishHandler];

}

@end
