//
//  SOACCExamRequest.h
//  ACC
//
//  Created by 刘强 on 2020/12/15.
//

#import <Foundation/Foundation.h>
#import "SOACCHeader.h"

NS_ASSUME_NONNULL_BEGIN

@interface SOACCExamRequest : NSObject

+ (instancetype)sharedInstance;

- (void)getTestResultByUserId:(NSString *)userId
                    onSuccess:(SoulAPIResponseCommonBlock)succeedHandler
                    onFailure:(FailureHandler)failureHandler
                     onFinish:(FinishHandler)finishHandler;

@end

NS_ASSUME_NONNULL_END
