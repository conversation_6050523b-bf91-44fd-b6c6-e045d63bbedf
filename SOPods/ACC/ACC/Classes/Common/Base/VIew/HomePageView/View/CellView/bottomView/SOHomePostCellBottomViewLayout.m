//
//  SOHomePostCellBottomViewLayout.m
//  ACC
//
//  Created by 11080237 on 2022/8/18.
//

#import "SOHomePostCellBottomViewLayout.h"
#import "SOHomePostCellBottomView.h"
@implementation SOHomePostCellBottomViewLayout

+ (void)load{
    static dispatch_once_t once;
    dispatch_once(&once, ^{
        [SOCommonViewLayoutManage.shareInstance addCreaterBlock:^id<SOCommonViewLayoutProtocol> _Nullable(id  _Nonnull data, NSDictionary * _Nullable extData) {
            SOHomePostCellBottomViewLayout *layout = [SOHomePostCellBottomViewLayout new];
            if ([data isKindOfClass:SOPost.class]){
                layout.post = (SOPost *)data;
            }
            NSString * isSoulMate = extData[@"isSoulMate"];
            if ([isSoulMate isKindOfClass:NSString.class] && [isSoulMate isEqualToString:@"1"]) {
                layout.isSoulmate = true;
            }
            return layout;
        } forType:SOCommonViewLayoutKey_HomeCellBottomView];
    });
}

- (id)layout{
    self.height = 0;
    self.topMargin = [self isOtherHomePagePost] ? 10 : 0;
    CGFloat giftInfoWidth = 150;
    CGFloat locationWidth = [self isOtherHomePagePost] ? ((KScreenWidth - 16 - 16 - 16 - giftInfoWidth - 32)) : ((KScreenWidth - giftInfoWidth - 16 - 16));
    /// 个人主页和他人主页的 地理位置 操作按钮显示
    self.locationLayout = [SOCommonViewLayoutManage.shareInstance createLayoutWithData:self.post extData:nil type:SOCommonViewLayoutKey_LocationView];
    if (self.locationLayout.isShow) {
        self.locationLayout.itemFrame = CGRectMake(16, 0 + self.topMargin, locationWidth, 20);
    } else {
        self.locationLayout.itemFrame = CGRectZero;
    }
    
    if (self.post.giftInfo.giftUserList) {
        self.giftViewFrame = CGRectMake(locationWidth + 16, - 2 + self.topMargin, giftInfoWidth, 24);
    } else {
        self.giftViewFrame = CGRectZero;
    }
    
    if ([self isOtherHomePagePost]) {
        self.toolViewFrame = CGRectMake(0, 0 - (36 - 24) / 2 + self.topMargin, KScreenWidth, 36);
        if (self.post.giftInfo.giftUserList.count <= 0) {
            self.giftViewFrame = CGRectZero;
        }
        self.height = 34;
    } else if (self.post.geoPositionInfo.showPosition || self.post.giftInfo.giftUserList.count > 0) {
        self.height = 20;
    } else if ([SoulUserManager isSelfEcpt:self.post.authorIdEcpt]) {
        self.height = 20;
    }
    self.itemFrame = CGRectMake(0, 0, KScreenWidth, self.height);
    return self;
}
#pragma mark SOCommonViewLayoutProtocol
- (NSString *)commonViewIdentifier{
    return NSStringFromClass(SOHomePostCellBottomView.class);
}
- (UIView<SOCommonViewProtocol> *)commonViewCreateView{
    return [[SOHomePostCellBottomView alloc] initWithFrame:CGRectZero];
}

- (BOOL)isShow {
    if ([self isOtherHomePagePost]  ){
        return YES;
    }
    if (self.post.geoPositionInfo.showPosition || self.post.giftInfo.giftUserList.count > 0){
        return YES;
    }
    if ([SoulUserManager isSelfEcpt:self.post.authorIdEcpt]) {
        return YES;
    }
    return NO;
}

- (BOOL)isOtherHomePagePost {
    BOOL isOtherHomePagePost = NO;
    NSString *userId = [SOUserInfoManager sharedInstance].userId;
    if (userId.integerValue != self.post.authorId.integerValue || self.isSoulmate) {
        isOtherHomePagePost = YES;
    }
    return isOtherHomePagePost;
}

- (NSString *)visibilityTitle {
    if (self.post.officialTag == 1) {
        return @"隐身发布";
    } else {
        return [SoulOwnPostAuthorityAide getDescribeWithAuthority:self.post.visibility];
    }
}

- (NSString *)visibilityIconName {
    if ([self.post.visibility isEqualToString:@"PRIVATE"]) {
        return @"acc_post_visibility_pirvate";
    } else {
        return @"acc_post_visibility_public";
    }
}

@end
