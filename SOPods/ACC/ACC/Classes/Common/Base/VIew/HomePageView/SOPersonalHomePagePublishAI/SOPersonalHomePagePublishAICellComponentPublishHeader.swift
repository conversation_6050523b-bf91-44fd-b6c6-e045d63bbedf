//
//  SOPersonalHomePagePublishAICellComponentPublishHeader.swift
//  ACC
//
//  Created by al<PERSON> on 2025/7/17.
//

import UIKit
import SnapKit
import SoulUIKitExtended
import SoulRouter
import SoulMobEvent
import SoSwiftLottieBridge
import SoulAssets

/// Header component for AI publish suggestions
/// Shows score information with fold/unfold and close functionality
class SOPersonalHomePagePublishAICellComponentPublishHeader: UIView {
    
    /// Callback to reload the cell content
    @objc
    public var reloadActionHandler: (() -> Void)?
    
    /// Callback to close the AI publish component
    @objc
    public var closeActionHandler: (() -> Void)?
    
    /// Data model containing header information
    private var data: SOPersonalHomePagePublishAIData?
    
    // MARK: - Life Circle
    
    init() {
        super.init(frame: .zero)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    /// Handles close button tap to disable AI publish component
    @objc
    private func closeAction() {
        SOPersonalHomePagePublishAIDataStore.shared.disable()
        closeActionHandler?()
    }
    
    /// Handles fold/unfold button tap to toggle content visibility
    /// Records analytics event and updates fold state
    @objc
    private func foldAction() {
        guard let data = data else { return }
        
        let params = ["post_unfold_status": data.isFold ? "2" : "1"]
        SoulEvent.eventClick("AI_Home_Assistant_Fold", params: params, pageId: nil, pagePrama: nil)
        
        data.isFold = !data.isFold
        reloadActionHandler?()
    }
    
    /// Handles container tap to navigate to AI assistant page
    /// Records analytics event and opens router URL
    @objc
    private func routerAction() {
        guard let routerURL = data?.scoreData?.routerURL, let url = URL(string: routerURL) else { return }
        SoulEvent.eventClick("AI_Home_Assistant_Clk", params: nil, pageId: nil, pagePrama: nil)
        try? SoulRouterManager.sharedInstance().handleOpen(url, fromVc: nil, appParas: nil)
    }
    
    // MARK: - Data
    
    /// Updates the component with new AI publish data
    /// - Parameter data: The AI publish data containing header information
    func update(data: SOPersonalHomePagePublishAIData) {
        self.data = data
        updateUI(data: data)
        updateUIConstraints(data: data)
    }
    
    // MARK: - UI
    
    /// Sets up the initial UI components and gesture recognizers
    private func setupUI() {
        addSubview(container)
        container.addSubview(scoreView)
        container.addSubview(foldButton)
        container.addSubview(publishFinishLabel)
        container.addSubview(closeButton)
        
        closeButton.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        foldButton.addTarget(self, action: #selector(foldAction), for: .touchUpInside)
        container.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(routerAction)))
    }
    
    /// Sets up the UI constraints for all components
    private func setupUIConstraints() {
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(48)
        }
        
        scoreView.snp.makeConstraints { make in
            make.leading.trailing.top.equalToSuperview()
        }
        
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(4)
            make.trailing.equalTo(-8)
            make.width.height.equalTo(24)
        }
        foldButton.snp.makeConstraints { make in
            make.trailing.equalTo(closeButton.snp.leading).offset(-8)
            make.centerY.equalTo(closeButton)
            make.width.equalTo(50)
            make.height.equalTo(18)
        }
        publishFinishLabel.snp.makeConstraints { make in
            make.trailing.equalTo(closeButton.snp.leading).offset(-4)
            make.centerY.equalTo(closeButton)
        }
    }
    
    /// Updates the UI with the provided AI publish data
    /// Controls visibility of components based on data state
    /// - Parameter data: The AI publish data
    private func updateUI(data: SOPersonalHomePagePublishAIData) {
        scoreView.update(data: data)
        if data.publishDataEnable {
            foldButton.isHidden = false
            publishFinishLabel.isHidden = true
        } else {
            foldButton.isHidden = true
            publishFinishLabel.isHidden = !SOPersonalHomePagePublishAIDataStore.shared.publishFinishState
        }
        if data.isFold {
            foldButton.setTitle("展开", for: .normal)
            foldButton.setImage(.acc_imageNamed("acc_publish_ai_score_unfold"), for: .normal)
        } else {
            foldButton.setTitle("收起", for: .normal)
            foldButton.setImage(.acc_imageNamed("acc_publish_ai_score_fold"), for: .normal)
        }
    }
    
    /// Updates UI constraints based on the provided data
    /// - Parameter data: The AI publish data
    private func updateUIConstraints(data: SOPersonalHomePagePublishAIData) {
        
    }
    
    // MARK: - UI Components
    
    /// Main container view for all content
    private let container = UIView()
    
    /// Score view component for displaying AI score information
    private let scoreView = SOPersonalHomePagePublishAICellComponentScore()
    
    /// Button to fold/unfold the publish content
    private let foldButton = UIButton().then {
        $0.isHidden = true
    }
    
    /// Label displayed when publish is finished
    private let publishFinishLabel = UILabel().then {
        $0.isHidden = true
        $0.text = "今日吸引力已提升"
        $0.textColor = .soul.color(6)
        $0.font = .soul.PingFangSC(size: 10)
    }
    
    /// Close button to dismiss the publish component
    private let closeButton = UIButton().then {
        $0.setImage(.acc_imageNamed("acc_publish_ai_cell_close_icon"), for: .normal)
    }
}
