//
//  SOCommonBusinessContainerView.h
//  ACC
//
//  Created by 11080237 on 2022/8/18.
//

#import <Foundation/Foundation.h>
#import <SoulBussinessKit/SoulBussinessKit.h>
#import "SOCommonBusinessContainerViewLayout.h"
NS_ASSUME_NONNULL_BEGIN

@protocol SOCommonBusinessContainerView <NSObject>

@end
@interface SOCommonBusinessContainerView : UIView <SOCommonViewProtocol>

@property(nonatomic, strong) SOCommonBusinessContainerViewLayout *layout;
@property(nonatomic, strong) SOPost *post;
@property(nonatomic, weak) id delegate;

@property (nonatomic, strong) UIView<SOCommonBusinessViewProtocol> * commonBusinessView;

@end

NS_ASSUME_NONNULL_END
