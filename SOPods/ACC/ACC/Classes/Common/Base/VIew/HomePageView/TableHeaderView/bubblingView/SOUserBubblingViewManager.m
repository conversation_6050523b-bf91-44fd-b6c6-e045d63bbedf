//
//  SOUserBubblingViewManager.m
//  ACC
//
//  Created by xupeng on 2021/11/18.
//

#import "SOUserBubblingViewManager.h"
#import <SoulCoreBase/SoulCoreBase.h>

#import "SOUserBubblingView.h"
#import "SAUserBubblingView.h"

@implementation SOUserBubblingViewManager

+ (void)getUserBubblingInfo:(NSString *)userId superView:(UIView *)superView isNewVersion:(BOOL)isNewVersion showPoint:(CGPoint)showPoint showUserBubbling:(nonnull void (^)(void))showUserBubbling{

    NSString *targetUserIdEcpt = [SoulUserIDUtils getEcptId:userId];
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    param[@"targetUserIdEcpt"] = targetUserIdEcpt;

    [[SoulAPIManager sharedInstance] requestGET:@"bubbling/queryByUserId" baseUrl:SOUserBaseDomain parameters:param success:^(SoulResponseCommonModel *_Nonnull responseModel) {
        if ([responseModel.data isKindOfClass:[NSDictionary class]]) {
            SOUserBubblingModel *model = [SOUserBubblingModel yy_modelWithJSON:responseModel.data];
            if (model.hasExp) {
                if (showUserBubbling){
                    showUserBubbling();
                }
                [SOUserBubblingViewManager showUserBubblingViewModel:model superView:superView showPoint:showPoint isNewVersion:isNewVersion];
            }
        }
    }                                   failure:^(NSInteger status, NSString *_Nonnull msg) {
    }];
}

+ (void)showUserBubblingViewModel:(SOUserBubblingModel *)bubblingModel superView:(UIView *)superView showPoint:(CGPoint)showPoint isNewVersion:(BOOL)isNewVersion{
    if (bubblingModel && superView) {
        /* 展示泡泡状态曝光曝光埋点 */
        [SoulEvent eventExpose:@"HomePage_ShowPaoPao_Exp" params:nil pageId:nil pagePrama:nil];
        if (isNewVersion){
            //新版个人主页
            SAUserBubblingView *bubblingView = [[SAUserBubblingView alloc] initWithFrame:CGRectMake(showPoint.x, showPoint.y, 193, 80) bubblingModel:bubblingModel];
            [superView addSubview:bubblingView];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [bubblingView playAnimation];
            });
            
        }else{
            SOUserBubblingView *bubblingView = [[SOUserBubblingView alloc] initWithFrame:CGRectMake(showPoint.x, showPoint.y, 140, 54) bubblingModel:bubblingModel];
            [superView addSubview:bubblingView];
        }
        
    }
}

@end
