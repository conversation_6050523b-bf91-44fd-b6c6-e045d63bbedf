//
//  SOPersonalHomePagePublishAIData.swift
//  ACC
//
//  Created by albert on 2025/6/3.
//

import Foundation
import SoulCore

/// Model representing the publish guide data structure
@objcMembers
public class SOPersonalHomePagePublishAIData: NSObject, Codable {
    
    /// List of different style configurations for the guide
    public var scoreData: SOPersonalHomePagePublishAIScoreData?
    /// List of recommended cards to show
    public var publishData: SOPersonalHomePagePublishAIPublishData?
    public var publishDataList: [SOPersonalHomePagePublishAIPublishData]?
    
    public var visitData: SOPersonalHomePagePublishAIVisitData?
    /// Flag indicating if the guide is folded/collapsed
    public var isFold = false
    
    enum CodingKeys: String, CodingKey {
        case scoreData = "scoreShowDTO"
        case publishDataList = "postAddDTOList"
        case visitData = "userShowDTO"
    }
}

extension SOPersonalHomePagePublishAIData {
    
    public var publishDataEnable: Bool {
        guard let publishDataList = publishDataList, !publishDataList.isEmpty else { return false }
        return true
    }
}

@objcMembers
public class SOPersonalHomePagePublishAIScoreData: NSObject, Codable {
    
    public var title: String?
    public var score: Int?
    public var scoreTotal: Int?
    public var routerURL: String?
    
    enum CodingKeys: String, CodingKey {
        case title = "scoreMessage"
        case score
        case scoreTotal = "totalScore"
        case routerURL = "jumpUrl"
    }
}

@objcMembers
public class SOPersonalHomePagePublishAIPublishData: NSObject, Codable {
    
    public var title: String?
    public var subTitle: String?
    public var imageURL: String?
    public var routerURL: String?
    public var actionTitle: String?
    public var enable: Bool?
    
    enum CodingKeys: String, CodingKey {
        case title = "postAddScoreStr"
        case subTitle = "imagePostContent"
        case imageURL = "imageUrl"
        case routerURL = "jumpUrl"
        case actionTitle = "jumpButtonName"
        case enable = "showImageInfo"
    }
}

@objcMembers
public class SOPersonalHomePagePublishAIVisitData: NSObject, Codable {
    
    public var title: String?
    public var buttonName: String?
    public var cardList: [SOPost]?
    
    enum CodingKeys: String, CodingKey {
        case title = "todaySeeMeCountDesc"
        case buttonName
    }
}

@objcMembers
public class SOPersonalHomePagePublishAIVisitCardData: NSObject, Codable {
    
}
