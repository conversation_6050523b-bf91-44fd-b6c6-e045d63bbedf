//
//  SOSoulersMeetView.m
//  SoulBussinessKit
//
//  Created by xupeng on 2021/10/22.
//

#import "SOSoulersMeetView.h"
#import <SOWebImage/SOWebImage.h>
#import <SoulUIKit/SoulUIKit.h>

#define Animation_width 80
#define MeetIcon_width 22
#define MeetIcon_overWidth 6
#define MeetIcon_fixWidth ((MeetIcon_width - MeetIcon_overWidth) / 2.0)
#define MeetIcon_InitLeft ((Animation_width - MeetIcon_width) / 2.0 + MeetIcon_fixWidth)


@interface SOSoulersMeetView ()

@property(nonatomic, strong) NSMutableArray <UIView *> *iconViewArray;

@end

@implementation SOSoulersMeetView

- (void)setupUIWithMeetModel:(SAHomePageMetricsModel *)meetModel {
    [self removeAnimation];

    self.meetModel = meetModel;
    if (self.meetModel.recentViewNum > 0) {
        [self createSoulersIconView];
    }
}

- (void)removeAnimation {
    for (UIView *tempView in self.iconViewArray) {
        [tempView.layer removeAllAnimations];
        [tempView removeFromSuperview];
    }
}

//#pragma mark 创建视图
/// 创建和计算 souler 头像数据
- (void)createSoulersIconView {
    dispatch_group_t group = dispatch_group_create();
    self.iconViewArray = [NSMutableArray arrayWithCapacity:0];

    NSInteger count = self.meetModel.recentViewUser.count;
    NSMutableArray *array = [NSMutableArray arrayWithCapacity:count];
    
    for (int i = 0; i < count; i++) {
        dispatch_group_enter(group);
        UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(70, 14, 22, 22)];
        imageView.layer.borderWidth = 1;
        imageView.layer.masksToBounds = YES;
        imageView.layer.cornerRadius = 11.0;
        [imageView.layer so_setLayerBorderColorTag:0];
        imageView.alpha = 0;
        SOHomeRecentViewUserModel *userModel = self.meetModel.recentViewUser[i];
        if (userModel.avatarName) {
            NSString *avatarName = [userModel.avatarName stringByAddingPercentEncodingWithAllowedCharacters:NSCharacterSet.URLQueryAllowedCharacterSet];
            NSURL *url = [[SoulGetMediaAddressManager shareInstance] getHeadsImageUrlWithPath:avatarName size:CGSizeMake(22, 22)];
            
            // 添加下载信息， 0代表该路径没有完成
            [array addObject:@{url: @(0)}];

            [imageView so_setImageWithURL:url placeholder:[UIImage imageNamed:userModel.avatarColor] options:0 completion:^(UIImage *_Nullable image, NSURL *url, NSError *_Nullable error) {
                for (NSInteger index = 0; index < array.count; index++) {
                    NSDictionary *dic = array[index];
                    NSURL *key = dic.allKeys.firstObject;
                    NSNumber *value = dic[key] ?: @(0);
                    if ([key.absoluteString isEqualToString:url.absoluteString]) {
                        // 找下一个key
                        if ( value.intValue == 1) {
                            continue;
                        } else {
                            array[index] = @{url: @(1)};
                            dispatch_group_leave(group);
                        }
                    }
                }
            }];
        } else {
            NSString *background = [userModel.background stringByAddingPercentEncodingWithAllowedCharacters:NSCharacterSet.URLQueryAllowedCharacterSet];
            NSURL *url = [NSURL URLWithString:background];
            
            // 添加下载信息， 0代表该路径没有完成
            [array addObject:@{url: @(0)}];
            
            [imageView so_setImageWithURL:[NSURL URLWithString:background] placeholder:nil options:0 completion:^(UIImage *_Nullable image, NSURL *url, NSError *_Nullable error) {
                for (NSInteger index = 0; index < array.count; index++) {
                    NSDictionary *dic = array[index];
                    NSURL *key = dic.allKeys.firstObject;
                    NSNumber *value = dic[key] ?: @(0);
                    if ([key.absoluteString isEqualToString:url.absoluteString]) {
                        // 找下一个key
                        if ( value.intValue == 1) {
                            continue;
                        } else {
                            array[index] = @{url: @(1)};
                            dispatch_group_leave(group);
                        }
                    }
                }
            }];
        }
        [self addSubview:imageView];
        [self.iconViewArray addObject:imageView];
    }
    SOWeakIfy(self);
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        SOStrongIfy(self);
        [self runAnimation];
    });
}

- (void)runAnimation {
    dispatch_group_t group = dispatch_group_create();
    for (int i = 0; i < self.iconViewArray.count; i++) {
        UIView *imageView = self.iconViewArray[i];
        CGFloat left = MeetIcon_InitLeft - self.iconViewArray.count * MeetIcon_fixWidth + i * MeetIcon_fixWidth * 2 + 10;
        dispatch_group_enter(group);
        [UIView animateWithDuration:0.5 delay:0.1 * i options:0 animations:^{
            imageView.alpha = 1;
            imageView.left = left;
        }                completion:^(BOOL finished) {
            [UIView animateWithDuration:0.5 delay:0.5 + 0.1 * i options:0 animations:^{
                imageView.alpha = 0;
                imageView.left = 30;
            }                completion:^(BOOL finished) {
                imageView.hidden = YES;
                [imageView removeFromSuperview];
                dispatch_group_leave(group);
            }];
        }];
    }
    
    SOWeakIfy(self);
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        SOStrongIfy(self);
        if (self.comepleteBlock) {
            self.comepleteBlock();
        }
    });
}


@end
