//
//  SAPersonalFocusCell.h
//  Soul_New
//
//  Created by 尚书威 on 2020/3/30.
//  Copyright © 2020 Soul. All rights reserved.
//

///个人主页/他人主页 关注关系cell
#import <UIKit/UIKit.h>
#import "SAHomePageMetricsModel.h"
#import "SAHomeSoulMateView.h"
#import "SoulHaoWuHeaderView.h"
#import "SOUserSchoolInfoView.h"
#import <SoulOfficial/SoulOfficial.h>

typedef enum : NSUInteger {
    SAHomePersonalFollowInfoViewNone,           //空视图
    SAHomePersonalFollowInfoViewFocus,          //只有关注数
    SAHomePersonalFollowInfoViewSoulMate,       //只有soulmate
    SAHomePersonalFollowInfoViewFocusSoulMate,  //关注数和soulmate
} SAHomePersonalFollowInfoViewType;

typedef enum : NSUInteger {
    FollowInfoViewClickFollow,//点击我关注的
    FollowInfoViewClickFollowed,//点击关注我的(被关注)
    FollowInfoViewClickMeeting,//点击看过我的
    FollowInfoViewClickGift//送礼
} SAHomePersonalFollowInfoViewClickType;

typedef enum : NSUInteger {
    SAHomePersonalFollowInfoViewMine,           // 自己主页
    SAHomePersonalFollowInfoViewOther,          // 他人主页
} SAHomePersonalFollowInfoViewOwnerType;

NS_ASSUME_NONNULL_BEGIN

@interface SAHomePersonalFollowInfoView : UIView

- (instancetype)initWithViewType:(SAHomePersonalFollowInfoViewType)type ownerType:(SAHomePersonalFollowInfoViewOwnerType)ownerType;

@property(nonatomic, assign) SAHomePersonalFollowInfoViewType viewType;
@property(nonatomic, assign) SAHomePersonalFollowInfoViewOwnerType ownerType;
@property(nonatomic, copy) void (^clickItemView)(SAHomePersonalFollowInfoViewClickType clickType);//点击关注/被关注/看过我
@property(nonatomic, copy) void (^clickSoulMate)(void);             /// 点击soulmate
@property(nonatomic, copy) void (^layoutUpdated)(void);

@property(nonatomic, strong) BrandInfo *info;
@property(nonatomic, strong) SAHomePageMetricsModel *followInfoModel;
@property(nonatomic, strong) SOUserSchoolInfoModel *schoolInfoModel;
@property(nonatomic, strong, nullable) SoulOfficialTouchPositionModel *meetBannerModel;
@property(nonatomic, strong, nullable) SoulOfficialTouchPositionModel *meetingLimitModel;

@property(nonatomic, copy) SoulHaoWuHeaderViewDidSelected haowuDidSelected;
@property(nonatomic, strong) SAHomeSoulMateView *soulMateView;
@property(nonatomic, assign) BOOL isShowGuide;

@end

NS_ASSUME_NONNULL_END
