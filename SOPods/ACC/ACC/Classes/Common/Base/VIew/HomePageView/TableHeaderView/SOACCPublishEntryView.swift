//
//  SOACCPublishEntryView.swift
//  ACC
//
//  Created by 1021500723 on 2023/11/1.
//

import UIKit
import Soul<PERSON><PERSON>itExtended
import SnapKit
import SoulRouter
import SoulEventBridge
import SwiftyJSON
import SoulProtocols

public class SOACCPublishEntryView: UIView {
    
    @objc
    public var closeActionHandler: (() -> Void)?
    
    private var items: [SOACCPublishEntryResponseCardData] = []
    private var requestId: String?
    
    private let topMargin: Float
    private let bottomMargin: Float
    
    // MARK: - Life Circle
    
    @objc
    public init() {
        topMargin = 0
        bottomMargin = 0
        super.init(frame: .zero)
        
        setupUI()
        setupUIConstraints()
    }
    
    @objc
    public init(topMargin: Float, bottomMargin: Float) {
        self.topMargin = topMargin
        self.bottomMargin = bottomMargin
        super.init(frame: .zero)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    @objc
    private func closeAction() {
        SOACCPublishEntryStore.resetLimitedTimeInterval()
        closeActionHandler?()
        SoulEventManager.eventClick("Postguideclose_HomePage", params: nil, pageId: nil, pagePrama: nil)
    }
    
    // MARK: - Data
    
    @objc
    public func update(model: SOACCPublishEntryResponseData) {
        requestId = model.requestId
        updateUI(model: model)
        updateUIConstraints()
    }
    
    // MARK: - UI
    
    private func setupUI() {
        addSubview(container)
        
        container.addSubview(headerView)
        titleLabel.textColor = .soul.color(3)
        titleLabel.font = .soul.PingFangSC(size: 14, type: .bold)
        headerView.addSubview(titleLabel)
        subtitleLabel.textColor = .soul.color(6)
        subtitleLabel.font = .soul.PingFangSC(size: 12)
        headerView.addSubview(subtitleLabel)
        closeButton.setImage(.acc_imageNamed("acc_publish_entry_close"), for: .normal)
        closeButton.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        headerView.addSubview(closeButton)
        
        container.addSubview(collectionView)
    }
    
    private func setupUIConstraints() {
        container.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalTo(topMargin)
            make.bottom.equalTo(-bottomMargin)
            make.height.equalTo(187)
        }
        headerView.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.top.trailing.equalToSuperview()
            make.height.equalTo(40)
        }
        titleLabel.snp.makeConstraints { make in
            make.leading.top.equalToSuperview()
            make.trailing.equalTo(closeButton.snp.leading).offset(-8)
        }
        subtitleLabel.snp.makeConstraints { make in
            make.leading.bottom.equalToSuperview()
            make.trailing.equalTo(closeButton.snp.leading).offset(-8)
        }
        closeButton.snp.makeConstraints { make in
            make.trailing.equalTo(-10)
            make.width.height.equalTo(30)
            make.centerY.equalToSuperview()
        }
        collectionView.snp.makeConstraints { make in
            make.leading.bottom.trailing.equalToSuperview()
            make.top.equalTo(headerView.snp.bottom).offset(12)
        }
    }
    
    private func updateUI(model: SOACCPublishEntryResponseData) {
        titleLabel.text = "🎉 发布助手"
        subtitleLabel.text = model.title
        items = model.guideDTOList
        collectionView.reloadData()
    }
    
    private func updateUIConstraints() {
        
    }
    
    private let container = UIView()
    private let headerView = UIView()
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    private let closeButton = UIButton()
    
    private lazy var collectionView: SOACCPublishEntryCollectionView = {
        let view = SOACCPublishEntryCollectionView(frame: .zero, collectionViewLayout: collectionLayout)
        view.dataSource = self
        view.delegate = self
        view.backgroundColor = .clear
        view.showsHorizontalScrollIndicator = false
        view.contentInset = .init(top: 0, left: 16, bottom: 0, right: 16)
        view.soul.register(SOACCPublishEntryTagCard.self)
        return view
    }()
    private lazy var collectionLayout: UICollectionViewFlowLayout = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.itemSize = .init(width: 210, height: 135)
        layout.minimumLineSpacing = 8
        return layout
    }()
}

extension SOACCPublishEntryView: UICollectionViewDelegate, UICollectionViewDataSource {
    
    public func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        items.count
    }
    
    public func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.soul.dequeue(SOACCPublishEntryTagCard.self, for: indexPath)
        cell.delegate = self
        if let item = items[safe: indexPath.item] {
            cell.update(item: item)
            let parameters = [
                "tag": item.title,
                "tagid": item.id,
                "card_id": item.id,
                "card_type": item.type ?? "-100",
                "requestId": requestId ?? "-100"
            ]
            SoulEventManager.eventExpose("Postguide_HomePage_expo", params: parameters, pageId: nil, pagePrama: nil)
        }
        return cell
    }
    
    public func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard let item = items[safe: indexPath.item] else { return }
        
        let vc = ZIKRouterToView(SubSquareTopicViewControllerProtocol.self)?.makeDestination(configuring: { config in
            config.addUserInfo(forKey: "tagName", object: item.title)
        })
        
        guard let vc = vc as? UIViewController & SubSquareTopicViewControllerProtocol else {
            return
        }
        vc.defaultTabType = .recommond
        SOGetVCHelper.getNavigationController()?.pushViewController(vc, animated: true)
    }
}

extension SOACCPublishEntryView: SOACCPublishEntryTagCardDelegate {
    
    func publishEntryTagCard(_ card: SOACCPublishEntryTagCard, didSelectedItem item: SOACCPublishEntryResponseCardData) {
        let extraMap = [
            "card_id": item.id,
            "card_type": item.type ?? "-100",
            "requestId": requestId ?? "-100"
        ]
        guard
            let tag = item.title.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
            let extraJson = JSON(extraMap).rawString(),
            let extraData = extraJson.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
            let publishRouterURL = URL(string: "soul://ul.soulapp.cn/post/postMoment?scene=album&flushCache=1&tags=\(tag)&pageFrom=homepageguide&extraData=\(extraData)")
        else { return }
        
        try? SoulRouterManager.sharedInstance().handleOpen(publishRouterURL, fromVc: nil, appParas: nil)
        let parameters = [
            "tag": item.title,
            "tagid": item.id,
            "card_id": item.id,
            "card_type": item.type ?? "-100",
            "requestId": requestId ?? "-100"
        ]
        SoulEventManager.eventClick("Postguide_HomePage_click", params: parameters, pageId: nil, pagePrama: nil)
    }
}

public class SOACCPublishEntryCollectionView: UICollectionView {
    
    public func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return false
    }
}
