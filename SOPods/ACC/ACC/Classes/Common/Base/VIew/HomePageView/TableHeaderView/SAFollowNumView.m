//
//  SAFollowNumView.m
//  Soul_New
//
//  Created by 尚书威 on 2020/3/31.
//  Copyright © 2020 Soul. All rights reserved.
//

#import "SAFollowNumView.h"
#import "SOACCHeader.h"
#import <SORemoteResKit/SORemoteResKit.h>

@interface SAFollowNumView()

@property (nonatomic, strong) LOTAnimationView* likeAnimationView;
@property (nonatomic, strong) LOTAnimationView* historyLikeAnimationView;
@property (nonatomic, strong) LOTAnimationView* meetingLimitActivityAnimationView;

@property (nonatomic, copy) dispatch_block_t delayOperateBlock;

@end

@implementation SAFollowNumView

- (instancetype)init {
    if (self = [super init]) {
        [self initUI];
    }
    return self;
}

- (void)initUI {
    self.backgroundColor = GET_COLOR(0);
    [self addSubview:self.likeAnimationView];
    [self addSubview:self.numLabel];
    [self addSubview:self.historyLikeAnimationView];
    [self addSubview:self.desLabel];
    [self addSubview:self.addNumLabel];
    [self addSubview:self.countingView];
    [self addSubview:self.meetingLimitActivityAnimationView];

    CGFloat numWidth = (KScreenWidth - 40) / 3 - 20;
    [self.likeAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.numLabel);
        make.size.mas_equalTo(CGSizeMake(40, 40));
    }];
    [self.numLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@16);
        make.centerX.equalTo(self);
        make.height.equalTo(@20);
        make.width.lessThanOrEqualTo(@(numWidth));
    }];
    [self.historyLikeAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.numLabel.mas_centerX).mas_offset(20);
        make.centerY.mas_equalTo(-20);
        make.size.mas_equalTo(CGSizeMake(100, 100));
    }];
    [self.meetingLimitActivityAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(70, 47));
        make.centerX.mas_equalTo(self);
        make.centerY.mas_equalTo(6);
    }];
    [self.desLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.numLabel.mas_bottom).offset(2);
        make.height.equalTo(@17);
        make.left.right.equalTo(self);
    }];
    [self.addNumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_lessThanOrEqualTo(self.numLabel.mas_right);
        make.height.equalTo(@17);
        make.top.equalTo(@4);
        make.right.mas_greaterThanOrEqualTo(self.mas_right);
    }];
    [self.countingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.numLabel.mas_right).offset(-1.5);
        make.height.equalTo(@17);
        make.top.equalTo(@6);
        make.width.equalTo(@19);
    }];

    SOWeakIfy(self);
    [self addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithActionBlock:^(id _Nonnull sender) {
        SOStrongIfy(self);
        if (self.shouldPlayMeetingAnimation) {
            self.shouldPlayMeetingAnimation = NO;
            self.desLabel.alpha = 1;
            self.numLabel.alpha = 1;
            /* 页面内限时活动模块点击点击埋点 */
            [SoulEvent eventClick:@"Visitact1_botton_Clk" params:nil pageId: nil pagePrama:nil];
        }
        self.meetingLimitActivityAnimationView.hidden = YES;
        if (self.tapViewBlock) {
            self.tapViewBlock();
        }
    }]];
}

- (void)playHistoryLikeAnimation:(SAHomePageMetricsModel *)meetModel {
    [self removeAnimation];
    
    self.numLabel.alpha = 1;
    self.desLabel.alpha = 1;
    self.historyLikeAnimationView.hidden = NO;
    SOWeakIfy(self);
    [self.historyLikeAnimationView setAnimationWithResKey:@"user_home/lottie_like_history" completeCallback:^(BOOL succeed) {
        SOStrongIfy(self);
        self.historyLikeAnimationView.loopAnimation = YES;
        [self.historyLikeAnimationView play];
    }];
}

- (void)playLikeAnimation:(SAHomePageMetricsModel *)meetModel {
    [self removeAnimation];
    self.desLabel.alpha = 1;
    self.likeAnimationView.hidden = NO;
    SOWeakIfy(self);
    [self.likeAnimationView setAnimationWithResKey:@"user_home/lottie_like_entry" completeCallback:^(BOOL succeed) {
        SOStrongIfy(self);
        [self.likeAnimationView play];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self playCountingViewAnimation:meetModel.homePageLikedMetric.addNum];
        });
    }];
}

- (void)playMeetViewAnimation:(SAHomePageMetricsModel *)meetModel {
    [self removeAnimation];
    self.desLabel.alpha = 1;
    SOSoulersMeetView* meetView = [[SOSoulersMeetView alloc] initWithFrame:CGRectMake(0, 0, 100, 37)];
    [self addSubview:meetView];
    [meetView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.centerX.equalTo(self);
        make.width.equalTo(@100);
        make.height.equalTo(@37);
    }];
    [meetView setupUIWithMeetModel:meetModel];

    SOWeakIfy(self);
    SOWeakIfy(meetView);
    meetView.comepleteBlock = ^{
        SOStrongIfy(meetView);
        if (meetView.superview) {
            SOStrongIfy(self);
            [self playCountingViewAnimation:meetModel.recentViewNum];
            [meetView removeFromSuperview];
        }
    };
    self.meetView = meetView;
}

- (void)playMeetingLimitActivity {
    SOWeakIfy(self);
    
    /* 页面内限时活动模块曝光曝光埋点 */
    [SoulEvent eventExpose:@"Visitact1_botton_Exp" params:nil pageId: nil pagePrama:nil];
    
    [self.meetingLimitActivityAnimationView setAnimationWithResKey:@"user_home/lottie_meeting_limit_activity" completeCallback:^(BOOL succeed) {
        SOStrongIfy(self);
        if (succeed) {
            self.numLabel.alpha = 0;
            self.countingView.alpha = 0;
            self.desLabel.alpha = 0;
            self.likeAnimationView.hidden = YES;
            self.historyLikeAnimationView.hidden = YES;
            self.meetingLimitActivityAnimationView.hidden = NO;
            [self.meetingLimitActivityAnimationView play];
        }
    }];
}

- (void)playCountingViewAnimation:(NSInteger)count {
    NSString *countString = [NSString stringWithFormat:@"+%ld", count];
    CGFloat countWidth = [countString widthForFont:[UIFont systemFontOfSize:9.0]] + 11;
    [self.countingView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@(countWidth));
    }];

    CGFloat animationDuration = count > 10 ? 1 : 0.5;
    [self.countingView countStartValue:1 endValue:count withDuration:animationDuration];
    [UIView animateWithDuration:0.25 animations:^{
        self.numLabel.alpha = 1;
        self.desLabel.alpha = 1;
        self.countingView.alpha = 1;
    } completion:^(BOOL finished) {
        if (self.shouldPlayMeetingAnimation) {
            SOWeakIfy(self);
            self.delayOperateBlock = dispatch_block_create(0, ^{
                SOStrongIfy(self);
                [self playMeetingLimitActivity];
            });
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), self.delayOperateBlock);
        }
    }];
}

- (void)removeAnimation {
    [self.meetView removeFromSuperview];
    self.numLabel.alpha = 0;
    self.countingView.alpha = 0;
    [self.numLabel.layer removeAllAnimations];
    [self.countingView.layer removeAllAnimations];
    self.likeAnimationView.hidden = YES;
    self.historyLikeAnimationView.hidden = YES;
    self.meetingLimitActivityAnimationView.hidden = YES;
    if (self.delayOperateBlock) {
        dispatch_block_cancel(self.delayOperateBlock);
    }
}

- (LOTAnimationView *)likeAnimationView {
    if (!_likeAnimationView) {
        _likeAnimationView = [[LOTAnimationView alloc] init];
    }
    return _likeAnimationView;
}

- (LOTAnimationView *)historyLikeAnimationView {
    if (!_historyLikeAnimationView) {
        _historyLikeAnimationView = [[LOTAnimationView alloc] init];
    }
    return _historyLikeAnimationView;
}

- (LOTAnimationView *)meetingLimitActivityAnimationView {
    if (!_meetingLimitActivityAnimationView) {
        _meetingLimitActivityAnimationView = [[LOTAnimationView alloc] init];
        _meetingLimitActivityAnimationView.hidden = YES;
    }
    return _meetingLimitActivityAnimationView;
}

- (SOCountingLabel *)countingView {
    if (!_countingView) {
        _countingView = [[SOCountingLabel alloc] init];
        _countingView.backgroundColor = HEXCOLOR(0xFE6063, 1);
        _countingView.layer.cornerRadius = 8.5;
        _countingView.layer.borderWidth = 1.5;
        _countingView.layer.borderColor = SOCGColor(0);
        SOWeakIfy(self);
        [SODarkModeManager addThemeChangeObserver:self handler:^(BOOL isDarkMode) {
            SOStrongIfy(self);
            self.countingView.layer.borderColor = SOCGColor(0);
        }];
        _countingView.layer.masksToBounds = YES;
        _countingView.font = [UIFont systemFontOfSize:9.0];
        _countingView.textColor = SOColor(0);
        _countingView.textAlignment = NSTextAlignmentCenter;
        _countingView.hidden = YES;
        _countingView.alpha = 0;
        _countingView.formatBlock = ^NSString *(CGFloat value) {
            return [NSString stringWithFormat:@"+%.0f", value];
        };
    }
    return _countingView;
}

- (UILabel *)numLabel {
    if (!_numLabel) {
        _numLabel = [[UILabel alloc] init];
        _numLabel.font = [UIFont fontWithName:[SOFontDefine fontBoldName] size:20];
        _numLabel.textColor = GET_COLOR(2);
        _numLabel.lineBreakMode = NSLineBreakByTruncatingMiddle;
        _numLabel.text = @"0";
        _numLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _numLabel;
}

- (UILabel *)desLabel {
    if (!_desLabel) {
        _desLabel = [[UILabel alloc] init];
        _desLabel.font = [UIFont fontWithName:[SOFontDefine fontRegularName] size:12];
        _desLabel.textColor = GET_COLOR(6);
        _desLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _desLabel;
}

- (UILabel *)addNumLabel {
    if (!_addNumLabel) {
        _addNumLabel = [[UILabel alloc] init];
        _addNumLabel.font = [UIFont fontWithName:[SOFontDefine fontBoldName] size:12];
        _addNumLabel.textColor = HEXCOLOR(0xFE5F63, 1);
        _addNumLabel.textAlignment = NSTextAlignmentLeft;
        _addNumLabel.hidden = YES;
    }
    return _addNumLabel;
}

@end
