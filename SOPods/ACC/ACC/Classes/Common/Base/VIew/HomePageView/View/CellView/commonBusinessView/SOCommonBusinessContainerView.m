//
//  SOCommonBusinessContainerView.m
//  ACC
//
//  Created by 11080237 on 2022/8/18.
//

#import "SOCommonBusinessContainerView.h"

@implementation SOCommonBusinessContainerView

#pragma mark SOCommonViewProtocol
- (NSString *)commonViewIdentifier{
    return NSStringFromClass(SOCommonBusinessContainerView.class);
}
- (void)setViewLayout:(SOCommonBusinessContainerViewLayout *)layout{
    if (![layout isKindOfClass:SOCommonBusinessContainerViewLayout.class]){
        return;
    }

    self.layout = (SOCommonBusinessContainerViewLayout *)layout;
    self.post = self.layout.post;
    
    [self.commonBusinessView removeFromSuperview];
    self.commonBusinessView = (UIView <SOCommonBusinessViewProtocol> *) [SOCommonBusinessManger makeDestinationWithType:self.post.globalViewModel.bizType.integerValue];
    CGSize size = layout.itemFrame.size;
    self.commonBusinessView.frame = CGRectMake(0, 0, size.width, size.height);
    [self addSubview:self.commonBusinessView];
    [self.commonBusinessView setDataModel:layout.commonBusinessLayout];
}


@end
