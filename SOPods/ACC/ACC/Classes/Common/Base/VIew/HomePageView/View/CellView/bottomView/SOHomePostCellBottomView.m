//
//  SOHomePostCellBottomView.m
//  ACC
//
//  Created by 11080237 on 2022/8/18.
//

#import <YYCategories/YYCategories.h>
#import <SoulAssets/SoulAssets.h>
#import <SoulUIKit/SoulUIKit.h>
#import "SOHomePostCellBottomView.h"

@interface SOHomePostCellBottomView()

@property(nonatomic, strong) UIView<SOCommonViewProtocol> *locationView;//位置

@property (nonatomic, strong) UIView *visibilityView;
@property (nonatomic, strong) UILabel *visibilityLabel;
@property (nonatomic, strong) UIImageView *visibilityIcon;

@end

@implementation SOHomePostCellBottomView

- (instancetype)initWithFrame:(CGRect)frame{
    if (self = [super initWithFrame:frame]){
        [self setupView];
    }
    return self;
}
- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}
- (void)setupView{
    [self addSubview:self.toolView];
    [self addSubview:self.giftInfoView];
    [self addSubview:self.recommdChatBtn];
    [self.visibilityView addSubview:self.visibilityIcon];
    [self.visibilityView addSubview:self.visibilityLabel];
    [self addSubview:self.visibilityView];
    
    [self.visibilityIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.bottom.equalTo(@0);
        make.width.height.equalTo(@16);
    }];
    [self.visibilityLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.visibilityIcon.mas_trailing).offset(4);
        make.trailing.equalTo(@0);
        make.centerY.equalTo(@0);
    }];
    [self.visibilityView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(@(-16));
        make.centerY.equalTo(self.mas_centerY).offset(self.layout.topMargin);
    }];
}

- (void)setLayout:(SOHomePostCellBottomViewLayout *)layout{
    if (![layout isKindOfClass:SOHomePostCellBottomViewLayout.class]){
        return;
    }
    _layout = layout;
    SOPost *post = layout.post;
    
    if (layout.locationLayout.isShow){
        self.locationView.hidden = NO;
        self.locationView.frame = layout.locationLayout.itemFrame;
        [self.locationView setViewLayout:layout.locationLayout];
        if ([self.locationView isKindOfClass:UIStackView.class]){
            UIStackView *stackView = (UIStackView *)self.locationView;
            stackView.layoutMarginsRelativeArrangement = false;
        }
    } else {
        self.locationView.hidden = YES;
    }

    if (!CGRectEqualToRect(layout.toolViewFrame, CGRectZero)) {
        self.toolView.hidden = NO;
        self.toolView.frame = layout.toolViewFrame;
        self.toolView.post = post;
    } else {
        self.toolView.hidden = YES;
    }
    
    if ([self chatAIEnableWithPost:layout.post]) {
        if ([self chatAIEntryEnableWithPost:layout.post]) {
            self.recommdChatBtn.hidden = NO;
            self.recommdChatBtn.alpha = 1;
        } else {
            self.recommdChatBtn.hidden = YES;
            self.recommdChatBtn.alpha = 0;
        }
        [self.recommdChatBtn setTitle:@"" forState:UIControlStateNormal];
        [self.recommdChatBtn setImage:[UIImage acc_imageNamed:@"acc_ai_chat_button_icon"] forState:UIControlStateNormal];
        self.recommdChatBtn.width = 68;
        self.recommdChatBtn.frame = CGRectMake(KScreenWidth - 68 - 62, 0 + self.layout.topMargin, 68, 24);
        self.recommdChatBtn.backgroundColor = UIColor.clearColor;
        self.visibilityView.hidden = YES;
        self.giftInfoView.hidden = YES;
    } else {
        self.recommdChatBtn.hidden = !layout.showedRecommd;
        [self.recommdChatBtn setTitle:@"聊聊这条" forState:UIControlStateNormal];
        
        if ([SoulUserManager isSelfEcpt:layout.post.authorIdEcpt]) {
            self.visibilityIcon.image = [UIImage square_imageNamed:layout.visibilityIconName];
            self.visibilityLabel.text = layout.visibilityTitle;
            self.visibilityView.hidden = NO;
            
            self.giftInfoView.hidden = YES;
        } else {
            self.visibilityView.hidden = YES;
            
            if (!CGRectEqualToRect(layout.giftViewFrame, CGRectZero)) {
                self.giftInfoView.hidden = NO;
                self.giftInfoView.frame = layout.giftViewFrame;
                self.giftInfoView.model = post.giftInfo;
                self.giftInfoView.post = layout.post;
            } else {
                self.giftInfoView.hidden = YES;
            }
        }
    }
}

- (void)setDelegate:(id<SAPersonalHomePageCellToolBarViewDelegate>)delegate{
    _delegate = delegate;
    self.toolView.cellDelegate = delegate;
}

- (BOOL)chatAIEnableWithPost:(SOPost *)post {
    if (post.officialTag == 1) return NO;
    if ([post.authorIdEcpt isEqualToString:SOUserInfoManager.sharedInstance.userIdEcpt]) return NO;
    if (![SOABItem(@"214166").value isEqualToString:@"a"]) return NO;
    return YES;
}

- (BOOL)chatAIEntryEnableWithPost:(SOPost *)post {
    if (post.officialTag == 1) return NO;
    if ([post.authorIdEcpt isEqualToString:SOUserInfoManager.sharedInstance.userIdEcpt]) return NO;
    if (![SOABItem(@"214166").value isEqualToString:@"a"]) return NO;
    if (![post.type isEqualToString:@"TEXT"] && ![post.type isEqualToString:@"IMAGE"]) return NO;
    return YES;
}

- (void)displayRecommdChatBtnAnimation {
    [self.recommdChatBtn.layer setAnchorPoint:CGPointMake(1, 0.5)];
    self.recommdChatBtn.frame = CGRectMake(KScreenWidth - 82 - 16 - 32 - 8, 0 + self.layout.topMargin, 82, 24);
    
    self.recommdChatBtn.transform = CGAffineTransformScale(CGAffineTransformIdentity, 0.0001, 0.0001);
    self.recommdChatBtn.alpha = 0;
    self.recommdChatBtn.hidden = NO;
    [UIView animateWithDuration:0.3 animations:^{
        self.recommdChatBtn.alpha = 1;
        self.recommdChatBtn.transform = CGAffineTransformIdentity;
    } completion:^(BOOL finished) {
        /* 聊聊这条_曝光曝光埋点 */
        [SoulEvent eventExpose:@"ChatReference_exp" params:nil pageId: nil pagePrama:nil];
    }];
}

- (void)showRecommdChatBtnWithAnimation:(BOOL)animation{
    [self showRecommdChatBtnAnimation:animation];
}

- (void)showRecommdChatBtnAnimation:(BOOL)animation {
    if ([self chatAIEnableWithPost:self.layout.post]) return;
    if (self.layout.showedRecommd) {
        return;
    }
    self.layout.showedRecommd = YES;
    if (animation) {
        if (self.giftInfoView.hidden == NO) {
            [UIView animateWithDuration:0.15 animations:^{
                self.giftInfoView.alpha = 0;
            } completion:^(BOOL finished) {
                if (finished) {
                    self.giftInfoView.hidden = YES;
                    [self displayRecommdChatBtnAnimation];
                }
            }];
        } else {
            [self displayRecommdChatBtnAnimation];
        }
        
    } else {
        self.giftInfoView.alpha = 0;
        self.giftInfoView.hidden = YES;
        self.recommdChatBtn.alpha = 1;
        self.recommdChatBtn.hidden = NO;
    }
}

- (void)dissRecommdChatBtnAnimation:(BOOL)animation {
    self.recommdChatBtn.hidden = YES;
    if (!CGRectEqualToRect(self.layout.giftViewFrame, CGRectZero)) {
        self.giftInfoView.hidden = NO;
        self.giftInfoView.alpha = 1;
    }
}

#pragma mark SOCommonViewProtocol
- (NSString *)commonViewIdentifier{
    return NSStringFromClass(self.class);
}
- (void)setViewLayout:(id)layout{
    self.layout = layout;
}
#pragma mark setter
- (SAPersonalHomePageCellToolBarView *)toolView {
    if (!_toolView) {
        _toolView = [[SAPersonalHomePageCellToolBarView alloc] initWithFrame:CGRectZero];
    }
    return _toolView;
}

- (UIView<SOCommonViewProtocol> *)locationView {
    if (!_locationView && self.layout.locationLayout.isShow) {
        _locationView = [self addSubViewWithLayout:self.layout.locationLayout];
    }
    return _locationView;
}
- (SOPostGiftRecordInfoView *)giftInfoView {
    if (!_giftInfoView) {
        _giftInfoView = [[SOPostGiftRecordInfoView alloc] initWithContainRecommendInfo:NO];
        [_giftInfoView distributionRight];
        SOWeakIfy(self);
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithActionBlock:^(id _Nonnull sender) {
            SOStrongIfy(self);
            if ([self.delegate.delegate respondsToSelector:@selector(homeCell:tapGiftRankView:)]) {
                [self.delegate.delegate homeCell:self.delegate tapGiftRankView:self.layout.post];
            }
        }];
        [_giftInfoView addGestureRecognizer:tap];
        _giftInfoView.userInteractionEnabled = YES;
    }
    return _giftInfoView;
}

- (UIButton *)recommdChatBtn {
    if (!_recommdChatBtn) {
        _recommdChatBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _recommdChatBtn.frame = CGRectMake(KScreenWidth - 82 - 16 - 32 - 8, 0 + self.layout.topMargin, 82, 24);
        _recommdChatBtn.backgroundColor = SOColor(1);
        [_recommdChatBtn setTitle:@"聊聊这条" forState:UIControlStateNormal];
        [_recommdChatBtn setImage:[UIImage acc_imageNamed:@"icon_homepage_recommdchat"] forState:UIControlStateNormal];
        [_recommdChatBtn setTitleColor:SOColor(0) forState:UIControlStateNormal];
        _recommdChatBtn.titleLabel.font = FONT(TextFontName, 11);
        [_recommdChatBtn addTarget:self action:@selector(tap_recommdChatBtn) forControlEvents:UIControlEventTouchUpInside];
        _recommdChatBtn.layer.cornerRadius = 12;
        _recommdChatBtn.layer.masksToBounds = YES;
        [_recommdChatBtn setButtonImagePosition:SOButtonImagePositionLeft spacing:2];
        _recommdChatBtn.hidden = YES;
        _recommdChatBtn.alpha = 0;
    }
    return _recommdChatBtn;
}

- (void)tap_recommdChatBtn {
    if ([self chatAIEnableWithPost:self.layout.post]) {
        if ([self.delegate.delegate respondsToSelector:@selector(tapRecommandAIChat:)]) {
            [self.delegate.delegate tapRecommandAIChat:self.layout.post];
        }
    } else {
        if ([self.delegate.delegate respondsToSelector:@selector(tapRecommandChat:)]) {
            [self.delegate.delegate tapRecommandChat:self.layout.post];
        }
    }
}

- (UIView *)visibilityView {
    if (!_visibilityView) {
        _visibilityView = [UIView new];
        _visibilityView.backgroundColor = SOColor(0);
    }
    return _visibilityView;
}

- (UIImageView *)visibilityIcon {
    if (!_visibilityIcon) {
        _visibilityIcon = [UIImageView new];
    }
    return _visibilityIcon;
}

- (UILabel *)visibilityLabel {
    if (!_visibilityLabel) {
        _visibilityLabel = [UILabel new];
        _visibilityLabel.font = FONT(TextFontName, 12);
        _visibilityLabel.textColor = SOColor(6);
    }
    return _visibilityLabel;
}

@end
