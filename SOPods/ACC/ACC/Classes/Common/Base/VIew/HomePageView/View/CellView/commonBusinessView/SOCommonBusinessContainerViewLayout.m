//
//  SOCommonBusinessContainerViewLayout.m
//  ACC
//
//  Created by 11080237 on 2022/8/18.
//

#import "SOCommonBusinessContainerViewLayout.h"
#import "SOCommonBusinessContainerView.h"
#import <SOUGCInterface/NSMutableDictionary+Build.h>

@interface SOCommonBusinessContainerViewLayout ()

@property (nonatomic, copy)  NSString *boxType;

@end
@implementation SOCommonBusinessContainerViewLayout


+ (void)load{
    static dispatch_once_t once;
    dispatch_once(&once, ^{
        [SOCommonViewLayoutManage.shareInstance addCreaterBlock:^id<SOCommonViewLayoutProtocol> _Nullable(id  _Nonnull data, NSDictionary * _Nullable extData) {
            SOCommonBusinessContainerViewLayout *layout = [SOCommonBusinessContainerViewLayout new];
            if ([data isKindOfClass:SOPost.class]) {
                layout.post = (SOPost *)data;
            }
            if ([extData[@"boxType"] isKindOfClass:NSString.class]) {
                layout.boxType = extData[@"boxType"];
            }
            return layout;
        } forType:SOCommonViewLayoutKey_BussinessView];
    });
}
- (instancetype)init{
    if (self = [super init]){
        self.boxType = @"homePage";
    }
    return self;
}

- (instancetype)layout{
    @weakify(self);
    self.commonBusinessLayout = (id)[[SOCommonBusinessManger shareInstance] configModelWithScene:SOBizUsedSceneTypePostHomePage withType:self.post.globalViewModel.bizType.integerValue config:^(id<SOCommonBusinessCofigModelProtocol>  _Nonnull configModel) {
        @strongify(self);
        configModel.postIdAssociated = SOString(self.post.postId);
        configModel.authorId = SOString(self.post.authorId);
        configModel.data = self.post.globalViewModel.bizJson;
        NSMutableDictionary *extMap = [NSMutableDictionary initWithExtMap:^(NSMutableDictionary * _Nonnull data) {
            data[@"postId"] = self.post.postId;
            data[@"post"] = [self.post yy_modelToJSONString];
        }];
        configModel.extMap = extMap;
    }];
    NSValue *boxSize = _commonBusinessLayout.boxSize;
    self.itemFrame = CGRectMake(0, 0, boxSize.CGSizeValue.width, boxSize.CGSizeValue.height);
    return self;
}


#pragma mark  SOCommonViewLayoutProtocol
@synthesize itemFrame;

- (NSString *)commonViewIdentifier{
    return NSStringFromClass(SOCommonBusinessContainerView.class);
}
- (BOOL)isShow{
    return self.post.globalViewModel.bizType.integerValue > 0;
}
- (UIView<SOCommonViewProtocol> *)commonViewCreateView{
    return [[SOCommonBusinessContainerView alloc] init];
}

@end
