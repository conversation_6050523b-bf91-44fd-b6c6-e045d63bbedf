//
//  SOPersonalHomePagePublishAICellComponentVisitCard.swift
//  ACC
//
//  Created by al<PERSON> on 2025/7/17.
//

import SOWebImage
import SnapKit
import SoulCore
import SoulUIKitExtended
import UIKit

/// Card component for displaying AI visit suggestions
/// Shows rotated cards with post images and user information
class SOPersonalHomePagePublishAICellComponentVisitCard: UIView {

    // MARK: - Life Circle

    init() {
        super.init(frame: .zero)

        setupUI()
        setupUIConstraints()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Action

    // MARK: - Data

    /// Updates the component with new AI visit data
    /// - Parameter data: The AI visit data containing card list information
    func update(data: SOPersonalHomePagePublishAIData) {
        updateUI(data: data)
        updateUIConstraints(data: data)
    }

    // MARK: - UI

    /// Sets up the initial UI components
    private func setupUI() {
        addSubview(container)
        container.addSubview(imageView)
        container.addSubview(secondCard)
        container.addSubview(firstCard)
    }

    /// Sets up the UI constraints with rotation and anti-aliasing
    private func setupUIConstraints() {
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        imageView.snp.makeConstraints { make in
            make.top.equalTo(14)
            make.trailing.equalTo(-14)
            make.width.equalTo(106)
            make.height.equalTo(91)
        }
        
        firstCard.snp.makeConstraints { make in
            make.width.equalTo(70)
            make.height.equalTo(86)
            make.centerX.equalToSuperview().offset(-14)
            make.centerY.equalToSuperview()
        }
        // Set anti-aliasing properties to resolve jagged edges after rotation
        firstCard.layer.allowsEdgeAntialiasing = true
        firstCard.layer.shouldRasterize = true
        firstCard.layer.rasterizationScale = UIScreen.main.scale
        firstCard.transform = CGAffineTransform(rotationAngle: -8 * .pi / 180)
        
        secondCard.snp.makeConstraints { make in
            make.width.equalTo(70)
            make.height.equalTo(86)
            make.centerX.equalToSuperview().offset(10)
            make.centerY.equalToSuperview()
        }
        // Set anti-aliasing properties to resolve jagged edges after rotation
        secondCard.layer.allowsEdgeAntialiasing = true
        secondCard.layer.shouldRasterize = true
        secondCard.layer.rasterizationScale = UIScreen.main.scale
        secondCard.transform = CGAffineTransform(rotationAngle: 8 * .pi / 180).scaledBy(x: 0.9, y: 0.9)
    }

    /// Updates the UI with the provided AI visit data
    /// Controls visibility of cards based on available data
    /// - Parameter data: The AI visit data
    private func updateUI(data: SOPersonalHomePagePublishAIData) {
        if let cardList = data.visitData?.cardList, cardList.count >= 2 {
            firstCard.isHidden = false
            secondCard.isHidden = false
            imageView.isHidden = true
            firstCard.update(data: cardList[0])
            secondCard.update(data: cardList[1])
        } else if let cardList = data.visitData?.cardList, cardList.count == 1 {
            firstCard.isHidden = false
            secondCard.isHidden = true
            imageView.isHidden = true
            firstCard.update(data: cardList[0])
        } else {
            firstCard.isHidden = true
            secondCard.isHidden = true
            imageView.isHidden = false
        }
    }

    /// Updates UI constraints based on the provided data
    /// - Parameter data: The AI visit data
    private func updateUIConstraints(data: SOPersonalHomePagePublishAIData) {

    }

    // MARK: - UI Components

    /// Main container view for all content
    private let container = UIView()

    /// Placeholder image when no cards are available
    private let imageView = UIImageView().then {
        $0.image = .acc_imageNamed("acc_publish_ai_visit_placeholder")
    }
    
    /// First rotated card displaying post information
    private let firstCard = SOPersonalHomePagePublishAICellComponentVisitCardItem().then {
        $0.isHidden = true
    }
    
    /// Second rotated card displaying post information
    private let secondCard = SOPersonalHomePagePublishAICellComponentVisitCardItem().then {
        $0.isHidden = true
    }
}

/// Individual card item component for displaying post information
/// Shows post image, user avatar, title, and subtitle with shadow effects
class SOPersonalHomePagePublishAICellComponentVisitCardItem: UIView {

    // MARK: - Life Circle

    init() {
        super.init(frame: .zero)

        setupUI()
        setupUIConstraints()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Action

    // MARK: - Data

    /// Updates the card item with new post data
    /// - Parameter data: The post data containing image, user info, and text
    func update(data: SOPost) {
        updateUI(data: data)
        updateUIConstraints(data: data)
    }

    // MARK: - UI

    /// Sets up the initial UI components with shadow effects
    private func setupUI() {
        layer.shadowColor = UIColor.black.cgColor
        layer.shadowOpacity = 0.08
        layer.shadowOffset = CGSize(width: 0, height: 2)
        layer.shadowRadius = 8
        layer.masksToBounds = false
        
        addSubview(shadowView)
        addSubview(container)
        container.addSubview(imageView)
        container.addSubview(avatarView)
        container.addSubview(titleLabel)
        container.addSubview(subTitleLabel)
        
        addSubview(iconView)
    }

    /// Sets up the UI constraints for all components
    private func setupUIConstraints() {
        container.snp.makeConstraints { make in
            make.leading.equalTo(2)
            make.trailing.equalTo(-2)
            make.top.equalTo(2)
            make.bottom.equalTo(2)
        }
        shadowView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        imageView.snp.makeConstraints { make in
            make.leading.top.trailing.equalToSuperview()
            make.height.equalTo(50)
        }
        avatarView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(imageView.snp.bottom).offset(10)
            make.width.height.equalTo(20)
        }
        titleLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalTo(avatarView.snp.bottom).offset(0)
        }
        subTitleLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalTo(titleLabel.snp.bottom).offset(0)
        }
        iconView.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview()
        }
    }

    /// Updates the UI with the provided post data
    /// - Parameter data: The post data
    private func updateUI(data: SOPost) {
        if let attachment = data.attachments?.firstObject as? SOAttachment,
            let fileURL = attachment.fileUrl
        {
            let imageURL = fileURL + "?x-oss-process=image/crop,w_512,g_center"
            imageView.so_setImage(with: URL(string: imageURL))
        }
        if let avatarName = data.avatarName, let avatarColor = data.avatarColor {
            avatarView.setHeadWithName(avatarName, size: 20)
            avatarView.setHeadBgWithName(avatarColor, size: 20)
        }
        titleLabel.text = data.signature
        subTitleLabel.text = data.showIpLocationInfo
    }

    /// Updates UI constraints based on the provided data
    /// - Parameter data: The post data
    private func updateUIConstraints(data: SOPost) {

    }

    // MARK: - UI Components

    /// Shadow view for card depth effect
    private let shadowView = UIView().then {
        $0.layer.cornerRadius = 6
        $0.layer.masksToBounds = true
        $0.backgroundColor = .soul.color(0)
    }
    
    /// Main container view for card content
    private let container = UIView().then {
        $0.layer.cornerRadius = 6
        $0.layer.masksToBounds = true
    }
    
    /// Image view for displaying post image
    private let imageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
        $0.layer.masksToBounds = true
    }
    
    /// Icon overlay on the image
    private let iconView = UIImageView(image: .acc_imageNamed("acc_publish_ai_visit_image_icon"))
    
    /// Avatar view for displaying user profile picture
    private let avatarView = SOHeadPortraitView().then {
        $0.sizeType = 20
    }
    
    /// Label for displaying post signature/title
    private let titleLabel = UILabel().then {
        $0.font = .soul.PingFangSC(size: 8)
        $0.textColor = .soul.color(2)
        $0.textAlignment = .center
    }
    
    /// Label for displaying location information
    private let subTitleLabel = UILabel().then {
        $0.font = .soul.PingFangSC(size: 6)
        $0.textColor = .soul.color(15)
        $0.textAlignment = .center
    }

}
