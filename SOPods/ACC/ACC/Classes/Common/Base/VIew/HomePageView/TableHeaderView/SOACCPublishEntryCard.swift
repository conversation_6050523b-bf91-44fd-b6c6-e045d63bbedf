//
//  SOACCPublishEntryCard.swift
//  ACC
//
//  Created by 1021500723 on 2023/11/2.
//

import UIKit
import SoulUIKitExtended
import SoulAssets
import SnapKit

struct SOACCPublishEntryCard {
    
}

protocol SOACCPublishEntryTagCardDelegate: AnyObject {
    func publishEntryTagCard(_ card: SOACCPublishEntryTagCard, didSelectedItem item: SOACCPublishEntryResponseCardData)
}

class SOACCPublishEntryTagCard: UICollectionViewCell {
    
    weak var delegate: SOACCPublishEntryTagCardDelegate?
    private var item: SOACCPublishEntryResponseCardData?
    
    // MARK: - Life Circle
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Data
    
    func update(item: SOACCPublishEntryResponseCardData) {
        self.item = item
        updateUI(title: item.title, subtitle: item.desc)
        updateUIConstraints()
    }
    
    // MARK: - Action
    
    @objc
    private func selectAction() {
        guard let item = item else { return }
        delegate?.publishEntryTagCard(self, didSelectedItem: item)
    }
    
    // MARK: - UI
    
    private func setupUI() {
        addSubview(backgroundImageView)
        
        button.setTitle("发布", for: .normal)
        
        button.setTitleColor(.soul.color(1), for: .normal)
        button.backgroundColor = .soul.color(0)
        button.titleLabel?.font = .soul.PingFangSC(size: 14, type: .medium)
        button.layer.cornerRadius = 12
        button.layer.masksToBounds = true
        button.addTarget(self, action: #selector(selectAction), for: .touchUpInside)
        addSubview(button)
        
        titleLabel.textColor = .soul.color(3)
        titleLabel.font = .soul.PingFangSC(size: 13, type: .medium)
        titleLabel.numberOfLines = 2
        addSubview(titleLabel)
        
        subtitleLable.textColor = .soul.color(6)
        subtitleLable.font = .soul.PingFangSC(size: 12)
        addSubview(subtitleLable)
        
    }
    
    private func setupUIConstraints() {
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        button.snp.makeConstraints { make in
            make.leading.equalTo(12)
            make.bottom.equalTo(-16)
            make.width.equalTo(60)
            make.height.equalTo(24)
        }
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(12)
            make.trailing.equalTo(-12)
            make.top.equalTo(14)
        }
        subtitleLable.snp.makeConstraints { make in
            make.leading.equalTo(12)
            make.trailing.equalTo(-12)
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
        }
    }
    
    private func updateUI(title: String?, subtitle: String?) {
        let imageAttchment = NSTextAttachment()
        imageAttchment.image = .acc_imageNamed("acc_publish_entry_title_icon_hot")
        imageAttchment.bounds = .init(x: 0, y: -(titleLabel.font.lineHeight - 16) - 1, width: 32, height: 16)
        let imageText = NSMutableAttributedString(attachment: imageAttchment)
        
        let titleText = NSMutableAttributedString(string: " #\(title ?? "")", attributes: nil)
        titleText.yy_font = titleLabel.font
        titleText.yy_color = titleLabel.textColor
        
        let attributedText = NSMutableAttributedString()
        attributedText.append(imageText)
        attributedText.append(titleText)
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 3
        paragraphStyle.lineBreakMode = .byTruncatingTail
        attributedText.addAttribute(.paragraphStyle, value: paragraphStyle, range: attributedText.yy_rangeOfAll())
        titleLabel.attributedText = attributedText
        
        subtitleLable.text = subtitle
    }
    
    private func updateUIConstraints() {
        
    }
    
    private let backgroundImageView = UIImageView(image: .acc_imageNamed("acc_publish_entry_background"))
    private let titleLabel = UILabel()
    private let subtitleLable = UILabel()
    private let button = UIButton()
}
