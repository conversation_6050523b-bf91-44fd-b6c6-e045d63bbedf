//
//  SOPersonalHomePagePublishAIDataStore.swift
//  ACC
//
//  Created by al<PERSON> on 2025/6/3.
//

import Foundation
import SoulABStrategy
import SoulAPI
import SoulCore
import SoulUserModule
import SwiftyJSON
import YYModel

/// Data store for managing personal homepage publish data
public class SOPersonalHomePagePublishAIDataStore: NSObject {

    /// Shared singleton instance
    @objc
    public static let shared = SOPersonalHomePagePublishAIDataStore()

    /// Current user's publish data
    public var item: SOPersonalHomePagePublishAIData? {
        items[cacheKey]
    }

    /// Cache of publish data items, keyed by encrypted user ID
    private(set) var items: [String: SOPersonalHomePagePublishAIData] = [:]

    private(set) var publishFinishState = false

    var loadingAnimationPlayed = false

    private override init() {
        super.init()

        setupNotification()
    }
}

extension SOPersonalHomePagePublishAIDataStore {

    @objc
    public func isEnable() -> Bool {
        /// AB
        guard let ab = ABTestStrategyCenter.shareInstance().multiTestStrategy(forKey: "214284"),
            ab.value == "a"
        else { return false }
        guard isEnableMMKV(key: kMMKVEnableKey) else { return false }
        return true
    }

    /// Checks if time-based limitations allow feature to be enabled
    /// - Parameter key: MMKV key for storing time limit
    /// - Returns: Boolean indicating if feature should be enabled based on time
    private func isEnableMMKV(key: String) -> Bool {
        let time = MMKV.default()?.double(forKey: key) ?? 0
        guard time > 0 else { return true }
        let now = Date()
        guard now.timeIntervalSince1970 > time else { return false }
        let timeInterval = now.timeIntervalSince1970 - time
        let limitTimeInterval = TimeInterval(enableLimitDayCount * 24 * 60 * 60)
        return timeInterval > limitTimeInterval
    }

    /// Resets the regular publish time limit to current time
    @objc
    public func disable() {
        let time = Date().timeIntervalSince1970
        MMKV.default()?.set(time, forKey: kMMKVEnableKey)
    }

    /// MMKV key for storing regular publish time limit
    private var kMMKVEnableKey: String {
        "com.acc.publish.ai.guide.enable-" + SOUserInfoManager.sharedInstance().userIdEcpt
    }

    /// Limit day count
    private var enableLimitDayCount: Int {
        var count: Int
        if let value = SoulConfigManager.sharedInstance()?.globalConfig.object(forKey: "ugc_homepage_ai_assistant_show_time_limit") {
            count = SOIntegerWith(value, 30)
        } else {
            count = 30
        }
        return max(count, 1)
    }
}

extension SOPersonalHomePagePublishAIDataStore {

    // MARK: - Request Data

    /// Fetches publish guide data from server
    /// - Parameter completion: Callback with fetched data or cached data
    @objc
    public func requestData(
        update: @escaping (SOPersonalHomePagePublishAIData?) -> Void,
        completion: @escaping (SOPersonalHomePagePublishAIData?) -> Void
    ) {
        let key = cacheKey
        var item = items[key]
        let parameters: [String: Any] = [:]
//        #if DEBUG
//        if item == nil {
//            item = fakeItem()
//        }
//        #endif
        if let item = item {
            update(item)
        }
        guard let host = HttpDnsHelper.curEnvDomain(.newPost) else {
            completion(item)
            return
        }
        let path = "/v1/post/homepage/ai/assistant/guide"
        SoulAPIManager.sharedInstance()?.requestGET(
            path, baseUrl: host, parameters: parameters,
            success: { model, _ in
                guard let model = model, model.codeSuccess, let modelData = model.data else {
                    completion(item)
                    return
                }
                let json = JSON(modelData)
                guard let jsonData = try? json.rawData() else {
                    completion(item)
                    return
                }
                guard
                    let e = try? JSONDecoder().decode(
                        SOPersonalHomePagePublishAIData.self, from: jsonData)
                else {
                    completion(item)
                    return
                }
                guard e.scoreData != nil else {
                    completion(item)
                    return
                }
                e.isFold = item?.isFold ?? false
                item = e

                let postListData = json["userShowDTO"]["userDiagramPostList"].arrayValue
                if !postListData.isEmpty {
                    var cardList: [SOPost] = []
                    postListData.forEach {
                        if let postJson = try? $0.rawData(), let post = SOPost.yy_model(withJSON: postJson) {
                            cardList.append(post)
                        }
                    }
                    item?.visitData?.cardList = cardList
                }

                self.items[key] = item
                completion(item)
            },
            fail: { _, _ in
                completion(item)
            })
    }

    private func fakeItem() -> SOPersonalHomePagePublishAIData {
        let item = SOPersonalHomePagePublishAIData()
        item.scoreData = SOPersonalHomePagePublishAIScoreData()
        item.scoreData?.score = 38
        item.scoreData?.scoreTotal = 100
        item.scoreData?.title = "今天主页吸引为<highlight>38</highlight>分"
        item.publishData = SOPersonalHomePagePublishAIPublishData()
        item.publishData?.enable = true
        item.publishData?.title = "已自动生成了今天心情瞬间, 发布提升<highlight>吸引力+5</highlight>"
        item.publishData?.subTitle = "如果万事注定不能称心如意的话，那就祝你在就祝你在就祝你在就祝你..."
        item.publishData?.imageURL =
            "https://img.soulapp.cn/app-source-prod/app-1/19/d7a3a86c-d49c-4688-960a-c7642c65c07c.png"
        item.publishData?.actionTitle = "去发布"
        return item
    }
}

extension SOPersonalHomePagePublishAIDataStore {

    func flushCache() {
        items[cacheKey] = nil
    }

    private var cacheKey: String {
        "userIdEcpt" + SOUserInfoManager.sharedInstance().userIdEcpt
    }
}

extension SOPersonalHomePagePublishAIDataStore {

    // MARK: - Notification

    private func setupNotification() {
        NotificationCenter.default.addObserver(
            self, selector: #selector(publishFinish(_:)),
            name: .init(rawValue: "post_publish_finished"), object: nil)
    }

    @objc
    private func publishFinish(_ notification: Notification) {
        guard !publishFinishState else { return }
        guard isPublishFinishTimeLimit(key: kMMKVPublishFinishLimitedKey) else { return }
        guard let userInfo = notification.userInfo, let source = userInfo["source"] as? String,
            source == "AI_Home_Assistant"
        else { return }
        publishFinishState = true
        resetPublishFinishTimeLimit()
    }

    private func isPublishFinishTimeLimit(key: String) -> Bool {
        let time = MMKV.default()?.double(forKey: key) ?? 0
        guard time > 0 else { return true }
        let date = Date(timeIntervalSince1970: time)
        let now = Date()
        guard now.timeIntervalSince1970 > time else { return false }
        return !Calendar.current.isDate(date, inSameDayAs: now)
    }

    private func resetPublishFinishTimeLimit() {
        let time = Date().timeIntervalSince1970
        MMKV.default()?.set(time, forKey: kMMKVPublishFinishLimitedKey)
    }

    private var kMMKVPublishFinishLimitedKey: String {
        "com.acc.publish.ai.limit.enable" + SOUserInfoManager.sharedInstance().userIdEcpt
    }
}
