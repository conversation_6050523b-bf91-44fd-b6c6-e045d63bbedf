//
//  SOPersonalHomePagePublishAICellComponentPublish.swift
//  ACC
//
//  Created by al<PERSON> on 2025/7/18.
//

import UIKit
import SnapKit
import SoulUIKitExtended

/// Main AI publish component for personal homepage
/// Combines header and content sections with fold/unfold functionality
class SOPersonalHomePagePublishAICellComponentPublish: UIView {
    
    /// Callback to reload the cell content
    @objc
    public var reloadActionHandler: (() -> Void)?
    
    /// Callback to close the AI publish component
    @objc
    public var closeActionHandler: (() -> Void)?
    
    // MARK: - Life Circle
    
    init() {
        super.init(frame: .zero)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    // MARK: - Data
    
    /// Updates the component with new AI publish data
    /// - Parameter data: The AI publish data containing header and content information
    func update(data: SOPersonalHomePagePublishAIData) {
        updateUI(data: data)
        updateUIConstraints(data: data)
    }
    
    // MARK: - UI
    
    /// Sets up the initial UI components and callback handlers
    private func setupUI() {
        addSubview(container)
        container.addSubview(stackView)
        stackView.addArrangedSubview(headerView)
        stackView.addArrangedSubview(publishView)
        
        headerView.reloadActionHandler = { [weak self] in
            self?.reloadActionHandler?()
        }
        headerView.closeActionHandler = { [weak self] in
            self?.closeActionHandler?()
        }
    }
    
    /// Sets up the UI constraints for all components
    private func setupUIConstraints() {
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        stackView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalTo(8)
            make.bottom.equalTo(-8)
        }
    }
    
    /// Updates the UI with the provided AI publish data
    /// Controls visibility of components based on data state
    /// - Parameter data: The AI publish data
    private func updateUI(data: SOPersonalHomePagePublishAIData) {
        headerView.update(data: data)
        publishView.update(data: data)
        if data.publishDataEnable {
            stackView.isHidden = false
            publishView.isHidden = data.isFold
        } else {
            stackView.isHidden = true
            publishView.isHidden = true
        }
    }
    
    /// Updates UI constraints based on the provided data
    /// - Parameter data: The AI publish data
    private func updateUIConstraints(data: SOPersonalHomePagePublishAIData) {
        
    }
    
    // MARK: - UI Components
    
    /// Main container view for all content
    private let container = UIView()
    
    /// Stack view to arrange header and content vertically
    private let stackView = UIStackView().then {
        $0.axis = .vertical
        $0.spacing = 0
        $0.alignment = .fill
        $0.distribution = .fillProportionally
    }
    
    /// Header component containing score and fold/close buttons
    private let headerView = SOPersonalHomePagePublishAICellComponentPublishHeader()
    
    /// Content component containing AI publish suggestions
    private let publishView = SOPersonalHomePagePublishAICellComponentPublishContent().then {
        $0.isHidden = true
    }
}
