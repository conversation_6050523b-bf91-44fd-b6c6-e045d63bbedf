//
//  SAFollowNumView.h
//  Soul_New
//
//  Created by 尚书威 on 2020/3/31.
//  Copyright © 2020 Soul. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "SOSoulersMeetView.h"
#import <SoulUIKit/SOCountingLabel.h>

NS_ASSUME_NONNULL_BEGIN

@interface SAFollowNumView : UIView

@property(nonatomic, copy) void (^tapViewBlock)(void);

@property(nonatomic, weak) SOSoulersMeetView *meetView;
@property(nonatomic, strong) SOCountingLabel *countingView;

@property(nonatomic, strong) UILabel *numLabel;
@property(nonatomic, strong) UILabel *desLabel;
@property(nonatomic, strong) UILabel *addNumLabel;

- (void)playHistoryLikeAnimation:(SAHomePageMetricsModel *)meetModel;
- (void)playLikeAnimation:(SAHomePageMetricsModel *)meetModel;
- (void)playMeetViewAnimation:(SAHomePageMetricsModel *)meetModel;

@property (nonatomic, assign) BOOL shouldPlayMeetingAnimation;

@end

NS_ASSUME_NONNULL_END
