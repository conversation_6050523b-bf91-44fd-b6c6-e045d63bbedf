//
//  SOPersonalHomePagePublishAICellComponentPublishCard.swift
//  ACC
//
//  Created by al<PERSON> on 2025/7/16.
//

import UIKit
import SnapKit
import SoulUIKitExtended

/// Card component for displaying AI-generated publishing suggestions
/// Shows title, subtitle, and image with highlighted text support
class SOPersonalHomePagePublishAICellComponentPublishCard: UIView {
    
    // MARK: - Life Circle
    
    init() {
        super.init(frame: .zero)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    // MARK: - Data
    
    /// Updates the card with new AI publish data
    /// - Parameter data: The AI publish data containing title, subtitle, and image
    func update(data: SOPersonalHomePagePublishAIPublishData) {
        updateUI(data: data)
        updateUIConstraints(data: data)
    }
    
    // MARK: - UI
    
    /// Sets up the initial UI components with shadow and styling
    private func setupUI() {
        addSubview(container)
        container.addSubview(containerBackgroundView)
        container.addSubview(imageView)
        container.addSubview(titleLabel)
        container.addSubview(titleIcon)
        container.addSubview(subTitleLabel)
    }
    
    /// Sets up the UI constraints for all components
    private func setupUIConstraints() {
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        containerBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        imageView.snp.makeConstraints { make in
            make.leading.equalTo(8)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(68)
        }
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(9)
            make.leading.equalTo(imageView.snp.trailing).offset(12)
            make.trailing.equalTo(titleIcon.snp.leading).offset(-2)
            make.height.equalTo(14)
        }
        titleIcon.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.trailing.lessThanOrEqualTo(-16)
            make.width.height.equalTo(10)
        }
        subTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(6)
            make.leading.equalTo(titleLabel)
            make.trailing.equalTo(-16)
            make.height.equalTo(44)
        }
    }
    
    /// Updates the UI with the provided AI publish data
    /// - Parameter data: The AI publish data containing content to display
    private func updateUI(data: SOPersonalHomePagePublishAIPublishData) {
        if let title = data.title {
            titleLabel.attributedText = makeHighlightedAttributedString(title)
        }
        subTitleLabel.text = data.subTitle
        if let imageURL = data.imageURL, let url = URL(string: imageURL) {
            imageView.so_setImage(with: url)
        }
    }
    
    /// Updates UI constraints based on the provided data
    /// - Parameter data: The AI publish data
    private func updateUIConstraints(data: SOPersonalHomePagePublishAIPublishData) {
        
    }
    
    // MARK: - UI Components
    
    /// Main container view with shadow and corner radius
    private let container = UIView().then {
        $0.backgroundColor = .soul.color(0)
        $0.layer.cornerRadius = 12
    }
    
    /// Background image view for the card
    private let containerBackgroundView = UIImageView().then {
        $0.contentMode = .scaleToFill
        $0.isUserInteractionEnabled = true
        $0.layer.cornerRadius = 12
        $0.layer.masksToBounds = true
        $0.image = .acc_imageNamed("acc_publish_ai_publish_card_background")
    }
    
    /// Image view for displaying AI suggestion image
    private let imageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
        $0.layer.cornerRadius = 6
        $0.layer.masksToBounds = true
    }
    
    /// Label for displaying the AI suggestion title with highlight support
    private let titleLabel = UILabel().then {
        $0.font = .soul.PingFangSC(size: 15, type: .medium)
        $0.textColor = .soul.color(6)
    }
    
    /// Icon displayed next to the title
    private let titleIcon = UIImageView().then {
        $0.image = .acc_imageNamed("acc_publish_ai_publish_title_icon")
        $0.contentMode = .scaleAspectFit
    }
    
    /// Label for displaying the AI suggestion subtitle
    private let subTitleLabel = UILabel().then {
        $0.font = .soul.PingFangSC(size: 14)
        $0.textColor = .soul.color(3)
        $0.numberOfLines = 2
    }
}

// MARK: - Text Processing Extensions

extension SOPersonalHomePagePublishAICellComponentPublishCard {
    
    /// Creates attributed string with highlighted text from raw string containing highlight tags
    /// - Parameter rawString: The raw string containing <highlight> tags
    /// - Returns: Attributed string with highlighted portions
    private func makeHighlightedAttributedString(_ rawString: String) -> NSMutableAttributedString {
        let highlightAttributedString = NSMutableAttributedString()
        let pattern = "(?:<highlight>([^<]+)</highlight>)|([^<]+)"
        let regex = try! NSRegularExpression(pattern: pattern, options: [])
        let matches = regex.matches(in: rawString, options: [], range: NSRange(rawString.startIndex..., in: rawString))
        for match in matches {
            if let highlightRange = Range(match.range(at: 1), in: rawString) {
                let highlightText = String(rawString[highlightRange])
                let highlightAttr = NSAttributedString(string: highlightText, attributes: [
                    .foregroundColor: UIColor.soul.color(11),
                    .font: UIFont.soul.PingFangSC(size: 12, type: .medium)
                ])
                highlightAttributedString.append(highlightAttr)
            } else if let textRange = Range(match.range(at: 2), in: rawString) {
                let normalText = String(rawString[textRange])
                let normalAttr = NSAttributedString(string: normalText, attributes: [
                    .foregroundColor: UIColor.soul.color(6),
                    .font: UIFont.soul.PingFangSC(size: 12, type: .medium)
                ])
                highlightAttributedString.append(normalAttr)
            }
        }
        return highlightAttributedString
    }
}
