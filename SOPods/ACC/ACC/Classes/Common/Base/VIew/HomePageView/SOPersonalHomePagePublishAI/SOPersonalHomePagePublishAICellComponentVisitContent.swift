//
//  SOPersonalHomePagePublishAICellComponentVisitContent.swift
//  ACC
//
//  Created by al<PERSON> on 2025/7/17.
//

import UIKit
import SnapKit
import SoulUIKitExtended

/// Content component for displaying AI visit suggestions
/// Shows score information and title with highlighted text support
class SOPersonalHomePagePublishAICellComponentVisitContent: UIView {
    
    // MARK: - Life Circle
    
    init() {
        super.init(frame: .zero)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    // MARK: - Data
    
    /// Updates the component with new AI visit data
    /// - Parameter data: The AI visit data containing score and title information
    func update(data: SOPersonalHomePagePublishAIData) {
        updateUI(data: data)
        updateUIConstraints(data: data)
    }
    
    // MARK: - UI
    
    /// Sets up the initial UI components
    private func setupUI() {
        addSubview(container)
        container.addSubview(scoreView)
        container.addSubview(titleIconView)
        container.addSubview(titleLabel)
    }
    
    /// Sets up the UI constraints for all components
    private func setupUIConstraints() {
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        scoreView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalTo(8)
        }
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.trailing.equalTo(0)
            make.top.equalTo(scoreView.snp.bottom).offset(18)
        }
        titleIconView.snp.makeConstraints { make in
            make.leading.top.equalTo(titleLabel)
        }
    }
    
    /// Updates the UI with the provided AI visit data
    /// - Parameter data: The AI visit data
    private func updateUI(data: SOPersonalHomePagePublishAIData) {
        scoreView.update(data: data, iconHidden: true)
        guard let title = data.visitData?.title else { return }
        titleLabel.attributedText = makeHighlightedAttributedString(title)
    }
    
    /// Updates UI constraints based on the provided data
    /// - Parameter data: The AI visit data
    private func updateUIConstraints(data: SOPersonalHomePagePublishAIData) {
        
    }
    
    // MARK: - UI Components
    
    /// Main container view for all content
    private let container = UIView()
    
    /// Score view component for displaying AI score information
    private let scoreView = SOPersonalHomePagePublishAICellComponentScore()
    
    /// Label for displaying the visit title with highlight support
    private let titleLabel = UILabel().then {
        $0.numberOfLines = 2
    }
    
    /// Icon displayed next to the title
    private let titleIconView = UIImageView(image: .acc_imageNamed("acc_publish_ai_visit_content_icon"))
}

// MARK: - Text Processing Extensions

extension SOPersonalHomePagePublishAICellComponentVisitContent {
    
    /// Creates attributed string with highlighted text from raw string containing highlight tags
    /// - Parameter rawString: The raw string containing <highlight> tags
    /// - Returns: Attributed string with highlighted portions
    private func makeHighlightedAttributedString(_ rawString: String) -> NSMutableAttributedString {
        let highlightAttributedString = NSMutableAttributedString()
        
        let pattern = "(?:<highlight>([^<]+)</highlight>)|([^<]+)"
        let regex = try! NSRegularExpression(pattern: pattern, options: [])
        let matches = regex.matches(in: rawString, options: [], range: NSRange(rawString.startIndex..., in: rawString))
        
        for match in matches {
            if let highlightRange = Range(match.range(at: 1), in: rawString) {
                let highlightText = String(rawString[highlightRange])
                let highlightAttr = NSAttributedString(string: highlightText, attributes: [
                    .foregroundColor: UIColor.soul.color(3),
                    .font: UIFont.soul.PingFangSC(size: 13, type: .medium)
                ])
                highlightAttributedString.append(highlightAttr)
            } else if let textRange = Range(match.range(at: 2), in: rawString) {
                let normalText = String(rawString[textRange])
                let normalAttr = NSAttributedString(string: normalText, attributes: [
                    .foregroundColor: UIColor.soul.color(3),
                    .font: UIFont.soul.PingFangSC(size: 13, type: .regular)
                ])
                highlightAttributedString.append(normalAttr)
            }
        }
        return highlightAttributedString
    }
}
