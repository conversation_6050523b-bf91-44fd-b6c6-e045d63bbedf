//
//  SAPersonalFocusCell.m
//  Soul_New
//
//  Created by 尚书威 on 2020/3/30.
//  Copyright © 2020 Soul. All rights reserved.
//

#import "SAHomePersonalFollowInfoView.h"
#import "SAFollowNumView.h"
#import "SOPersonalHomePageCellLayout.h"
#import <YYCategories/YYCategories.h>
#import <ACC/ACC-Swift.h>
#import "SOFeelingMeetBannerData.h"

@interface SAHomePersonalFollowInfoView ()
@property(nonatomic, strong) SAFollowNumView *followView;//关注
@property(nonatomic, strong) SAFollowNumView *followedView;//被关注
@property(nonatomic, strong) SAFollowNumView *meetingView;//看过我
@property(nonatomic, strong) SAFollowNumView *giftView;//送礼
@property(nonatomic, strong) SAFollowNumView *likePostView;//帖子点赞View
@property(nonatomic, strong) SoulHaoWuHeaderView *haowuView;
@property(nonatomic, strong) SOUserSchoolInfoView *schoolInfoView;

@property(nonatomic, strong) UIView *followStackSuperView;
@property(nonatomic, strong) UIView *line1;
@property(nonatomic, strong) UIView *line2;
@property(nonatomic, strong) UIStackView *mainView;
@property(nonatomic, strong) UIStackView *schollStackView;
@property(nonatomic, strong) SOMeetEntryView *meetEntryView;
@end

@implementation SAHomePersonalFollowInfoView

- (instancetype)initWithViewType:(SAHomePersonalFollowInfoViewType)type ownerType:(SAHomePersonalFollowInfoViewOwnerType)ownerType {
    if (self = [super init]) {
        _viewType = type;
        _ownerType = ownerType;
        [self initUI];
    }
    return self;
}

- (NSString *)getNumString:(NSInteger)num {
    NSString *numString = @"";
    if (num > 999900) {
        numString = @"999.9k+";
    } else if (num > 99999) {
        id <SoulUtilsProtocol> utils = [ZIKRouterToService(SoulUtilsProtocol) makeDestination];
        numString = [utils tranNumToKiloString:num];
    } else {
        numString = [NSString stringWithFormat:@"%d", (int) num];
    }
    return numString;
}

- (void)setMeetBannerModel:(SoulOfficialTouchPositionModel *)meetBannerModel {
    _meetBannerModel = meetBannerModel;
    if (_meetBannerModel && _meetBannerModel.bizDataMap) {
        self.meetEntryView.hidden = NO;
        SOFeelingMeetBannerData* bannerData = [SOFeelingMeetBannerData yy_modelWithJSON: _meetBannerModel.bizDataMap];
        bannerData.showImage = _meetBannerModel.showImage;
        [self.meetEntryView updateWithData:bannerData];
    }else {
        self.meetEntryView.hidden = YES;
    }
}

- (void)setMeetingLimitModel:(SoulOfficialTouchPositionModel *)meetingLimitModel {
    _meetingLimitModel = meetingLimitModel;
    self.meetingView.shouldPlayMeetingAnimation = (meetingLimitModel != nil);
}

- (void)setFollowInfoModel:(SAHomePageMetricsModel *)followInfoModel {
    _followInfoModel = followInfoModel;
    self.followView.numLabel.text = [self getNumString:followInfoModel.followNum];
    self.followedView.numLabel.text = [self getNumString:followInfoModel.beFollowNum];
    
    //添加VoiceOver支持
    [self.followView so_setAccessibilityLabel:@"关注列表" value:self.followView.numLabel.text hint:@"" trait:UIAccessibilityTraitButton];
    
    //添加VoiceOver支持
    [self.followedView so_setAccessibilityLabel:@"被关注列表" value:self.followedView.numLabel.text hint:@"" trait:UIAccessibilityTraitButton];
    
    if (_ownerType == SAHomePersonalFollowInfoViewMine) {
        if (followInfoModel.brandUser) {
            self.meetingView.hidden = YES;
            self.likePostView.hidden = NO;
            self.likePostView.numLabel.text = [self getNumString:followInfoModel.postLikeNum];
        } else {
            self.meetingView.hidden = NO;
            self.likePostView.hidden = YES;
            if (self.followInfoModel.homePageLikedMetric.hasShowHistoryDynamic) {
                self.meetingView.countingView.hidden = NO;
                self.meetingView.desLabel.text = @"赞过我";
                NSString *numLabelString = [self getNumString:self.followInfoModel.homePageLikedMetric.likedTotalNum];
                CGFloat numWidth = [numLabelString widthForFont:[UIFont fontWithName:[SOFontDefine fontBoldName] size:20]];
                self.meetingView.numLabel.text = numLabelString;
                [self.meetingView.numLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.width.equalTo(@(numWidth + 0.5));
                }];
                
                [self.meetingView playHistoryLikeAnimation:self.followInfoModel];
                
                //添加VoiceOver支持
                [self.meetingView so_setAccessibilityLabel:@"赞过我" value:[NSString stringWithFormat:@"%@人",numLabelString] hint:@"" trait:UIAccessibilityTraitButton];
                
            }else if (self.followInfoModel.homePageLikedMetric.addNum > 0) {
                self.meetingView.countingView.hidden = NO;
                self.meetingView.desLabel.text = @"赞过我";
                NSString *numLabelString = [self getNumString:self.followInfoModel.homePageLikedMetric.likedTotalNum];
                CGFloat numWidth = [numLabelString widthForFont:[UIFont fontWithName:[SOFontDefine fontBoldName] size:20]];
                self.meetingView.numLabel.text = numLabelString;
                [self.meetingView.numLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.width.equalTo(@(numWidth + 0.5));
                }];
                
                [self.meetingView playLikeAnimation:self.followInfoModel];
                
                //添加VoiceOver支持
                [self.meetingView so_setAccessibilityLabel:@"赞过我" value:[NSString stringWithFormat:@"%@人",numLabelString] hint:@"" trait:UIAccessibilityTraitButton];
            }else {
                self.meetingView.desLabel.text = @"看过我";
                NSString *numLabelString = [self getNumString:followInfoModel.beViewNum];
                CGFloat numWidth = [numLabelString widthForFont:[UIFont fontWithName:[SOFontDefine fontBoldName] size:20]];
                self.meetingView.numLabel.text = numLabelString;
                [self.meetingView.numLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.width.equalTo(@(numWidth + 0.5));
                }];
                
                if (self.followInfoModel.recentViewUser.count > 0) {
                    if (self.followInfoModel.recentViewNum > 0) {
                        self.meetingView.countingView.hidden = NO;

                        [self.meetingView playMeetViewAnimation:self.followInfoModel];
                    } else {
                        self.meetingView.meetView.hidden = YES;
                        self.meetingView.countingView.hidden = YES;
                    }
                }else {
                    self.meetingView.meetView.hidden = YES;
                    self.meetingView.countingView.hidden = YES;
                    if (followInfoModel.recentViewNum > 0) {
                        self.meetingView.addNumLabel.hidden = NO;
                        if (followInfoModel.recentViewNum > 9999) {
                            self.meetingView.addNumLabel.text = @"+9999";
                        } else {
                            self.meetingView.addNumLabel.text = [NSString stringWithFormat:@"+%@", [NSString stringWithFormat:@"%d", (int) followInfoModel.recentViewNum]];
                        }
                    } else {
                        self.meetingView.addNumLabel.hidden = YES;
                        self.meetingView.addNumLabel.text = nil;
                    }
                }
                //添加VoiceOver支持
                [self.meetingView so_setAccessibilityLabel:@"看过我" value:[NSString stringWithFormat:@"%@人",numLabelString] hint:@"" trait:UIAccessibilityTraitButton];
            }
        }
    } else {
        self.meetingView.hidden = YES;
        if (followInfoModel.brandUser) {
            self.giftView.hidden = YES;
            self.likePostView.hidden = NO;
            self.likePostView.numLabel.text = [self getNumString:followInfoModel.postLikeNum];
        } else {
            self.giftView.hidden = NO;
            self.likePostView.hidden = YES;
            self.giftView.numLabel.text = [self getNumString:followInfoModel.giftValue];
        }

        if (followInfoModel.metricSwitchValue == 2) {
            self.followView.numLabel.textColor = GET_COLOR(18);
            self.followedView.numLabel.textColor = GET_COLOR(18);
            self.followView.desLabel.textColor = GET_COLOR(18);
            self.followedView.desLabel.textColor = GET_COLOR(18);
            self.likePostView.desLabel.textColor = GET_COLOR(18);
        } else {
            self.followView.numLabel.textColor = GET_COLOR(2);
            self.followedView.numLabel.textColor = GET_COLOR(2);
            self.followView.desLabel.textColor = GET_COLOR(6);
            self.followedView.desLabel.textColor = GET_COLOR(6);
            self.likePostView.desLabel.textColor = GET_COLOR(6);
        }
    }
}

- (void)setViewType:(SAHomePersonalFollowInfoViewType)viewType {
    _viewType = viewType;
    if (viewType == SAHomePersonalFollowInfoViewNone) {
        [self.mainView.arrangedSubviews enumerateObjectsUsingBlock:^(__kindof UIView *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
            obj.hidden = YES;
        }];
    } else {
        [self.mainView.arrangedSubviews enumerateObjectsUsingBlock:^(__kindof UIView *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
            obj.hidden = NO;
        }];
    }

    if (viewType == SAHomePersonalFollowInfoViewFocus) {
        self.followStackSuperView.hidden = NO;
        self.soulMateView.superview.hidden = YES;
    } else if (viewType == SAHomePersonalFollowInfoViewSoulMate) {
        self.followStackSuperView.hidden = YES;
        self.soulMateView.superview.hidden = NO;
    } else if (viewType == SAHomePersonalFollowInfoViewFocusSoulMate) {
        self.followStackSuperView.hidden = NO;
        self.soulMateView.superview.hidden = NO;
    }
    
    [self setMeetBannerModel:self.meetBannerModel];
}

- (void)initUI {
    self.backgroundColor = GET_COLOR(0);
    self.clipsToBounds = NO;

    UIView *soulMateSuperView = [UIView new];
    [soulMateSuperView addSubview:self.soulMateView];
    soulMateSuperView.hidden = YES;
    [self.soulMateView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.height.mas_equalTo(@46);
        make.top.mas_equalTo(16);
        make.bottom.mas_equalTo(-8);
    }];

    UIStackView *followStackView = [[UIStackView alloc] initWithArrangedSubviews:@[self.followView, self.followedView, self.likePostView, self.giftView, self.meetingView]];
    followStackView.axis = UILayoutConstraintAxisHorizontal;
    followStackView.distribution = UIStackViewDistributionFillEqually;

    UIView *followStackSuperView = [UIView new];
    [followStackSuperView addSubview:followStackView];
    [followStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(20);
        make.right.mas_equalTo(-20);
        make.top.bottom.mas_equalTo(0);
        make.height.mas_equalTo(63);
    }];

    self.schollStackView = [[UIStackView alloc] initWithArrangedSubviews:@[self.schoolInfoView]];

    UIStackView *stackView = [[UIStackView alloc] initWithArrangedSubviews:@[followStackSuperView, self.meetEntryView, self.haowuView, self.schollStackView, soulMateSuperView]];
    stackView.axis = UILayoutConstraintAxisVertical;
    [self addSubview:stackView];

    self.followStackSuperView = followStackSuperView;

    [self.schoolInfoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(8);
        make.height.mas_equalTo(0);
    }];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(0).priority(900);
        make.top.bottom.left.right.mas_equalTo(0);
    }];

    [self.meetEntryView.superview bringSubviewToFront:self.meetEntryView];
    self.mainView = stackView;

    [self addSubview:self.line1];
    [self addSubview:self.line2];

    self.meetingView.hidden = YES;
    self.giftView.hidden = YES;

    [self.line1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.followView);
        make.width.equalTo(@1);
        make.height.equalTo(@16);
        make.left.equalTo(self.followView.mas_right).offset(-0.5);
    }];
    [self.line2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.followView);
        make.width.equalTo(@1);
        make.height.equalTo(@16);
        make.left.equalTo(self.followedView.mas_right).offset(-0.5);
    }];

    if (self.ownerType == SAHomePersonalFollowInfoViewOther) {
        self.soulMateView.titleLabel.text = @"TA的Soulmate空间";
        
        //添加VoiceOver支持
        [self.soulMateView so_setAccessibilityLabel:self.soulMateView.titleLabel.text trait:UIAccessibilityTraitButton];
    }
    [self.mainView.arrangedSubviews enumerateObjectsUsingBlock:^(__kindof UIView *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
        obj.hidden = YES;
    }];
}

- (void)setInfo:(BrandInfo *)info {
    _info = info;
    BOOL hiddenInfo = (info == nil || (info.showBrandTags == NO && info.showBrandGoods == NO && info.activityList.count == 0));
    self.haowuView.hidden = hiddenInfo;
    self.haowuView.info = info;
}

- (void)setSchoolInfoModel:(SOUserSchoolInfoModel *)schoolInfoModel {
    _schoolInfoModel = schoolInfoModel;
    BOOL hasSchoolInfo = (schoolInfoModel.schoolName.length > 0);
    self.schollStackView.hidden = !hasSchoolInfo;
    self.schoolInfoView.hidden = !hasSchoolInfo;
    CGFloat schoolInfoHeight = 32;
    BOOL hasSoulMate = !self.soulMateView.superview.hidden;
    if (!hasSoulMate) {
        schoolInfoHeight = 40;
    }
    BOOL followViewVisibel = !self.followStackSuperView.hidden;
    [self.schoolInfoView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo((followViewVisibel ? 8 : 16));
        make.height.mas_equalTo((hasSchoolInfo ? schoolInfoHeight : 0));
    }];
    self.schoolInfoView.schoolInfoModel = schoolInfoModel;
}

- (SAFollowNumView *)followView {
    if (!_followView) {
        _followView = [[SAFollowNumView alloc] init];
        _followView.desLabel.text = @"关注";
        SOWeakIfy(self);
        _followView.tapViewBlock = ^{
            SOStrongIfy(self);
            if (self.clickItemView) {
                self.clickItemView(FollowInfoViewClickFollow);
            }
        };
        
        //添加VoiceOver支持
        [_followView so_setAccessibilityLabel:@"关注列表" trait:UIAccessibilityTraitButton];
    }
    return _followView;
}

- (SAFollowNumView *)followedView {
    if (!_followedView) {
        _followedView = [[SAFollowNumView alloc] init];
        _followedView.desLabel.text = @"被关注";
        SOWeakIfy(self);
        _followedView.tapViewBlock = ^{
            SOStrongIfy(self);
            if (self.clickItemView) {
                self.clickItemView(FollowInfoViewClickFollowed);
            }
        };
        
        //添加VoiceOver支持
        [_followedView so_setAccessibilityLabel:@"被关注列表" trait:UIAccessibilityTraitButton];
    }
    return _followedView;
}

- (SAFollowNumView *)meetingView {
    if (!_meetingView) {
        _meetingView = [[SAFollowNumView alloc] init];
        _meetingView.desLabel.text = @"看过我";
        SOWeakIfy(self);
        _meetingView.tapViewBlock = ^{
            SOStrongIfy(self);
            self.meetEntryView.hidden = YES;
            if (self.layoutUpdated) {
                self.layoutUpdated();
            }
            if (self.clickItemView) {
                self.clickItemView(FollowInfoViewClickMeeting);
            }
        };
        
        //添加VoiceOver支持
        [_meetingView so_setAccessibilityLabel:@"谁看过我" trait:UIAccessibilityTraitButton];
    }
    return _meetingView;
}

- (SAFollowNumView *)likePostView {
    if (!_likePostView) {
        _likePostView = [[SAFollowNumView alloc] init];
        _likePostView.desLabel.text = @"点赞";
        
        //添加VoiceOver支持
        [_likePostView so_setAccessibilityLabel:@"点赞" trait:UIAccessibilityTraitButton];
    }
    return _likePostView;
}

- (SAFollowNumView *)giftView {
    if (!_giftView) {
        _giftView = [[SAFollowNumView alloc] init];
        _giftView.desLabel.text = @"礼物";
        SOWeakIfy(self);
        _giftView.tapViewBlock = ^{
            SOStrongIfy(self);
            if (self.clickItemView) {
                self.clickItemView(FollowInfoViewClickGift);
            }
        };
        
        //添加VoiceOver支持
        [_giftView so_setAccessibilityLabel:@"礼物" trait:UIAccessibilityTraitButton];
    }
    return _giftView;
}

- (UIView *)line1 {
    if (!_line1) {
        _line1 = [[UIView alloc] init];
        _line1.backgroundColor = GET_COLOR(4);
    }
    return _line1;
}

- (UIView *)line2 {
    if (!_line2) {
        _line2 = [[UIView alloc] init];
        _line2.backgroundColor = GET_COLOR(4);
    }
    return _line2;
}

- (SAHomeSoulMateView *)soulMateView {
    if (!_soulMateView) {
        _soulMateView = [[SAHomeSoulMateView alloc] init];
        SOWeakIfy(self);
        _soulMateView.clickSoulMateBlock = ^{
            SOStrongIfy(self);
            if (self.clickSoulMate) {
                self.clickSoulMate();
            }
        };
    }
    return _soulMateView;
}

- (BOOL)isShowGuide {
    _isShowGuide = _isShowGuide?:0; //添加原因：解决Warn most问题需要补充下划线参数赋值，但同时期望保持业务逻辑无变化。
    
    if ((_viewType == SAHomePersonalFollowInfoViewNone) ||
            (_viewType == SAHomePersonalFollowInfoViewFocus) ||
            (_viewType == SAHomePersonalFollowInfoViewSoulMate) ||
            (_viewType == SAHomePersonalFollowInfoViewFocusSoulMate)) {
        return NO;
    }
    return YES;
}

- (SoulHaoWuHeaderView *)haowuView {
    if (_haowuView == nil) {
        _haowuView = [[SoulHaoWuHeaderView alloc] init];
        SOWeakIfy(self);
        _haowuView.didSelected = ^(id _Nonnull item) {
            SOStrongIfy(self);
            if (self.haowuDidSelected) {
                self.haowuDidSelected(item);
            }
        };
    }
    return _haowuView;
}

- (SOUserSchoolInfoView *)schoolInfoView {
    if (!_schoolInfoView) {
        _schoolInfoView = [[SOUserSchoolInfoView alloc] init];
        _schoolInfoView.hidden = YES;
        _schoolInfoView.isSelfHomePage = (self.ownerType == SAHomePersonalFollowInfoViewMine);
    }
    return _schoolInfoView;
}

- (SOMeetEntryView *)meetEntryView {
    if (!_meetEntryView) {
        SOWeakIfy(self);
        _meetEntryView = [[SOMeetEntryView alloc]initOnTap:^(SOMeetEntryView * _Nonnull view) {
            SOStrongIfy(self);
            if (self.layoutUpdated) {
                self.layoutUpdated();
            }
            if (self.clickItemView) {
                self.clickItemView(FollowInfoViewClickMeeting);
            }
            if (self.meetBannerModel) {
                /* 触达中台策略-通用位置点击点击埋点 */
                [SoulEvent eventClick:@"Reach_Strategy_Position_Click"
                               params:@{@"positionDetailCode": self.meetBannerModel.positionDetailCode ?: @"", @"reach_strategy_id": self.meetBannerModel.ID ?: @""}
                               pageId:nil pagePrama:nil];
            }
        } onActionTap:^(SOMeetEntryView * _Nonnull view) {
            SOStrongIfy(self);
            if (self.layoutUpdated) {
                self.layoutUpdated();
            }
            if (self.clickItemView) {
                self.clickItemView(FollowInfoViewClickMeeting);
            }
            if (self.meetBannerModel) {
                /* 触达中台策略-通用位置点击点击埋点 */
                [SoulEvent eventClick:@"Reach_Strategy_Position_Click"
                               params:@{@"positionDetailCode": self.meetBannerModel.positionDetailCode ?: @"", @"reach_strategy_id": self.meetBannerModel.ID ?: @""}
                               pageId:nil pagePrama:nil];
            }
        } onClose:^(SOMeetEntryView * _Nonnull view) {
            SOStrongIfy(self);
            view.hidden = YES;
            if (self.layoutUpdated) {
                self.layoutUpdated();
            }
            if (self.meetBannerModel) {
                /* 触达中台策略-通用位置点击点击埋点 */
                [SoulEvent eventClick:@"Reach_Guide_close_click"
                               params:@{@"positionDetailCode": self.meetBannerModel.positionDetailCode ?: @"", @"reach_strategy_id": self.meetBannerModel.ID ?: @""}
                               pageId:nil pagePrama:nil];
            }
            self.meetBannerModel = nil;
        }];
    }
    return _meetEntryView;
}

@end
