//
//  SOHomePostCellBottomView.h
//  ACC
//
//  Created by 11080237 on 2022/8/18.
//
#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import "SAPersonalHomePageCellToolBarView.h"
#import "SOHomePostCellBottomViewLayout.h"

NS_ASSUME_NONNULL_BEGIN

@protocol SOHomePostCellShowRecommondChatProtocol;
@interface SOHomePostCellBottomView : UIView <SOCommonViewProtocol, SOHomePostCellShowRecommondChatProtocol>

@property (nonatomic, strong) SOHomePostCellBottomViewLayout *layout;

@property (nonatomic, weak) id<SAPersonalHomePageCellToolBarViewDelegate> delegate;

@property (nonatomic, strong) SAPersonalHomePageCellToolBarView *toolView;
@property (nonatomic, strong) SOPostGiftRecordInfoView *giftInfoView;//送礼信息
@property (nonatomic, strong) UIButton *recommdChatBtn;                 /// 聊聊这条

- (void)showRecommdChatBtnAnimation:(BOOL)animation;
- (void)dissRecommdChatBtnAnimation:(BOOL)animation;

@end

NS_ASSUME_NONNULL_END
