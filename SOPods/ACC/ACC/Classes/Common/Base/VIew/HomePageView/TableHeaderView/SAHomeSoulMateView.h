//
//  SAHomeSoulMateView.h
//  Soul_New
//
//  Created by 尚书威 on 2020/4/2.
//  Copyright © 2020 Soul. All rights reserved.
//

#import <UIKit/UIKit.h>

@class SOSoulModel;

typedef enum : NSUInteger {
    SAHomeSoulMateView_OldHomePage,       /// 旧版个人主页 defalt
    SAHomeSoulMateView_MetaHomePage,      /// meta个人主页
} SAHomeSoulMateViewType;

NS_ASSUME_NONNULL_BEGIN

@interface SAHomeSoulMateView : UIView

@property(nonatomic, strong) SOSoulModel *soulMate;
@property(nonatomic, strong) NSDictionary *personalInfo;
@property(nonatomic, strong) UILabel *titleLabel;
@property(nonatomic, copy) void (^clickSoulMateBlock)(void);

@property (nonatomic, assign) SAHomeSoulMateViewType type;

- (instancetype)initWithType:(SAHomeSoulMateViewType)type;

@end

NS_ASSUME_NONNULL_END
