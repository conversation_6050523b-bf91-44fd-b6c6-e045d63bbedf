//
//  SOHomePostCellBottomViewLayout.h
//  ACC
//
//  Created by 11080237 on 2022/8/18.
//

#import <Foundation/Foundation.h>
#import <SoulBussinessKit/SoulBussinessKit.h>
#import <SoulCore/SoulCore.h>

NS_ASSUME_NONNULL_BEGIN

@interface SOHomePostCellBottomViewLayout : NSObject <SOCommonViewLayoutProtocol>

@property (nonatomic, assign) CGFloat height;
@property (nonatomic, assign) CGFloat topMargin;

@property (nonatomic, strong) SOPost *post;

@property (nonatomic, assign) CGRect itemFrame;

@property (nonatomic, strong) id<SOCommonViewLayoutProtocol> locationLayout;

@property (nonatomic, assign) CGRect giftViewFrame;

@property (nonatomic, assign) CGRect toolViewFrame;

@property (nonatomic, assign) BOOL isSoulmate;

@property (nonatomic, assign) BOOL showedRecommd;

- (NSString *)visibilityTitle;
- (NSString *)visibilityIconName;

@end

NS_ASSUME_NONNULL_END
