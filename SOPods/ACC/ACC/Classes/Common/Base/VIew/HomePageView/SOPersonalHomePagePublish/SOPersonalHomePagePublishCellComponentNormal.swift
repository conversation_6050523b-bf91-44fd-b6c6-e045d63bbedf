//
//  SOPersonalHomePagePublishCellComponentNormal.swift
//  ACC
//
//  Created by albert on 2024/12/27.
//

import UIKit
import SoulUIKitExtended
import SoulAssets
import SnapKit
import SOUGCBaseUI
import SDWebImage
import SoulEventBridge
import YYText
import SODarkMode
import SoulBussinessKit

/// A view component that displays publishing options in the personal homepage
class SOPersonalHomePagePublishCellComponentNormal: UIView {
    
    /// Callback handler when the cancel button is tapped
    var cancelActionHandler: (() -> Void)?
    /// Callback handler when the completion button is tapped
    var complationActionHandler: (() -> Void)?
    
    /// Stores the current data being displayed
    private var data: SOPersonalHomePagePublishData?
    
    // MARK: - Life Circle
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    /// Handles the cancel button tap action
    /// - Calls the cancel handler
    /// - Tracks the "Postguideclose_HomePage" event
    /// - Resets the limited time interval
    @objc
    private func cancelAction() {
        cancelActionHandler?()
        SoulEvent.eventClick("Postguideclose_HomePage", params: nil, pageId: nil, pagePrama: nil)
        SOPersonalHomePagePublishDataStore.resetLimitedTimeInterval()
    }
    
    /// Handles the completion button tap action
    /// - Calls the completion handler
    /// - Tracks different events based on the content type (socialCard or other)
    @objc
    private func complationAction() {
        complationActionHandler?()
        if let type = data?.styles?.first?.type, type == "socialCard" {
            SoulEvent.eventClick("SocialCard_clk", params: nil, pageId: nil, pagePrama: nil)
        } else {
            let params = [
                "tag": data?.styles?.first?.contents?.first?.title ?? "",
                "type": data?.styles?.first?.type ?? ""
            ]
            SoulEvent.eventClick("Postguide_HomePage_click", params: params, pageId: nil, pagePrama: nil)
        }
    }
    
    // MARK: - Data
    
    /// Updates the view with new data
    /// - Parameter data: The new data to display
    @objc
    public func update(data: SOPersonalHomePagePublishData) {
        self.data = data
        updateUI(data: data)
        updateUIConstraints(data: data)
    }
    
    // MARK: - UI
    
    /// Sets up the initial UI components and their properties
    private func setupUI() {
        backgroundImageView.contentMode = .scaleToFill
        backgroundImageView.isUserInteractionEnabled = true
        addSubview(backgroundImageView)
        backgroundIcon.isUserInteractionEnabled = true
        backgroundImageView.addSubview(backgroundIcon)
        
        titleIcon.contentMode = .scaleAspectFit
        backgroundImageView.addSubview(titleIcon)
        titleLabel.font = .soul.PingFangSC(size: 12)
        titleLabel.textColor = .soul.color(15)
        titleLabel.textAlignment = .left
        backgroundImageView.addSubview(titleLabel)
        
        contentIcon.contentMode = .scaleAspectFit
        backgroundImageView.addSubview(contentIcon)
        contentLabel.font = .soul.PingFangSC(size: 14, type: .medium)
        contentLabel.textColor = .soul.color(3)
        contentLabel.textAlignment = .left
        backgroundImageView.addSubview(contentLabel)
        
        cancelButton.setImage(.acc_imageNamed("acc_publish_guide_cell_cancel"), for: .normal)
        cancelButton.addTarget(self, action: #selector(cancelAction), for: .touchUpInside)
        backgroundImageView.addSubview(cancelButton)
        
        completeButton.backgroundColor = .soul.color(1)
        completeButton.layer.cornerRadius = 12
        completeButton.layer.masksToBounds = true
        completeButton.titleLabel?.font = .soul.PingFangSC(size: 13, type: .medium)
        completeButton.setTitleColor(.soul.color(0), for: .normal)
        completeButton.addTarget(self, action: #selector(complationAction), for: .touchUpInside)
        backgroundImageView.addSubview(completeButton)
    }
    
    /// Sets up the initial constraints for all UI components
    private func setupUIConstraints() {
        backgroundImageView.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.top.equalTo(6)
            make.trailing.equalTo(-16)
        }
        backgroundIcon.snp.makeConstraints { make in
            make.trailing.centerY.equalToSuperview()
            make.width.height.equalTo(76)
        }
        
        titleIcon.snp.makeConstraints { make in
            make.leading.equalTo(12)
            make.top.equalTo(8)
            make.height.width.equalTo(24)
        }
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleIcon.snp.trailing)
            make.centerY.height.equalTo(titleIcon)
            make.trailing.lessThanOrEqualTo(-38)
        }
        
        contentIcon.snp.makeConstraints { make in
            make.leading.equalTo(12)
            make.top.equalTo(titleIcon.snp.bottom).offset(6)
            make.bottom.equalTo(-6)
            make.height.width.equalTo(32)
        }
        contentLabel.snp.makeConstraints { make in
            make.leading.equalTo(contentIcon.snp.trailing)
            make.centerY.height.equalTo(contentIcon)
            make.trailing.lessThanOrEqualTo(completeButton.snp.leading).offset(-12)
        }
        
        cancelButton.snp.makeConstraints { make in
            make.top.equalTo(4)
            make.trailing.equalTo(-4)
            make.height.width.equalTo(30)
        }
        completeButton.snp.makeConstraints { make in
            make.trailing.equalTo(-12)
            make.centerY.equalTo(contentIcon)
            make.width.equalTo(60)
            make.height.equalTo(24)
        }
    }
    
    /// Updates the UI components based on the provided data
    /// - Parameter data: The data to use for updating the UI
    private func updateUI(data: SOPersonalHomePagePublishData) {
        guard let style = data.styles?.first, let content = style.contents?.first else { return }
        if let iconURL = style.titleIcon {
            titleIcon.so_setImage(with: URL(string: iconURL))
            titleLabel.text = " \(style.desc ?? "")"
        } else {
            titleIcon.image = nil
            titleLabel.text = style.desc
        }
        
        if style.type == "socialCard", let title = titleLabel.text, !title.isEmpty {
            /// Attributes
            let normalAttributes: [NSAttributedString.Key: Any] = [.font: titleLabel.font as Any, .foregroundColor: titleLabel.textColor as UIColor]
            let highlightedAttributes: [NSAttributedString.Key: Any] = [.font: titleLabel.font as Any, .foregroundColor: UIColor.soul.color(1)]
            /// Attributed String
            let attributedString = NSMutableAttributedString(string: title, attributes: normalAttributes)
            /// Highlight Attributed String
            let highlightText = "10+Souler"
            let range = (title as NSString).range(of: highlightText)
            if range.location != NSNotFound {
                attributedString.addAttributes(highlightedAttributes, range: range)
                titleLabel.attributedText = attributedString
            }
        }
        
        if let iconURL = content.iconUrl {
            contentIcon.so_setImage(with: URL(string: iconURL))
            contentLabel.text = "  \(content.title ?? "")"
        } else {
            contentIcon.image = nil
            contentLabel.text = "\(content.title ?? "")"
        }
        if style.type == "topic" {
            backgroundIcon.image = .acc_imageNamed("acc_publish_guide_cell_background_icon_tag")
        } else {
            backgroundIcon.image = .acc_imageNamed("acc_publish_guide_cell_background_icon_template")
        }
        completeButton.setTitle(content.button, for: .normal)
    }
    
    /// Updates the UI constraints based on the content type
    /// - Parameter data: The data containing style information
    /// - Note: Special handling for socialCard type with different dimensions and styling
    private func updateUIConstraints(data: SOPersonalHomePagePublishData) {
        guard let style = data.styles?.first, let content = style.contents?.first else { return }
        if style.type == "socialCard" {
            contentIcon.snp.updateConstraints { make in
                make.bottom.equalTo(-12)
                make.height.width.equalTo(48)
            }
            completeButton.snp.updateConstraints { make in
                make.height.equalTo(28)
            }
            completeButton.layer.cornerRadius = 14
            contentIcon.layer.cornerRadius = 6
            contentIcon.layer.masksToBounds = true
            contentIcon.contentMode = .scaleAspectFill
        } else {
            contentIcon.snp.updateConstraints { make in
                make.bottom.equalTo(-6)
                make.height.width.equalTo(32)
            }
            completeButton.snp.updateConstraints { make in
                make.height.equalTo(24)
            }
            completeButton.layer.cornerRadius = 12
            contentIcon.layer.cornerRadius = 0
            contentIcon.layer.masksToBounds = false
            contentIcon.contentMode = .scaleAspectFit
        }
    }
    
    /// Background image view for the cell
    private let backgroundImageView = UIImageView(image: .acc_imageNamed("acc_publish_guide_cell_background"))
    /// Icon displayed in the background
    private let backgroundIcon = UIImageView()
    /// Icon displayed next to the title
    private let titleIcon = UIImageView()
    /// Label displaying the title text
    private let titleLabel = UILabel()
    /// Icon displayed next to the content
    private let contentIcon = UIImageView()
    /// Label displaying the content text
    private let contentLabel = UILabel()
    
    /// Button to cancel/close the view
    private let cancelButton = UIButton()
    /// Button to complete/confirm the action
    private let completeButton = UIButton()
}
