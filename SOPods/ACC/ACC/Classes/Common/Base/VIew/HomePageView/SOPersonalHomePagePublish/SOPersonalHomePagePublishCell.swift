//
//  SOPersonalHomePagePublishCell.swift
//  ACC
//
//  Created by 1021500723 on 2024/1/3.
//

import UIKit
import SoulUIKitExtended
import SoulAssets
import SnapKit
import SOUGCBaseUI
import SDWebImage
import SoulEventBridge
import YYText
import SODarkMode
import SoulBussinessKit

/// Cell for displaying publishing options in the personal homepage
public class SOPersonalHomePagePublishCell: SOBaseCell {
    
    // MARK: - Callback Handlers
    
    /// Callback when user cancels the publishing action
    @objc
    public var cancelActionHandler: (() -> Void)?
    /// Callback to reload the cell content
    @objc
    public var reloadActionHandler: (() -> Void)?
    
    /// Callback for navigating to a post detail
    @objc
    public var navigatePostActionHandler: ((SOPost) -> Void)?
    /// Callback for navigating to a user profile
    @objc
    public var navigateUserActionHandler: ((SOPost) -> Void)?
    /// Callback for navigating to chat
    @objc
    public var navigateChatActionHandler: ((SOPost) -> Void)?
    
    // MARK: - Properties
    
    private var data: SOPersonalHomePagePublishData?
    
    // MARK: - Life Circle
    
    @objc
    public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    /// Handles the cancel button tap
    @objc
    private func cancelAction() {
        cancelActionHandler?()
    }
    
    /// Handles the completion button tap
    /// Records the click event and navigates to post creation
    @objc
    private func complationAction() {
        SOPersonalHomePagePublishActionManager.shared.recordPublishGuideClick()
        let urlString = data?.styles?.first?.contents?.first?.jumpUrl ?? "soul://ul.soulapp.cn/post/postMoment?pageFrom=homepageguide&flushCache=1"
        guard let publishRouterURL = URL(string: urlString) else { return }
        try? SoulRouterManager.sharedInstance().handleOpen(publishRouterURL, fromVc: nil, appParas: nil)
    }
    
    // MARK: - Data
    
    /// Updates the cell with new data
    /// - Parameter data: The publish data to display
    @objc
    public func update(data: SOPersonalHomePagePublishData) {
        self.data = data
        updateUI(data: data)
        updateUIConstraints(data: data)
    }
    
    // MARK: - UI
    
    /// Sets up the initial UI components
    private func setupUI() {
        // Configure normal component
        componentNormal.cancelActionHandler = cancelAction
        componentNormal.complationActionHandler = complationAction
        contentView.addSubview(componentNormal)
        
        // Configure AI component
        componentAI.cancelActionHandler = cancelAction
        componentAI.complationActionHandler = complationAction
        componentAI.navigatePostActionHandler = { [weak self] post in
            self?.navigatePostActionHandler?(post)
        }
        componentAI.navigateUserActionHandler = { [weak self] post in
            self?.navigateUserActionHandler?(post)
        }
        componentAI.navigateChatActionHandler = { [weak self] post in
            self?.navigateChatActionHandler?(post)
        }
        componentAI.reloadActionHandler = { [weak self] in
            self?.reloadActionHandler?()
        }
        contentView.addSubview(componentAI)
    }
    
    /// Sets up the UI constraints
    private func setupUIConstraints() {
        componentNormal.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        componentAI.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    /// Updates the UI based on the provided data
    /// Shows either AI or normal component based on the style type
    private func updateUI(data: SOPersonalHomePagePublishData) {
        // Hide all subviews initially
        contentView.subviews.forEach { $0.isHidden = true }
        
        guard let type = data.styles?.first?.type else { return }
        
        if type == "aiDiagnose" || type == "aiDiagnoseRecommend" {
            // Show AI component
            componentAI.isHidden = false
            componentAI.update(data: data)
            
            SoulEvent.eventExpose("AI_imp_exp", params: nil, pageId: "HomePage_Main", pagePrama: nil)
        } else {
            // Handle social card type
            if type == "socialCard" {
                var publishRouterURL = "soul://ul.soulapp.cn/post/postMoment?pageFrom=homepageguide&flushCache=1&isSocializeCardEnable=1&tagsMaxCount=50"
                if let iconURL = data.styles?.first?.contents?.first?.iconUrl {
                    publishRouterURL += "&socializeCardImagePath=\(iconURL)"
                }
                data.styles?.first?.contents?.first?.jumpUrl = publishRouterURL
                SoulEvent.eventExpose("SocialCard_exp", params: nil, pageId: "HomePage_Main", pagePrama: nil)
            }
            // Show normal component
            componentNormal.isHidden = false
            componentNormal.update(data: data)
        }
        
        SOPersonalHomePagePublishActionManager.shared.recordPublishGuideExpose()
    }
    
    /// Updates UI constraints based on the provided data
    private func updateUIConstraints(data: SOPersonalHomePagePublishData) {
        
    }
    
    private let componentNormal = SOPersonalHomePagePublishCellComponentNormal()
    private let componentAI = SOPersonalHomePagePublishCellComponentAI()
}
