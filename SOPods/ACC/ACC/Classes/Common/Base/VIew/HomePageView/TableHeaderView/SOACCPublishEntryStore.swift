//
//  SOACCPublishEntryStore.swift
//  ACC
//
//  Created by 1021500723 on 2023/11/7.
//

import Foundation
import SoulABStrategy
import MMKV

public class SOACCPublishEntryStore: NSObject {
    
    private static let kMMKVLimitedCountKey = "com.acc.publish.entry.limit.count"
    
    static var kSettingLimitedMaxCount: Int {
        var count: Int
        if let value = SoulConfigManager.sharedInstance()?.globalConfig.object(forKey: "publish_assistant_post_count_limit") {
            count = SOIntegerWith(value, 3)
        } else {
            count = 3
        }
        return count
    }
    private static var kSettingLimitedMinTimeInterval: TimeInterval {
        var timeInterval: TimeInterval
        if let value = SoulConfigManager.sharedInstance()?.globalConfig.object(forKey: "publish_assistant_request_period") {
            timeInterval = SODoubleWith(value, 48)
        } else {
            timeInterval = 48
        }
        return max(timeInterval, 1) * 3600
    }
    
    @objc
    public static func isEnable(with postCount: Int) -> Bool {
        /// Count
        guard postCount <= kSettingLimitedMaxCount else { return false }
        /// Time
        let time = MMKV.default()?.double(forKey: kMMKVLimitedCountKey) ?? 0
        let now = Date().timeIntervalSince1970
        guard now - time > kSettingLimitedMinTimeInterval else { return false }
        
        return true
    }
    
    static func resetLimitedTimeInterval() {
        let time = Date().timeIntervalSince1970
        MMKV.default()?.set(time, forKey: kMMKVLimitedCountKey)
    }
    
}
