//
//  SOPersonalHomePagePublishActionManager.swift
//  ACC
//
//  Created by al<PERSON> on 2025/1/14.
//

import Foundation
import MMKV
import SoulABStrategy
import SoulCore

@objcMembers
public class SOPersonalHomePagePublishActionManager: NSObject {
    
    // Singleton instance for managing publish actions
    public static let shared = SOPersonalHomePagePublishActionManager()
    
    // Counters and timestamps for tracking guide interactions
    private var publishGuideClickCount = 0
    private var publishGuideExposeCount = 0
    private var publishGuideExposeTime: TimeInterval = .zero
    private var publishGuideAIExposeTime: TimeInterval = .zero
    
    // MMKV key for storing AI fold state, unique per user
    private var publishGuideAIFoldMMKVKey: String {
        "acc.publish.guide.ai.fold.state" + SOUserInfoManager.sharedInstance().userIdEcpt
    }
    
    // Timestamp for tracking publish completion exposure
    private var publishFinishExposeTime: TimeInterval = .zero
    
    // MARK: - Record
    
    /// Records when user clicks on the publish guide
    public func recordPublishGuideClick() {
        publishGuideClickCount += 1
        print("PublishActionManager - publishGuideClickCount: \(publishGuideClickCount)")
    }
    
    /// Records when the publish guide is exposed to the user
    public func recordPublishGuideExpose() {
        publishGuideExposeCount += 1
        if publishGuideExposeCount == 1 {
            publishGuideExposeTime = Date().timeIntervalSince1970
        }
        print("PublishActionManager - publishGuideExposeCount: \(publishGuideExposeCount)")
    }
    
    /// Records when the AI guide is exposed for a specific post
    /// - Parameter post: The post for which AI guide is exposed
    public func recordPublishAIGuideExpose(post: SOPost) {
        let key = "acc.publish.guide.ai.fold.state" + (post.authorIdEcpt ?? "authorIdEcpt") + (post.postIdEcpt ?? "postIdEcpt")
        publishGuideAIExposeTime = MMKV.default()?.double(forKey: key) ?? .zero
        guard publishGuideAIExposeTime == .zero else { return }
        publishGuideAIExposeTime = Date().timeIntervalSince1970
        MMKV.default()?.set(publishGuideAIExposeTime, forKey: key)
        print("PublishActionManager - publishGuideAIExposeTime: \(publishGuideAIExposeTime)")
    }
    
    /// Records when publish completion is exposed
    public func recordPublishFinishExpose() {
        publishFinishExposeTime = Date().timeIntervalSince1970
    }
    
    /// Saves the AI fold state to persistent storage
    /// - Parameter isFold: Boolean indicating if AI guide is folded
    public func recordPublishAIFold(isFold: Bool) {
        MMKV.default()?.set(isFold, forKey: publishGuideAIFoldMMKVKey)
    }
    
    // MARK: - TriggerTrap
    
    /// Determines if the guide should be hidden based on user interactions
    /// Returns true if any of the following conditions are met:
    /// - User has clicked the guide at least once
    /// - Guide has been exposed 5 or more times
    /// - 24 hours have passed since first exposure
    /// - User has completed a publish action
    public func triggerTrap() -> Bool {
        guard
            publishGuideClickCount > 0 ||
            publishGuideExposeCount > 10 ||
            (publishGuideExposeTime > .zero && Date().timeIntervalSince1970 - publishGuideExposeTime > 24 * 60 * 60) ||
            triggerTrapForPublish()
        else { return false }
        reset()
        return true
    }
    
    /// Checks if AI guide should be hidden (24 hours after exposure)
    public func triggerTrapForAI() -> Bool {
        guard
            publishGuideAIExposeTime > .zero && Date().timeIntervalSince1970 - publishGuideAIExposeTime > 24 * 60 * 60
        else { return false }
        return true
    }
    
    /// Retrieves the saved AI fold state
    public func triggerTrapForAIFold() -> Bool {
        MMKV.default()?.bool(forKey: publishGuideAIFoldMMKVKey, defaultValue: false) ?? false
    }
    
    /// Checks if user has completed a publish action
    public func triggerTrapForPublish() -> Bool {
        guard
            publishFinishExposeTime > .zero
        else { return false }
        return true
    }
    
    // Reset
    
    private func reset() {
        publishGuideClickCount = 0
        publishGuideExposeCount = 0
        publishGuideExposeTime = .zero
        resetForPublish()
    }
    
    private func resetForPublish() {
        publishFinishExposeTime = .zero
    }
    
    // MARK: - Life Cycle
    
    private override init() {
        super.init()
        
        setupNotification()
    }
}

extension SOPersonalHomePagePublishActionManager {
    
    // MARK: - Notification
    
    private func setupNotification() {
        NotificationCenter.default.addObserver(self, selector: #selector(publishFinish), name: .init(rawValue: "post_publish_finished"), object: nil)
    }
    
    @objc
    private func publishFinish() {
        recordPublishFinishExpose()
    }
}
