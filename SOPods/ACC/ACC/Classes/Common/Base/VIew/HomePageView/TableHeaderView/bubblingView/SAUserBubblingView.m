//
//  SAUserBubblingView.m
//  ACC
//
//  Created by 1021500721 on 2024/2/1.
//

#import "SAUserBubblingView.h"
#import <SOWebImage/SOWebImage.h>
#import <Masonry/Masonry.h>
#import <SoulUIKit/SOUIKit.h>
#import <SoulAssets/UIImage+SoulAssets.h>
#import <SORemoteResKit/UIImageView+SoRemoteRes.h>
@interface SAUserBubblingView ()

@property(nonatomic, strong) SOUserBubblingModel *bubblingModel;

@property(nonatomic, strong) UIImageView *bgImageView;
@property(nonatomic, strong) UIImageView *iconImageView;
@property(nonatomic, strong) UILabel *titleLabel;
@property(nonatomic, strong) UILabel *timeLabel;

@property(nonatomic, strong) CAKeyframeAnimation *scaleAnimation;
@end

@implementation SAUserBubblingView

- (instancetype)initWithFrame:(CGRect)frame bubblingModel:(SOUserBubblingModel *)bubblingModel {
    if (self = [super initWithFrame:frame]) {
        self.bubblingModel = bubblingModel;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.bgImageView];
    [self.bgImageView addSubview:self.titleLabel];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(14);
        make.top.mas_equalTo(10);
        make.height.mas_equalTo(20);
    }];
    
    [self.bgImageView addSubview:self.iconImageView];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(14);
        make.top.mas_equalTo(self.titleLabel.mas_bottom).offset(4);
        make.height.width.mas_equalTo(16);
    }];
    

    [self.bgImageView addSubview:self.timeLabel];
    
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.iconImageView.mas_right).offset(4);
        make.centerY.mas_equalTo(self.iconImageView);
        make.height.mas_equalTo(16);
        make.right.mas_equalTo(-8);
    }];

}

- (void)playAnimation {
    [self.bgImageView.layer addAnimation:self.scaleAnimation forKey:@"kUserBubbScale"];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.bgImageView.layer removeAnimationForKey:@"kUserBubbScale"];
        [self.bgImageView.layer addAnimation:self.scaleAnimation forKey:@"kUserBubbScale"];
    });
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.bgImageView.layer removeAnimationForKey:@"kUserBubbScale"];
            [UIView animateWithDuration:0.3 delay:0 options:0 animations:^{
                self.bgImageView.transform = CGAffineTransformMakeScale(0.01, 0.01);
            }                completion:^(BOOL finished) {
                [self removeFromSuperview];
            }];
    });
}

#pragma mark -- getter --

- (UIImageView *)bgImageView {
    if (!_bgImageView) {
        _bgImageView = [[UIImageView alloc] initWithFrame:self.bounds];
        [_bgImageView soSetImageWithResKey:@"user_home/home_user_bubbling_new_1"];
    }
    return _bgImageView;
}

- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [UIImageView new];
        [_iconImageView so_setImageWithURL:[NSURL URLWithString:self.bubblingModel.mood]];
    }
    return _iconImageView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.text = self.bubblingModel.stateTip;
        _titleLabel.font = FONT(TextFontName_Medium, 14);
        _titleLabel.textColor = SOColor(3);
    }
    return _titleLabel;
}

- (UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [UILabel new];
        if (self.bubblingModel.desc.length) {
            _timeLabel.text = [NSString stringWithFormat:@"%@ · %@",self.bubblingModel.desc,self.bubblingModel.createStr];
        } else {
            _timeLabel.text = self.bubblingModel.createStr;
        }
        _timeLabel.font = FONT(TextFontName, 12);
        _timeLabel.textColor = SOColor(6);
    }
    return _timeLabel;
}

- (CAKeyframeAnimation *)scaleAnimation{
    if (!_scaleAnimation){
        _scaleAnimation = [CAKeyframeAnimation animation];
        _scaleAnimation.keyPath = @"transform.scale";
        _scaleAnimation.duration = 1.2;
        _scaleAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
        _scaleAnimation.values = @[@(1),@(0.95),@(1),@(0.97),@(1)];
        _scaleAnimation.keyTimes = @[@(0),@(0.25),@(0.5),@(0.7),@(1)];
    }
    return _scaleAnimation;
}
@end
