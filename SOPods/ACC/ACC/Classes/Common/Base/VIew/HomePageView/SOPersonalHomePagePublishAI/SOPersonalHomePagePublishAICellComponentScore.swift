//
//  SOPersonalHomePagePublishAICellComponentScore.swift
//  ACC
//
//  Created by al<PERSON> on 2025/6/3.
//

import UIKit
import SnapKit
import SoulUIKitExtended
import SoulRouter
import SoulMobEvent
import SoSwiftLottieBridge
import SoulAssets

/// Score component for displaying AI-generated attractiveness scores
/// Shows animated progress bar and title with highlight support
class SOPersonalHomePagePublishAICellComponentScore: UIView {
    
    /// Callback to reload the cell content
    @objc
    public var reloadActionHandler: (() -> Void)?
    
    /// Data model containing score information
    private var data: SOPersonalHomePagePublishAIData?
    
    // MARK: - Life Circle
    
    init() {
        super.init(frame: .zero)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Data
    
    /// Updates the component with new AI score data
    /// - Parameters:
    ///   - data: The AI score data containing title and score information
    ///   - iconHidden: Whether to hide the score icon and animation
    func update(data: SOPersonalHomePagePublishAIData, iconHidden: Bool = false) {
        self.data = data
        updateUI(data: data, iconHidden: iconHidden)
        updateUIConstraints(data: data, iconHidden: iconHidden)
    }
    
    // MARK: - UI
    
    /// Sets up the initial UI components
    private func setupUI() {
        addSubview(container)
        container.addSubview(imageView)
        container.addSubview(animatedView)
        container.addSubview(titleLabel)
        container.addSubview(titleIcon)
        container.addSubview(processView)
    }
    
    /// Sets up the UI constraints for all components
    private func setupUIConstraints() {
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        imageView.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.top.equalTo(4)
            make.width.height.equalTo(24)
        }
        animatedView.snp.makeConstraints { make in
            make.leading.top.equalTo(imageView)
            make.width.height.equalTo(24)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(imageView.snp.trailing).offset(8)
            make.centerY.equalTo(imageView).offset(-1.5)
        }
        titleIcon.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel.snp.trailing).offset(0)
            make.centerY.equalTo(imageView)
            make.width.height.equalTo(16)
        }
        
        processView.snp.makeConstraints { make in
            make.leading.equalTo(imageView)
            make.trailing.equalTo(-16)
            make.top.equalTo(imageView.snp.bottom).offset(6)
            make.bottom.equalToSuperview()
            make.height.equalTo(4)
        }
    }
    
    /// Updates the UI with the provided AI score data
    /// - Parameters:
    ///   - data: The AI score data
    ///   - iconHidden: Whether to hide the score icon and animation
    private func updateUI(data: SOPersonalHomePagePublishAIData, iconHidden: Bool = false) {
        if let title = data.scoreData?.title {
            titleLabel.attributedText = makeHighlightedAttributedString(title)
        }
        let score: CGFloat = CGFloat(data.scoreData?.score ?? 0)
        let scoreTotal: CGFloat = CGFloat(data.scoreData?.scoreTotal ?? 100)
        processView.update(process: score / scoreTotal)
        
        imageView.isHidden = iconHidden
        animatedView.isHidden = iconHidden
        if iconHidden {
            titleLabel.snp.remakeConstraints { make in
                make.leading.equalTo(16)
                make.centerY.equalTo(imageView).offset(-1.5)
            }
        } else {
            playAnimation()
            titleLabel.snp.remakeConstraints { make in
                make.leading.equalTo(imageView.snp.trailing).offset(8)
                make.centerY.equalTo(imageView).offset(-1.5)
            }
        }
    }
    
    /// Updates UI constraints based on the provided data
    /// - Parameters:
    ///   - data: The AI score data
    ///   - iconHidden: Whether to hide the score icon and animation
    private func updateUIConstraints(data: SOPersonalHomePagePublishAIData, iconHidden: Bool = false) {
        
    }
    
    // MARK: - UI Components
    
    /// Main container view for all content
    private let container = UIView()
    
    /// Static icon for score display
    private let imageView = UIImageView().then {
        $0.image = .acc_imageNamed("acc_publish_ai_score_icon")
    }
    
    /// Label for displaying the score title with highlight support
    private let titleLabel = UILabel().then {
        $0.textColor = .soul.color(3)
        $0.font = .soul.PingFangSC(size: 14, type: .medium)
        $0.lineBreakMode = .byTruncatingMiddle
    }
    
    /// Icon displayed next to the title
    private let titleIcon = UIImageView().then {
        $0.image = .acc_imageNamed("acc_publish_ai_score_title_icon")
    }
    
    /// Progress view for displaying score progress bar
    private let processView = SOPersonalHomePagePublishAICellComponentScoreProcessView()
    
    /// Animated view for score completion animation
    private let animatedView = LOTAnimationView().then {
        $0.loopAnimation = false
        $0.loopAnimationCount = 1
        $0.isHidden = true
    }
}

// MARK: - Text Processing Extensions

extension SOPersonalHomePagePublishAICellComponentScore {
    
    /// Creates attributed string with highlighted text from raw string containing highlight tags
    /// - Parameter rawString: The raw string containing <highlight> tags
    /// - Returns: Attributed string with highlighted portions
    private func makeHighlightedAttributedString(_ rawString: String) -> NSMutableAttributedString {
        let highlightAttributedString = NSMutableAttributedString()
        
        let pattern = "(?:<highlight>([^<]+)</highlight>)|([^<]+)"
        let regex = try! NSRegularExpression(pattern: pattern, options: [])
        let matches = regex.matches(in: rawString, options: [], range: NSRange(rawString.startIndex..., in: rawString))
        
        for match in matches {
            if let highlightRange = Range(match.range(at: 1), in: rawString) {
                let highlightText = String(rawString[highlightRange])
                let highlightAttr = NSAttributedString(string: highlightText, attributes: [
                    .foregroundColor: UIColor.soul.color(2),
                    .font: UIFont.monospacedDigitSystemFont(ofSize: 18, weight: .heavy)
                ])
                highlightAttributedString.append(highlightAttr)
            } else if let textRange = Range(match.range(at: 2), in: rawString) {
                let normalText = String(rawString[textRange])
                let normalAttr = NSAttributedString(string: normalText, attributes: [
                    .foregroundColor: UIColor.soul.color(3),
                    .font: UIFont.monospacedSystemFont(ofSize: 14, weight: .medium)
                ])
                highlightAttributedString.append(normalAttr)
            }
        }
        return highlightAttributedString
    }
}

// MARK: - Animation Extensions

extension SOPersonalHomePagePublishAICellComponentScore {
    
    /// Plays the score completion animation
    /// Only plays once per session to avoid repetition
    func playAnimation() {
        guard !SOPersonalHomePagePublishAIDataStore.shared.loadingAnimationPlayed else { return }
        SOPersonalHomePagePublishAIDataStore.shared.loadingAnimationPlayed = true
        
        let animationURL = NSURL.publish_resourceNamed("publish_aigc_task_action_bar_success_lottie", ofType: "zip")
        animatedView.setAnimationWithZipPath(animationURL.path) { [weak self] success in
            guard let self = self else { return }
            self.imageView.isHidden = true
            self.animatedView.isHidden = false
            self.animatedView.play { [weak self] finish in
                guard let self = self else { return }
                self.imageView.isHidden = false
                self.animatedView.isHidden = true
            }
        }
    }
    
    /// Stops the score completion animation
    func stopAnimation() {
        imageView.isHidden = false
        animatedView.isHidden = true
        animatedView.stop()
    }
}

/// Progress view component for displaying score progress bar
/// Shows animated progress with background and highlight layers
class SOPersonalHomePagePublishAICellComponentScoreProcessView: UIView {
    
    /// Current progress value (0.0 to 1.0)
    private var currentProcess: CGFloat = 0
    
    // MARK: - Life Circle
    
    init() {
        super.init(frame: .zero)
        
        setupUI()
        setupUIConstraints()
        /// Assign the mask view
        highlightView.mask = hightlightMaskView
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        /// Update the mask's frame here, as highlightView's bounds are now accurate.
        let maskWidth = container.bounds.width * currentProcess
        let maskHeight = container.bounds.height
        hightlightMaskView.frame = CGRect(x: 0, y: 0, width: maskWidth, height: maskHeight)
        iconView.snp.updateConstraints { make in
            make.centerX.equalTo(maskWidth)
        }
    }
    
    // MARK: - Action
    
    /// Updates the progress value and triggers layout update
    /// - Parameter process: Progress value between 0.0 and 1.0
    func update(process: CGFloat) {
        currentProcess = max(0.0, min(1.0, process))
        setNeedsLayout()
        layoutIfNeeded()
    }
    
    // MARK: - Data
    
    /// Updates the progress view
    func update() {
        updateUI()
        updateUIConstraints()
    }
    
    // MARK: - UI
    
    /// Sets up the initial UI components with mask for progress animation
    private func setupUI() {
        addSubview(container)
        container.addSubview(progressContainer)
        progressContainer.addSubview(backgroundView)
        progressContainer.addSubview(highlightView)
        container.addSubview(iconView)
        highlightView.mask = hightlightMaskView
    }
    
    /// Sets up the UI constraints for all components
    private func setupUIConstraints() {
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        progressContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        highlightView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        iconView.snp.makeConstraints { make in
            make.centerX.equalTo(0)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
    }
    
    /// Updates the UI
    private func updateUI() {
        
    }
    
    /// Updates UI constraints
    private func updateUIConstraints() {
        
    }
    
    // MARK: - UI Components
    
    /// Main container view for all content
    private let container = UIView()
    
    /// Container for progress bar with corner radius
    private let progressContainer = UIView().then {
        $0.layer.cornerRadius = 2
        $0.layer.masksToBounds = true
    }
    
    /// Background image for progress bar
    private let backgroundView = UIImageView().then {
        $0.image = .acc_imageNamed("acc_publish_ai_score_process_background")
        $0.contentMode = .scaleToFill
    }
    
    /// Highlight image for progress bar (masked for animation)
    private let highlightView = UIImageView().then {
        $0.image = .acc_imageNamed("acc_publish_ai_score_process_highlight")
        $0.contentMode = .scaleToFill
    }
    
    /// Mask view for controlling highlight visibility
    private let hightlightMaskView = UIView().then {
        $0.backgroundColor = .black
    }
    
    /// Icon displayed at the end of progress bar
    private let iconView = UIImageView(image: .acc_imageNamed("acc_publish_ai_score_progress_icon"))
}
