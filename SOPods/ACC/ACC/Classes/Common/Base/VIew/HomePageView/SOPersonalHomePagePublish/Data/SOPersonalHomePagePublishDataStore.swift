//
//  SOPersonalHomePagePublishDataStore.swift
//  ACC
//
//  Created by 1021500723 on 2024/1/3.
//

import Foundation
import SoulAPI
import YYModel
import SoulABStrategy
import SoulUserModule
import SwiftyJSON
import SoulCore

/// Data store for managing personal homepage publish data
public class SOPersonalHomePagePublishDataStore: NSObject {
    
    /// Shared singleton instance
    @objc
    public static let shared = SOPersonalHomePagePublishDataStore()
    
    /// Cache of publish data items, keyed by encrypted user ID
    private(set) var items: [String: SOPersonalHomePagePublishData] = [:]
    
    /// Current user's publish data
    public var item: SOPersonalHomePagePublishData? {
        let key = "userIdEcpt" + SOUserInfoManager.sharedInstance().userIdEcpt
        return items[key]
    }
        
    /// Fetches publish guide data from server
    /// - Parameter completion: Callback with fetched data or cached data
    @objc
    public func requestData(_ completion: @escaping (SOPersonalHomePagePublishData?) -> Void) {
        let key = "userIdEcpt" + SOUserInfoManager.sharedInstance().userIdEcpt
        var item = items[key]
        let parameters: [String: Any] = [
            "postFeedbackFlag":SOPersonalHomePagePublishActionManager.shared.triggerTrapForPublish() ? 1 : 0
        ]
//        item = fakeItem()
        if SOPersonalHomePagePublishActionManager.shared.triggerTrap() {
            item = nil
        }
        guard item == nil, let host = HttpDnsHelper.curEnvDomain(.newPost) else {
            completion(item)
            return
        }
        let path = "/v3/post/homepage/guide/card"
        SoulAPIManager.sharedInstance()?.requestGET(path, baseUrl: host, parameters: parameters, success: { model, _ in
            guard let model = model, model.codeSuccess, let modelData = model.data else {
                completion(item)
                return
            }
            let json = JSON(modelData)
            guard let jsonData = try? json.rawData() else {
                completion(item)
                return
            }
            guard let e = try? JSONDecoder().decode(SOPersonalHomePagePublishData.self, from: jsonData), let styles = e.styles, styles.count > 0  else {
                completion(item)
                return
            }
            item = e
            
            if let cards = e.cards, !cards.isEmpty {
                let posts = json["recommendCardList"].arrayValue
                var postValid = false
                zip(cards, posts).forEach { card, json in
                    if let postJson = try? json["postCard"].rawData() {
                        let post = SOPost.yy_model(withJSON: postJson)
                        card.post = post
                        if post != nil {
                            postValid = true
                        }
                    }
                }
                if !postValid {
                    item?.cards = nil
                }
            }
            
            self.items[key] = item
            completion(item)
        }, fail: { _, _ in
            completion(item)
        })
    }
}

extension SOPersonalHomePagePublishDataStore {
    
    /// Checks if the publish feature should be enabled
    /// - Parameters:
    ///   - postCount: Number of user's posts
    ///   - isNewHome: Flag indicating if using new home page version
    /// - Returns: Boolean indicating if feature should be enabled
    @objc
    public static func isEnable(with postCount: Int, isNewHome: Bool = false) -> Bool {
        /// AB
        if let ab = ABTestStrategyCenter.shareInstance().multiTestStrategy(forKey: "214284"), ab.value == "a" {
            return false
        }
        /// Count 阿波罗配置（只在就版本判断）
        if isNewHome == false {
            guard postCount > SOACCPublishEntryStore.kSettingLimitedMaxCount else { return false }
        }
        /// Time
        if let item = SOPersonalHomePagePublishDataStore.shared.item {
            if let type = item.styles?.first?.type, (type == "aiDiagnose" || type == "aiDiagnoseRecommend") {
                return isEnableTimeLimit(key: kMMKVAILimitedCountKey)
            } else {
                return isEnableTimeLimit(key: kMMKVLimitedCountKey)
            }
        }
        return isEnableTimeLimit(key: kMMKVLimitedCountKey) || isEnableTimeLimit(key: kMMKVAILimitedCountKey)
    }
    
    /// Checks if time-based limitations allow feature to be enabled
    /// - Parameter key: MMKV key for storing time limit
    /// - Returns: Boolean indicating if feature should be enabled based on time
    private static func isEnableTimeLimit(key: String) -> Bool {
        let time = MMKV.default()?.double(forKey: key) ?? 0
        guard time > 0 else { return true }
        let date = Date(timeIntervalSince1970: time)
        let now = Date()
        guard now.timeIntervalSince1970 > time else { return false }
        return !Calendar.current.isDate(date, inSameDayAs: now)
    }
    
    /// Resets the regular publish time limit to current time
    @objc
    public static func resetLimitedTimeInterval() {
        let time = Date().timeIntervalSince1970
        MMKV.default()?.set(time, forKey: kMMKVLimitedCountKey)
    }
    
    /// Resets the AI publish time limit to current time
    @objc
    public static func resetAILimitedTimeInterval() {
        let time = Date().timeIntervalSince1970
        MMKV.default()?.set(time, forKey: kMMKVAILimitedCountKey)
    }
    
    /// MMKV key for storing regular publish time limit
    private static var kMMKVLimitedCountKey: String {
        "com.acc.publish.guide.cell.limit.count" + SOUserInfoManager.sharedInstance().userIdEcpt
    }
    
    /// MMKV key for storing AI publish time limit
    private static var kMMKVAILimitedCountKey: String {
        "com.acc.publish.guide.cell.limit.ai.count" + SOUserInfoManager.sharedInstance().userIdEcpt
    }
}

extension SOPersonalHomePagePublishDataStore {
    
    /// Creates fake data for testing purposes
    func fakeItem() -> SOPersonalHomePagePublishData {
        let content = SOPersonalHomePagePublishCardContentData()
        content.title = "上一次大笑时候你还记得吗"
        content.button = "去发布"
        content.jumpUrl = "soul://ul.soulapp.cn/post/postMoment?pageFrom=homepageguide"
        content.titleJumpUrl = "soul://ul.soulapp.cn/post/tagSquare?tagName=suol"
        
        let style = SOPersonalHomePagePublishCardStyleData()
        style.desc = "大家都在聊"
        style.type = "topic"
        style.contents = [
            content
        ]
        
        let data = SOPersonalHomePagePublishData()
        data.styles = [
            style
        ]
        return data
    }
}

/// Model representing the publish guide data structure
@objcMembers
public class SOPersonalHomePagePublishData: NSObject, Codable {
    
    public var requestId: String?
    /// List of different style configurations for the guide
    public var styles: [SOPersonalHomePagePublishCardStyleData]?
    /// List of recommended cards to show
    public var cards: [SOPersonalHomePagePublishCardRecommandData]?
    /// Flag indicating if the guide is folded/collapsed
    public var isFold = false
    
    enum CodingKeys: String, CodingKey {
        case requestId
        case styles = "guideDTOList"
        case cards = "recommendCardList"
    }
}

/// Model representing the style configuration for a publish guide card
@objcMembers
public class SOPersonalHomePagePublishCardStyleData: NSObject, Codable {
    
    public var titleIcon: String?
    public var desc: String?
    /// Type of guide (e.g., "topic", "aiDiagnose")
    public var type: String?
    /// List of content templates for this style
    public var contents: [SOPersonalHomePagePublishCardContentData]?
    
    enum CodingKeys: String, CodingKey {
        case titleIcon
        case desc
        case type
        case contents = "postTemplates"
    }
}

/// Model representing a recommended card in the publish guide
@objcMembers
public class SOPersonalHomePagePublishCardRecommandData: NSObject, Codable {
    
    public var type: String?
    /// Associated post data
    public var post: SOPost?
    
    enum CodingKeys: String, CodingKey {
        case type = "cardType"
    }
}

/// Model representing the content of a publish guide card
@objcMembers
public class SOPersonalHomePagePublishCardContentData: NSObject, Codable {
    
    public var iconUrl: String?
    public var title: String?
    public var button: String?
    /// URL for navigation when card is tapped
    public var jumpUrl: String?
    /// URL for navigation when title is tapped
    public var titleJumpUrl: String?
    /// List of interactive actions available for this content
    public var actions: [SOPersonalHomePagePublishCardContentAction]?
    
    enum CodingKeys: String, CodingKey {
        case iconUrl
        case title
        case button
        case jumpUrl
        case titleJumpUrl
        case actions = "subLabelDTOList"
    }
}

/// Model representing an interactive action in the publish guide
@objcMembers
public class SOPersonalHomePagePublishCardContentAction: NSObject, Codable {
    
    public var name: String?
    public var value: String?
    
    enum CodingKeys: String, CodingKey {
        case name
        case value
    }
}

extension SOPersonalHomePagePublishCardContentAction {
    
    /// Generates an attributed string combining name and value with specific styling
    var attributedString: NSAttributedString? {
        guard let title = name, let subtitle = value else { return nil }
        let attributedTitle = NSMutableAttributedString(string: "\(title) ")
        attributedTitle.yy_font = .soul.PingFangSC(size: 12)
        attributedTitle.yy_color = .soul.color(3)
        let attributedSubtitle = NSMutableAttributedString(string: subtitle)
        attributedSubtitle.yy_font = .soul.PingFangSC(size: 12, type: .bold)
        attributedSubtitle.yy_color = .soul.color(hex: 0xF364FF)
        let attributedString = NSMutableAttributedString()
        attributedString.append(attributedTitle)
        attributedString.append(attributedSubtitle)
        return attributedString
    }
}
