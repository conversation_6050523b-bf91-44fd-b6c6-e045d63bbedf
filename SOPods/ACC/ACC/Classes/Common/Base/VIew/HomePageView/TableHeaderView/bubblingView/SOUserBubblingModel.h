//
//  SOUserBubblingModel.h
//  ACC
//
//  Created by xupeng on 2021/11/18.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SOUserBubblingModel : NSObject

@property(nonatomic, assign) NSInteger bubblingType;   /**< 泡泡类型 */
@property(nonatomic, assign) BOOL hasPicked;           /**< 是否已被戳破 */
@property(nonatomic, assign) BOOL hasExp;              /**< 是否在实验内 */

@property(nonatomic, copy) NSString *stateTip;         /**< 状态内容 */
@property(nonatomic, copy) NSString *mood;             /**< 心情emoji */
@property(nonatomic, copy) NSString *moodTip;          /**< 心情emoji文案 */
@property(nonatomic, copy) NSString *createStr;        /**< 发布时间。比如：刚刚, n分钟前 */
@property(nonatomic, copy) NSString *desc;             /**< 自定义文案 */
@property(nonatomic, copy) NSString *skinUrl;          /**< 皮肤图片 */
@property(nonatomic, copy) NSString *consecuriveTimes; /**< 打卡次数 */


@end

NS_ASSUME_NONNULL_END
