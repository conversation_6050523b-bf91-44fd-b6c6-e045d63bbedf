//
//  SOPersonalHomePagePublishAICellLayout.swift
//  ACC
//
//  Created by al<PERSON> on 2025/6/3.
//

import Foundation

/// Layout manager for AI publish cell height calculation
/// Determines cell height based on content type and fold state
public class SOPersonalHomePagePublishAICellLayout: SOPersonalHomePageCellLayout {
    
    /// Data model containing the publish cell content
    @objc
    public var data: SOPersonalHomePagePublishAIData? {
        didSet {
            layoutHeight()
        }
    }
    
    /// Performs the layout calculation
    /// - Returns: Self for method chaining
    @discardableResult
    public override func layout() -> Any {
        layoutHeight()
        return self
    }
    
    /// Calculates the height of the cell based on its content type and state
    /// Handles different heights for publish enabled/disabled and fold/unfold states
    private func layoutHeight() {
        height = 0
        let topMargin: CGFloat = 8
        guard let data = data, data.scoreData != nil else { return }
        
        if data.publishDataEnable {
            if data.isFold {
                // Folded state: header only
                height = 64 + topMargin
            } else {
                // Unfolded state: header + content
                height = 204 + topMargin
            }
        } else {
            // Visit state: score + visit content
            height = 172 + topMargin
        }
    }
}

