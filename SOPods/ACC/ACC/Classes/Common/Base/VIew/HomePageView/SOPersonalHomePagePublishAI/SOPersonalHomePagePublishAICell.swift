//
//  SOPersonalHomePagePublishAICell.swift
//  ACC
//
//  Created by albert on 2025/6/3.
//

import UIKit
import SoulUIKitExtended
import SoulAssets
import SnapKit
import SOUGCBaseUI
import SDWebImage
import SoulEventBridge
import YYText
import SODarkMode
import SoulBussinessKit

/// Cell for displaying AI-powered publishing suggestions in the personal homepage
/// Shows either AI publish suggestions or visit recommendations based on data state
public class SOPersonalHomePagePublishAICell: SOBaseCell {
    
    // MARK: - Callback Handlers
    
    /// Callback to reload the cell content
    @objc
    public var reloadActionHandler: (() -> Void)?
    
    /// Callback to close the AI cell
    @objc
    public var closeActionHandler: (() -> Void)?
    
    // MARK: - Properties
    
    /// Data model containing AI publish and visit information
    private var data: SOPersonalHomePagePublishAIData?
    
    // MARK: - Life Circle
    
    @objc
    public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    /// Handles the cancel button tap
    @objc
    private func cancelAction() {
        
    }
    
    /// Handles the completion button tap
    /// Records the click event and navigates to post creation
    @objc
    private func complationAction() {
        guard let urlString = data?.publishData?.routerURL, let publishRouterURL = URL(string: urlString) else { return }
        try? SoulRouterManager.sharedInstance().handleOpen(publishRouterURL, fromVc: nil, appParas: nil)
    }
    
    // MARK: - Data
    
    /// Updates the cell with new AI data
    /// - Parameter data: The AI data containing publish and visit information
    @objc
    public func update(data: SOPersonalHomePagePublishAIData?) {
        self.data = data
        guard let data = data else { return }
        updateUI(data: data)
        updateUIConstraints(data: data)
    }
    
    // MARK: - UI
    
    /// Sets up the initial UI components and callback handlers
    private func setupUI() {
        contentView.addSubview(container)
        container.addSubview(containerBackgroundImageView)
        container.addSubview(publishView)
        container.addSubview(visitView)
        
        publishView.reloadActionHandler = { [weak self] in
            self?.reloadActionHandler?()
        }
        publishView.closeActionHandler = { [weak self] in
            self?.closeActionHandler?()
        }
        visitView.closeActionHandler = { [weak self] in
            self?.closeActionHandler?()
        }
    }
    
    /// Sets up the UI constraints for all components
    private func setupUIConstraints() {
        container.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.trailing.equalTo(-16)
            make.top.equalTo(8)
            make.bottom.equalToSuperview()
        }
        containerBackgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        publishView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        visitView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    /// Updates the UI based on the provided AI data
    /// Shows either AI publish or visit component based on the data state
    /// - Parameter data: The AI data
    private func updateUI(data: SOPersonalHomePagePublishAIData) {
        visitView.update(data: data)
        publishView.update(data: data)
        if data.publishDataEnable {
            visitView.isHidden = true
            publishView.isHidden = false
        } else {
            visitView.isHidden = false
            publishView.isHidden = true
        }
        if data.isFold {
            containerBackgroundImageView.image = .acc_imageNamed("acc_publish_ai_cell_fold_background")
        } else {
            containerBackgroundImageView.image = .acc_imageNamed("acc_publish_ai_cell_background")
        }
    }
    
    /// Updates UI constraints based on the provided data
    /// - Parameter data: The AI data
    private func updateUIConstraints(data: SOPersonalHomePagePublishAIData) {
        
    }
    
    // MARK: - UI Components
    
    /// Main container view for all content
    private let container = UIView()
    
    /// Background image view that changes based on fold state
    private let containerBackgroundImageView = UIImageView().then {
        $0.image = .acc_imageNamed("acc_publish_ai_cell_background")
        $0.contentMode = .scaleToFill
        $0.isUserInteractionEnabled = true
        $0.layer.cornerRadius = 8
        $0.layer.masksToBounds = true
    }
    
    /// AI publish component for displaying publishing suggestions
    private let publishView = SOPersonalHomePagePublishAICellComponentPublish()
    
    /// AI visit component for displaying visit recommendations
    private let visitView = SOPersonalHomePagePublishAICellComponentVisit()
}
