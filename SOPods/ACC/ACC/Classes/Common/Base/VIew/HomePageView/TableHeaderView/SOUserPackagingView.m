
//  SOUserPackagingView.m
//  ACC
//  Created by lian<PERSON> on 2024/9/19.


#import "SOUserPackagingView.h"
#import <SoulUIKit/SoulUIKit.h>
#import <Masonry/Masonry.h>
#import <SoulAssets/SoulAssets.h>
#import <SoulMobEvent/SoulEventClient.h>
#import <SoulUserModule/SOUserInfoManager.h>
@implementation SOUserPackagingView

-(UILabel *)contentLabel{
    if (!_contentLabel) {
        _contentLabel = [[UILabel alloc] init];
        _contentLabel.font = FONT(TextFontName_Light, 15);
        _contentLabel.textColor = SOColor(3);
        _contentLabel.numberOfLines = 0;
        _contentLabel.text = @"足不出户就可以在家看遍各地有一点不是太完美，就是看到想买的想吃的东西，也只能看看，有点逼死强迫症的感觉。如果要是先看一下参考参考，但是全都虚拟看过一遍了。";
    }
    return _contentLabel;
}

-(UILabel *)titleLabel{
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = FONT(TextFontName, 12);
        _titleLabel.textColor = SOColor(6);
        _titleLabel.textAlignment = NSTextAlignmentLeft;
        _titleLabel.text = @"为了让你的主页更吸引人，我会帮你进行一些优化";
    }
    return _titleLabel;
}

-(UIImageView *)leftImageView{
    if (!_leftImageView) {
        _leftImageView = [[UIImageView alloc] init];
        _leftImageView.contentMode = UIViewContentModeScaleAspectFit;
        _leftImageView.image = [UIImage acc_imageNamed:@"icon_userpack_ai"];
    }
    return _leftImageView;
}
-(UIImageView *)contentImageView{
    if (!_contentImageView) {
        _contentImageView = [[UIImageView alloc] init];
        _contentImageView.contentMode = UIViewContentModeScaleAspectFit;
        _contentImageView.layer.cornerRadius = 5;
        _contentImageView.clipsToBounds= YES;
    }
    return _contentImageView;
}

-(UIButton *)closeBtn{
    if (!_closeBtn) {
        _closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_closeBtn addTarget:self action:@selector(closeTipsClick) forControlEvents:UIControlEventTouchUpInside];
        [_closeBtn setImage:[UIImage acc_imageNamed:@"acc_publish_entry_close"] forState:UIControlStateNormal];
//        _closeBtn.hidden = YES;
    }
    return _closeBtn;
}

-(UIButton *)topShowBtn{
    if (!_topShowBtn) {
        _topShowBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_topShowBtn setTitle:@"去展示" forState:UIControlStateNormal];
        _topShowBtn.titleLabel.font = FONT(TextFontName, 12);
        [_topShowBtn setTitleColor:SOColor(1) forState:UIControlStateNormal];
        [_topShowBtn addTarget:self action:@selector(showTipsClick) forControlEvents:UIControlEventTouchUpInside];
        _topShowBtn.hidden = YES;
    }
    return _topShowBtn;
}

-(UIButton *)bottomShowBtn{
    if (!_bottomShowBtn) {
        _bottomShowBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _bottomShowBtn.titleLabel.font = FONT(TextFontName_Medium, 13);
        [_bottomShowBtn setTitle:@"去丰富引力签更新描述" forState:UIControlStateNormal];
        [_bottomShowBtn setTitleColor:SOColor(1) forState:UIControlStateNormal];
        [_bottomShowBtn addTarget:self action:@selector(updateDescriptionClick) forControlEvents:UIControlEventTouchUpInside];
    }
    return _bottomShowBtn;
}

-(UILabel *)tipsLabel{
    if (!_tipsLabel) {
        _tipsLabel = [[UILabel alloc] init];
        _tipsLabel.font = FONT(TextFontName, 13);
        _tipsLabel.textColor = SOColor(6);
        _tipsLabel.text = @"引力签编辑成功，描述更新中...";
        _tipsLabel.textAlignment = NSTextAlignmentLeft;
        _tipsLabel.hidden = YES;
    }
    return _tipsLabel;
}
-(UIImageView *)rightArrowImageView{
    if (!_rightArrowImageView) {
        _rightArrowImageView = [[UIImageView alloc] init];
        _rightArrowImageView.image = [UIImage acc_imageNamed:@"icon_userpack_rightArrow"];
        _rightArrowImageView.hidden = YES;
    }
    return _rightArrowImageView;
}

-(UIImageView *)bottomArrowImageView{
    if (!_bottomArrowImageView) {
        _bottomArrowImageView = [[UIImageView alloc] init];
        _bottomArrowImageView.image = [UIImage acc_imageNamed:@"icon_userpack_rightArrow"];
    }
    return _bottomArrowImageView;
}
- (instancetype)init {
    if (self = [super init]) {
        [self setupSubViews];
    }
    return self;
}

-(void)setupSubViews{
    
    [self addSubview:self.leftImageView];
    [self addSubview:self.titleLabel];
    [self addSubview:self.closeBtn];
    [self addSubview:self.topShowBtn];
    [self addSubview:self.rightArrowImageView];
    [self addSubview:self.contentLabel];
    [self addSubview:self.contentImageView];
    [self addSubview:self.bottomShowBtn];
    [self addSubview:self.bottomArrowImageView];
    [self addSubview:self.tipsLabel];
    [self initUIWithAll];
    self.backgroundColor = SOColor(0);
}

-(void)initUIWithAll{
    [self.leftImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(16);
        make.top.mas_offset(16);
        make.size.mas_equalTo(CGSizeMake(16, 16));
    }];
    
    [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.leftImageView.mas_right).mas_offset(4);
        make.centerY.mas_equalTo(self.leftImageView.mas_centerY);
        make.right.mas_offset(-22);
        make.height.mas_equalTo(18);
    }];
    
    
    [self.closeBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-5);
        make.size.mas_equalTo(CGSizeMake(30, 30));
        make.centerY.mas_equalTo(self.leftImageView.mas_centerY);
    }];
    
    [self.rightArrowImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-13);
        make.size.mas_equalTo(CGSizeMake(12, 12));
        make.centerY.mas_equalTo(self.leftImageView.mas_centerY);
    }];
    
    [self.topShowBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-22);
        make.centerY.mas_equalTo(self.leftImageView.mas_centerY);
        make.size.mas_equalTo(CGSizeMake(45, 18));
    }];
    [self.contentLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(16);
        make.right.mas_offset(-16);
        make.top.mas_equalTo(self.titleLabel.mas_bottom).mas_offset(8);
    }];
    
    [self.contentImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(16);
        make.right.mas_offset(-16);
        make.top.mas_equalTo(self.contentLabel.mas_bottom).mas_offset(8);
        make.height.mas_equalTo([UIScreen mainScreen].bounds.size.width -32);
        make.bottom.mas_offset(-40);
    }];
    
    [self.bottomShowBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_offset(-8);
        make.left.mas_offset(16);
        make.height.mas_equalTo(18);
    }];
    [self.bottomArrowImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.bottomShowBtn.mas_right).mas_offset(5);
        make.size.mas_equalTo(CGSizeMake(12, 12));
        make.centerY.mas_equalTo(self.bottomShowBtn.mas_centerY);
    }];
    [self.tipsLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_offset(-8);
        make.left.mas_offset(16);
        make.size.mas_equalTo(CGSizeMake([UIScreen mainScreen].bounds.size.width -32, 24));
    }];
}

-(void)initUIWithNoDisplay{
    
    self.tipsLabel.hidden = YES;
    self.bottomShowBtn.hidden = YES;
    self.bottomArrowImageView.hidden = YES;
    self.contentImageView.hidden = YES;
    self.contentLabel.hidden = YES;
    
    [self.leftImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(16);
        make.size.mas_equalTo(CGSizeMake(16, 16));
        make.top.mas_offset(16);
    }];
    
    [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.leftImageView.mas_right).mas_offset(4);
        make.centerY.mas_equalTo(self.leftImageView.mas_centerY);
        make.right.mas_offset(-22);
        make.height.mas_equalTo(18);
        make.bottom.mas_equalTo(self.mas_bottom).mas_offset(-16);
    }];
    
    [self.topShowBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_offset(-22);
        make.centerY.mas_equalTo(self.leftImageView.mas_centerY);
        make.size.mas_equalTo(CGSizeMake(45, 18));
    }];
}


-(void)initUIWithNoImage{

    self.contentImageView.hidden = YES;
    [self.contentLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_offset(16);
        make.right.mas_offset(-16);
        make.top.mas_equalTo(self.titleLabel.mas_bottom).mas_offset(8);
        make.bottom.mas_equalTo(self.mas_bottom).mas_offset(-40);
    }];
}


-(CGFloat)viewHeight{
    
    if (self.isHidden) {
        return 0;
    }
    if (!self.display) {
        return 16+18+16;
    }
    _viewHeight = 16+16 + 8+24 + 16;
    
    if (!self.contentImageView.isHidden) {
        _viewHeight = _viewHeight+[UIScreen mainScreen].bounds.size.width -32 +8;
    }
    
    if (self.contentLabel.text.length>0) {
        _viewHeight = _viewHeight + self.contentHeight + 8;
    }
    
    if (self.otherPeoplePage) {
        _viewHeight = _viewHeight - 24-8;
    }
    return _viewHeight;
}


-(void)updateUIWithData:(SOUserPackModel*)data{
    if (data) {
        self.hidden = NO;
        self.display = data.display;
        self.canUpdateDescription = [data.remainderUpdateTimes intValue]>0?YES:NO;
        self.titleLabel.text = data.topContent;
        if (!data.display) {
            self.topShowBtn.hidden = NO;
            self.rightArrowImageView.hidden = NO;
            self.closeBtn.hidden = YES;
            self.bottomShowBtn.hidden = YES;
            self.bottomArrowImageView.hidden = YES;
            [self initUIWithNoDisplay];
            self.contentHeight = 0;
        } else {
            self.topShowBtn.hidden = YES;
            self.rightArrowImageView.hidden = YES;
            self.closeBtn.hidden = NO;
            self.bottomShowBtn.hidden = NO;
            self.bottomArrowImageView.hidden = NO;
            [self initUIWithAll];
            
            self.contentLabel.hidden = NO;
            NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:data.content];
            attributedText.yy_color = YYColor(3);
            attributedText.yy_font = FONT(TextFontName_Light, 15);
            attributedText.yy_lineSpacing =  0;
            attributedText.yy_minimumLineHeight = 26;
            attributedText.yy_maximumLineHeight = 26;
            self.contentLabel.attributedText = attributedText;
            YYTextLayout*contentTextLayout = [YYTextLayout layoutWithContainer:[YYTextContainer containerWithSize:CGSizeMake([UIScreen mainScreen].bounds.size.width-32, NSIntegerMax)] text:attributedText range:attributedText.yy_rangeOfAll];
            self.contentHeight = contentTextLayout.textBoundingSize.height+4;
            
            if (data.imgUrl.length>0) {
                self.contentImageView.hidden = NO;
                [self.contentImageView sd_setImageWithURL:[NSURL URLWithString:data.imgUrl]];
            }else{
                [self initUIWithNoImage];
            }
            
            if (data.updating) {
                self.tipsLabel.hidden = NO;
                self.bottomShowBtn.hidden = YES;
                self.bottomArrowImageView.hidden = YES;
            }else{
                [self.bottomShowBtn setTitle:[NSString stringWithFormat:@"去丰富引力签更新描述(%@/%@)",data.remainderUpdateTimes,data.totalUpdateTimes] forState:UIControlStateNormal];
                self.tipsLabel.hidden = YES;
                self.bottomShowBtn.hidden = NO;
                self.bottomArrowImageView.hidden = NO;
            }
            if (data.display) {
                /* AIGC描述_曝光曝光埋点 */
               [SoulEvent eventExpose:@"AIGCDescription_exp"
                               params:@{@"tuid": [SOUserInfoManager sharedInstance].userId}
                               pageId: nil pagePrama:nil];
            }
        }
        
        [[NSNotificationCenter defaultCenter] postNotificationName:@"Multi_ACCOUNT_RECEIVE_MESSAGE" object:nil userInfo:@{@"hasRead": @(1),@"key":@"userUnReadMsgDot_pack"}];

    }
}

-(void)updateUIWithOtherPeopleData:(SOUserPackModel*)data otherID:(NSString*)otherID{
    if (data) {
        self.display = data.display;
        self.tipsLabel.hidden = YES;
        self.bottomShowBtn.hidden = YES;
        self.bottomArrowImageView.hidden = YES;
        self.closeBtn.hidden = YES;
        self.rightArrowImageView.hidden = YES;
        self.topShowBtn.hidden = YES;
        self.contentImageView.hidden = YES;
        
        self.titleLabel.text = data.topContent;
        NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:data.content];
        attributedText.yy_color = YYColor(3);
        attributedText.yy_font = FONT(TextFontName_Light, 15);
        attributedText.yy_lineSpacing =  0;
        attributedText.yy_minimumLineHeight = 26;
        attributedText.yy_maximumLineHeight = 26;
        self.contentLabel.attributedText = attributedText;
        YYTextLayout*contentTextLayout = [YYTextLayout layoutWithContainer:[YYTextContainer containerWithSize:CGSizeMake([UIScreen mainScreen].bounds.size.width-32, NSIntegerMax)] text:attributedText range:attributedText.yy_rangeOfAll];
        self.contentHeight = contentTextLayout.textBoundingSize.height+4;
        
        [self.contentLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_offset(16);
            make.right.mas_offset(-16);
            make.top.mas_equalTo(self.titleLabel.mas_bottom).mas_offset(8);
            make.bottom.mas_offset(-16);
            make.height.mas_equalTo(self.contentHeight);
        }];
        
        if (data.imgUrl.length>0) {
            self.contentImageView.hidden = NO;
            [self.contentImageView sd_setImageWithURL:[NSURL URLWithString:data.imgUrl]];
            
            [self.contentLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_offset(16);
                make.right.mas_offset(-16);
                make.height.mas_equalTo(self.contentHeight);
                make.top.mas_equalTo(self.titleLabel.mas_bottom).mas_offset(8);
            }];
            [self.contentImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_offset(16);
                make.right.mas_offset(-16);
                make.top.mas_equalTo(self.contentLabel.mas_bottom).mas_offset(8);
                make.height.mas_equalTo([UIScreen mainScreen].bounds.size.width -32);
                make.bottom.mas_offset(-8);
            }];
        }
        
        if (data.display) {
            /* AIGC描述_曝光曝光埋点 */
           [SoulEvent eventExpose:@"AIGCDescription_exp"
                           params:@{@"tuid": SOString(otherID)}
                           pageId: nil pagePrama:nil];
        }
    }
}

- (CGSize)intrinsicContentSize{
    return CGSizeMake(KScreenWidth, self.viewHeight);
}

- (void)closeTipsClick{
    if (self.closePackBlock) {
        self.closePackBlock();
    }
    
    /* AIGC描述_关闭_点击点击埋点 */
   [SoulEvent eventClick:@"AIGCDescription_Close_clk"
                  params:nil
                  pageId: nil pagePrama:nil];
 
}

- (void)showTipsClick{
    if (self.showPackBlock) {
        self.showPackBlock();
    }
    
    /* AIGC描述_去展示_点击点击埋点 */
   [SoulEvent eventClick:@"AIGCDescription_Show_clk"
                  params:nil
                  pageId: nil pagePrama:nil];
 
}

- (void)updateDescriptionClick{
    SOWeakIfy(self);
    if (self.updateDescriptionBlock) {
        SOStrongIfy(self);
        self.updateDescriptionBlock(self.canUpdateDescription);
    }
    /* AIGC描述_去丰富_点击点击埋点 */
   [SoulEvent eventClick:@"AIGCDescription_Rich_clk"
                  params:nil
                  pageId: nil pagePrama:nil];
}

@end
