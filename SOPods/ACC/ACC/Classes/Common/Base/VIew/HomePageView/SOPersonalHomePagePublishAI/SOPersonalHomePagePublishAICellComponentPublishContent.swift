//
//  SOPersonalHomePagePublishAICellComponentPublishContent.swift
//  ACC
//
//  Created by albert on 2025/6/3.
//

import UIKit
import SnapKit
import SoulUIKitExtended

/// AI-powered publish content component for personal homepage
/// Displays AI-generated publishing suggestions with flip animation and action buttons
class SOPersonalHomePagePublishAICellComponentPublishContent: UIView {
    
    /// Data model containing AI publish suggestions and configuration
    private var data: SOPersonalHomePagePublishAIData?
    
    /// Current index of the displayed card in the publish data list
    private var currentCardIndex = 0
    
    // MARK: - Life Circle
    
    init() {
        super.init(frame: .zero)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    /// Handles refresh button tap to show next AI suggestion
    /// Cycles through available publish suggestions with flip animation
    @objc
    private func refreshAction() {
        guard let list = data?.publishDataList, list.count > 1 else { return }
        
        performRefreshButtonRotationAnimation()
        
        let nextCardIndex = (currentCardIndex + 1) % list.count
        guard let nextCardData = list[safe: nextCardIndex] else { return }
        currentCardIndex = nextCardIndex
        data?.publishData = nextCardData
        performFlipAnimation(with: nextCardData)
        
        SoulEvent.eventClick("AI_Home_Assistant_Switch_Clk", params: nil, pageId: nil, pagePrama: nil)
    }
    
    /// Handles container tap to navigate to publish page
    /// Records analytics event and opens router URL
    @objc
    private func routerAction() {
        guard let urlString = data?.publishData?.routerURL, let publishRouterURL = URL(string: urlString) else { return }
        try? SoulRouterManager.sharedInstance().handleOpen(publishRouterURL, fromVc: nil, appParas: nil)
        
        SoulEvent.eventClick("AI_Home_Assistant_Quick_Post", params: nil, pageId: nil, pagePrama: nil)
    }
    
    // MARK: - Data
    
    /// Updates the component with new AI publish data
    /// - Parameter data: The AI publish data containing suggestions and configuration
    func update(data: SOPersonalHomePagePublishAIData) {
        self.data = data
        updateUI(data: data)
        updateUIConstraints(data: data)
    }
    
    // MARK: - UI
    
    /// Sets up the initial UI components and gesture recognizers
    private func setupUI() {
        addSubview(container)
        container.addSubview(cardViewBackground)
        container.addSubview(cardViewNext)
        container.addSubview(cardView)
        container.addSubview(actionContainer)
        actionContainer.addSubview(refreshButton)
        actionContainer.addSubview(button)
        
        refreshButton.addTarget(self, action: #selector(refreshAction), for: .touchUpInside)
        container.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(routerAction)))
    }
    
    /// Sets up the UI constraints for all components
    private func setupUIConstraints() {
        container.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.bottom.equalToSuperview()
        }
        cardView.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.trailing.equalTo(-16)
            make.top.equalTo(2)
        }
        cardViewNext.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.trailing.equalTo(-16)
            make.top.equalTo(2)
            make.width.height.equalTo(cardView)
        }
        cardViewBackground.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.trailing.equalTo(-16)
            make.top.equalTo(2)
            make.width.height.equalTo(cardView)
        }
        actionContainer.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalTo(cardView.snp.bottom).offset(12)
            make.bottom.equalTo(-8)
            make.height.equalTo(32)
        }
        refreshButton.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.top.bottom.equalToSuperview()
            make.width.equalTo(115)
        }
        button.snp.makeConstraints { make in
            make.leading.equalTo(refreshButton.snp.trailing).offset(8)
            make.trailing.equalTo(-16)
            make.top.bottom.equalToSuperview()
        }
        
        cardViewBackground.transform = CGAffineTransform(scaleX: 0.9, y: 0.9).translatedBy(x: 25, y: 0)
        cardViewBackground.alpha = 0.5
        cardViewBackground.layoutIfNeeded()
    }
    
    /// Updates UI constraints based on the provided data
    /// - Parameter data: The AI publish data
    private func updateUIConstraints(data: SOPersonalHomePagePublishAIData) {
        
    }
    
    /// Updates the UI with the provided AI publish data
    /// - Parameter data: The AI publish data containing suggestions
    private func updateUI(data: SOPersonalHomePagePublishAIData) {
        guard let publishData = data.publishDataList?.first else { return }
        self.data?.publishData = publishData
        button.setTitle(publishData.actionTitle ?? "去发布", for: .normal)
        cardView.update(data: publishData)
        cardViewNext.update(data: publishData)
        cardViewNext.layoutIfNeeded()
        currentCardIndex = 0
        
        DispatchQueue.main.async {
            self.endCardViewNextTransformAnimate()
        }
        
        SoulEvent.eventExpose("AI_Home_Assistant_Quick_Expo", params: nil, pageId: nil, pagePrama: nil)
    }
    
    // MARK: - UI Components
    
    /// Main container view for all content
    private let container = UIView()
    
    /// Primary card view displaying current AI suggestion
    private var cardView = SOPersonalHomePagePublishAICellComponentPublishCard()
    
    /// Secondary card view for flip animation
    private var cardViewNext = SOPersonalHomePagePublishAICellComponentPublishCard()
    
    /// Background card view for flip animation
    private var cardViewBackground = SOPersonalHomePagePublishAICellComponentPublishCard()
    
    /// Container for action buttons
    private let actionContainer = UIView()
    
    /// Button to refresh and show next AI suggestion
    private let refreshButton = UIButton().then {
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = true
        $0.backgroundColor = .soul.color(light: .soul.color(hex: 0xEBFBFB), dark: .soul.color(hex: 0x45515C))
        $0.titleLabel?.font = .soul.PingFangSC(size: 14, type: .medium)
        $0.setTitleColor(.soul.color(1), for: .normal)
        $0.setTitle("换个瞬间", for: .normal)
        $0.setImage(.acc_imageNamed("acc_publish_ai_publish_refresh_icon"), for: .normal)
        $0.semanticContentAttribute = .forceLeftToRight
        $0.imageEdgeInsets = UIEdgeInsets(top: 0, left: -4, bottom: 0, right: 4)
        $0.titleEdgeInsets = UIEdgeInsets(top: 0, left: 4, bottom: 0, right: -4)
    }
    
    /// Primary action button to navigate to publish page
    private let button = UIButton().then {
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = true
        $0.backgroundColor = .soul.color(1)
        $0.titleLabel?.font = .soul.PingFangSC(size: 14, type: .medium)
        $0.setTitleColor(.soul.color(0), for: .normal)
        $0.isUserInteractionEnabled = false
    }
}

// MARK: - Animation Extensions

extension SOPersonalHomePagePublishAICellComponentPublishContent {
    
    /// Performs rotation animation for refresh button icon
    private func performRefreshButtonRotationAnimation() {
        guard let imageView = refreshButton.imageView else { return }
        
        let rotationAnimation = CABasicAnimation(keyPath: "transform.rotation")
        rotationAnimation.duration = 0.25
        rotationAnimation.fromValue = 0
        rotationAnimation.toValue = CGFloat.pi * 2
        rotationAnimation.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        
        imageView.layer.add(rotationAnimation, forKey: "rotationAnimation")
    }
    
    /// Performs flip animation to transition between AI suggestions
    /// - Parameter cardData: The new card data to display
    private func performFlipAnimation(with cardData: SOPersonalHomePagePublishAIPublishData) {
        cardViewNext.update(data: cardData)
        cardViewNext.layoutIfNeeded()
        endCardViewNextTransformAnimate()
        UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseInOut]) {
            self.beginCardViewTransformAnimate()
            self.beginCardViewNextTransformAnimate()
        } completion: { _ in
            self.cardView.update(data: cardData)
            self.endCardViewTransformAnimate()
            self.endCardViewNextTransformAnimate()
        }
    }
    
    /// Resets the next card view transform to initial state
    private func beginCardViewNextTransformAnimate() {
        cardViewNext.transform = CGAffineTransform.identity
        cardViewNext.alpha = 1
        cardViewNext.layoutIfNeeded()
    }
    
    /// Applies transform to position next card view for animation
    private func endCardViewNextTransformAnimate() {
        cardViewNext.transform = CGAffineTransform(scaleX: 0.9, y: 0.9).translatedBy(x: 25, y: 0)
        cardViewNext.alpha = 0.0
        cardViewNext.layoutIfNeeded()
    }
    
    /// Applies transform to animate current card view out
    private func beginCardViewTransformAnimate() {
        cardView.transform = CGAffineTransform(scaleX: 1.0, y: 1.0).translatedBy(x: -20, y: 0)
        cardView.alpha = 0
        cardView.layoutIfNeeded()
    }
    
    /// Resets the current card view transform to normal state
    private func endCardViewTransformAnimate() {
        cardView.transform = CGAffineTransform.identity
        cardView.alpha = 1.0
        cardView.layoutIfNeeded()
    }
}

// MARK: - Text Processing Extensions

extension SOPersonalHomePagePublishAICellComponentPublishContent {
    
    /// Creates attributed string with highlighted text from raw string containing highlight tags
    /// - Parameter rawString: The raw string containing <highlight> tags
    /// - Returns: Attributed string with highlighted portions
    private func makeHighlightedAttributedString(_ rawString: String) -> NSMutableAttributedString {
        let highlightAttributedString = NSMutableAttributedString()
        
        let pattern = "(?:<highlight>([^<]+)</highlight>)|([^<]+)"
        let regex = try! NSRegularExpression(pattern: pattern, options: [])
        let matches = regex.matches(in: rawString, options: [], range: NSRange(rawString.startIndex..., in: rawString))
        
        for match in matches {
            if let highlightRange = Range(match.range(at: 1), in: rawString) {
                let highlightText = String(rawString[highlightRange])
                let highlightAttr = NSAttributedString(string: highlightText, attributes: [
                    .foregroundColor: UIColor.soul.color(11),
                    .font: UIFont.soul.PingFangSC(size: 12, type: .medium)
                ])
                highlightAttributedString.append(highlightAttr)
            } else if let textRange = Range(match.range(at: 2), in: rawString) {
                let normalText = String(rawString[textRange])
                let normalAttr = NSAttributedString(string: normalText, attributes: [
                    .foregroundColor: UIColor.soul.color(6),
                    .font: UIFont.soul.PingFangSC(size: 12, type: .medium)
                ])
                highlightAttributedString.append(normalAttr)
            }
        }
        return highlightAttributedString
    }
}
