//
//  SOPersonalHomePagePublishCellComponentAI.swift
//  ACC
//
//  Created by albert on 2024/12/27.
//

import UIKit
import SoulUIKitExtended
import SoulAssets
import SnapKit
import SO<PERSON>GCBaseUI
import SDWebImage
import SoulEventBridge
import YYText
import SODarkMode
import SoulBussinessKit

class SOPersonalHomePagePublishCellComponentAI: UIView {
    
    // MARK: - Callback Handlers
    /// Handler for cancel action
    var cancelActionHandler: (() -> Void)?
    /// Handler for completion action 
    var complationActionHandler: (() -> Void)?
    /// Handler for reload action
    var reloadActionHandler: (() -> Void)?
    
    /// Handler for navigating to a post
    var navigatePostActionHandler: ((SOPost) -> Void)?
    /// Handler for navigating to a user profile
    var navigateUserActionHandler: ((SOPost) -> Void)?
    /// Handler for navigating to chat
    var navigateChatActionHandler: ((SOPost) -> Void)?
    
    /// Data model for the view
    private var data: SOPersonalHomePagePublishData?
    
    
    // MARK: - Life Circle
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        setupUI()
        setupUIConstraints()
        
        SODarkModeManager.addThemeChangeObserver(self) { [weak self] isDarkMode in
            guard let self = self else { return }
            guard let data = self.data else { return }
            self.updateUI(data: data)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    @objc
    private func cancelAction() {
        cancelActionHandler?()
        SoulEvent.eventClick("AI_impx_clk", params: nil, pageId: nil, pagePrama: nil)
        SOPersonalHomePagePublishDataStore.resetAILimitedTimeInterval()
    }
    
    @objc
    private func complationAction() {
        complationActionHandler?()
        SoulEvent.eventClick("AI_imp_clk", params: nil, pageId: nil, pagePrama: nil)
    }
    
    // MARK: - Data
    
    @objc
    public func update(data: SOPersonalHomePagePublishData) {
        self.data = data
        updateUI(data: data)
        updateUIConstraints(data: data)
    }
    
    // MARK: - UI
    
    private func setupUI() {
        backgroundImageView.contentMode = .scaleToFill
        backgroundImageView.isUserInteractionEnabled = true
        backgroundImageView.layer.cornerRadius = 16
        backgroundImageView.layer.masksToBounds = true
        backgroundImageView.layer.borderWidth = 1
        backgroundImageView.layer.soul.borderColor(light: .soul.color(hex: 0xE3E4FF), dark: .soul.color(hex: 0x2D313F))
        addSubview(backgroundImageView)
        
        backgroundImageView.addSubview(container)
        
        titleIcon.contentMode = .scaleAspectFit
        container.addSubview(titleIcon)
        container.addSubview(titleLabelIcon)
        titleLabel.font = .soul.PingFangSC(size: 13, type: .bold)
        titleLabel.textAlignment = .left
        container.addSubview(titleLabel)
        
        contentLabel.font = .soul.PingFangSC(size: 13)
        contentLabel.textColor = .soul.color(3)
        contentLabel.textAlignment = .left
        contentLabel.numberOfLines = 0
        container.addSubview(contentLabel)
        
        action1Label.layer.cornerRadius = 14
        action1Label.layer.masksToBounds = true
        action1Label.textAlignment = .center
        container.addSubview(action1Label)
        action2Label.layer.cornerRadius = 14
        action2Label.layer.masksToBounds = true
        action2Label.textAlignment = .center
        container.addSubview(action2Label)
        container.addSubview(actionIcon)
        
        cancelButton.setImage(.acc_imageNamed("acc_publish_guide_cell_cancel"), for: .normal)
        cancelButton.addTarget(self, action: #selector(cancelAction), for: .touchUpInside)
        container.addSubview(cancelButton)
        
        completeButton.backgroundColor = .soul.color(hex: 0x8B54FF)
        completeButton.layer.cornerRadius = 16
        completeButton.layer.masksToBounds = true
        completeButton.titleLabel?.font = .soul.PingFangSC(size: 13, type: .bold)
        completeButton.setTitleColor(.soul.color(0), for: .normal)
        completeButton.addTarget(self, action: #selector(complationAction), for: .touchUpInside)
        container.addSubview(completeButton)
        
        recommandCard.isHidden = true
        recommandCard.navigatePostActionHandler = { [weak self] post in
            self?.navigatePostActionHandler?(post)
        }
        recommandCard.navigateUserActionHandler = { [weak self] post in
            self?.navigateUserActionHandler?(post)
        }
        recommandCard.navigateChatActionHandler = { [weak self] post in
            self?.navigateChatActionHandler?(post)
        }
        recommandCard.reloadActionHandler = { [weak self] in
            self?.reloadActionHandler?()
        }
        recommandCard.cancelActionHandler = { [weak self] in
            self?.cancelAction()
        }
        backgroundImageView.addSubview(recommandCard)
    }
    
    private func setupUIConstraints() {
        backgroundImageView.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.top.equalTo(8)
            make.trailing.equalTo(-16)
            make.bottom.equalTo(0)
        }
        
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        titleIcon.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.top.equalTo(10)
            make.height.equalTo(24)
            make.width.equalTo(0)
        }
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleIcon.snp.trailing).offset(10)
            make.centerY.height.equalTo(titleIcon)
            make.trailing.lessThanOrEqualTo(-38)
        }
        titleLabelIcon.snp.makeConstraints { make in
            make.leading.centerY.equalTo(titleLabel)
        }
        contentLabel.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.trailing.equalTo(-16)
            make.top.equalTo(titleIcon.snp.bottom).offset(8)
            make.bottom.equalTo(completeButton.snp.top).offset(-12)
        }
        action1Label.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.bottom.equalTo(-14)
            make.height.equalTo(28)
            make.width.equalTo(100)
        }
        action2Label.snp.makeConstraints { make in
            make.leading.equalTo(action1Label.snp.trailing).offset(8)
            make.bottom.equalTo(-14)
            make.height.equalTo(28)
            make.width.equalTo(100)
        }
        actionIcon.snp.makeConstraints { make in
            make.trailing.equalTo(action1Label).offset(8)
            make.bottom.equalTo(action1Label)
            make.height.width.equalTo(18)
        }
        cancelButton.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview()
            make.height.width.equalTo(30)
        }
        completeButton.snp.makeConstraints { make in
            make.trailing.equalTo(-16)
            make.bottom.equalTo(-12)
            make.height.equalTo(32)
        }
    }
    
    private func updateUI(data: SOPersonalHomePagePublishData) {
        guard let style = data.styles?.first else { return }
        let type = style.type
        
        container.isHidden = true
        recommandCard.isHidden = true
        if type == "aiDiagnose" {
            container.isHidden = false
            updateUIOfStyle(data: data)
        }
        if let cards = data.cards, !cards.isEmpty {
            recommandCard.isHidden = false
            updateUIOfCard(data: data)
        }
    }
    
    private func updateUIOfStyle(data: SOPersonalHomePagePublishData) {
        guard let style = data.styles?.first, let content = style.contents?.first else { return }
        /// Title
        if let iconURL = style.titleIcon, !iconURL.isEmpty {
            let url = URL(string: iconURL)
            SOWebImageView.downloadImage(with: url) { image, _, _ in
                self.titleIcon.image = image
                if let image = image {
                    let width = image.size.width * 24 / image.size.height
                    self.titleIcon.snp.updateConstraints { make in
                        make.width.equalTo(width)
                    }
                } else {
                    self.titleIcon.snp.updateConstraints { make in
                        make.width.equalTo(0)
                    }
                }
                self.titleLabel.snp.updateConstraints { make in
                    make.leading.equalTo(self.titleIcon.snp.trailing).offset(image == nil ? 0 : 10)
                }
            }
            titleIcon.alpha = SODarkModeManager.isDarkMode() ? 0.8 : 1
        } else {
            titleIcon.image = nil
            titleIcon.snp.updateConstraints { make in
                make.width.equalTo(0)
            }
            titleLabel.snp.updateConstraints { make in
                make.leading.equalTo(titleIcon.snp.trailing).offset(0)
            }
        }
        titleLabel.text = style.desc
        if let title = titleLabel.text {
            let titleSize = title.size(limitedHeight: 16, attributes: [.font: titleLabel.font as Any])
            if SODarkModeManager.isDarkMode() {
                titleLabel.textColor = .soul.color(hex: 0xAEA6FF)
            } else {
                titleLabel.textColor = .soul.color(
                    gradient: [
                        .soul.color(hex: 0x6A5BFF),
                        .soul.color(hex: 0x5B93FF)
                    ],
                    size: titleSize,
                    from: .init(x: 0, y: 0),
                    to: .init(x: 1, y: 0)
                )
            }
        }
        /// Content
        if let title = content.title {
            let attributedString = NSMutableAttributedString(string: title, attributes: [.font: contentLabel.font as Any])
            attributedString.yy_minimumLineHeight = 20
            attributedString.yy_maximumLineHeight = 20
            contentLabel.attributedText = attributedString
        } else {
            contentLabel.text = content.title
        }
        contentLabel.text = content.title
        /// Action
        if let action = content.actions?.first, let attributedString = action.attributedString {
            action1Label.isHidden = false
            action1Label.attributedText = attributedString
            let width = attributedString.boundingRect(with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: 28), options: .usesLineFragmentOrigin, context: nil).size.width + 20
            action1Label.snp.updateConstraints { make in
                make.width.equalTo(width)
            }
            if SODarkModeManager.isDarkMode() {
                action1Label.backgroundColor = .soul.color(
                    gradient: [
                        .soul.color(hex: 0xEDEFFF, alpha: 0.06),
                        .soul.color(hex: 0xFFFFFF, alpha: 0.06)
                    ],
                    size: .init(width: width, height: 28),
                    from: .init(x: 0, y: 0),
                    to: .init(x: 1, y: 0)
                )
            } else {
                action1Label.backgroundColor = .soul.color(
                    gradient: [
                        .soul.color(hex: 0xEDEFFF, alpha: 0.6),
                        .soul.color(hex: 0xFFFFFF, alpha: 0.6)
                    ],
                    size: .init(width: width, height: 28),
                    from: .init(x: 0, y: 0),
                    to: .init(x: 1, y: 0)
                )
            }
        } else {
            action1Label.isHidden = true
        }
        if let action = content.actions?[safe: 1], let attributedString = action.attributedString {
            action2Label.isHidden = false
            action2Label.attributedText = attributedString
            let width = attributedString.boundingRect(with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: 28), options: .usesLineFragmentOrigin, context: nil).size.width + 20
            action2Label.snp.updateConstraints { make in
                make.width.equalTo(width)
            }
            if SODarkModeManager.isDarkMode() {
                action2Label.backgroundColor = .soul.color(
                    gradient: [
                        .soul.color(hex: 0xEDEFFF, alpha: 0.06),
                        .soul.color(hex: 0xFFFFFF, alpha: 0.06)
                    ],
                    size: .init(width: width, height: 28),
                    from: .init(x: 0, y: 0),
                    to: .init(x: 1, y: 0)
                )
            } else {
                action2Label.backgroundColor = .soul.color(
                    gradient: [
                        .soul.color(hex: 0xEDEFFF, alpha: 0.6),
                        .soul.color(hex: 0xFFFFFF, alpha: 0.6)
                    ],
                    size: .init(width: width, height: 28),
                    from: .init(x: 0, y: 0),
                    to: .init(x: 1, y: 0)
                )
            }
        } else {
            action2Label.isHidden = true
        }
        /// Button
        completeButton.setTitle("    \(content.button ?? "")    ", for: .normal)
    }
    
    private func updateUIOfCard(data: SOPersonalHomePagePublishData) {
        recommandCard.update(data: data)
    }
    
    private func updateUIConstraints(data: SOPersonalHomePagePublishData) {
        guard let style = data.styles?.first else { return }
        let type = style.type
        
        if type == "aiDiagnose" {
            if let cards = data.cards, !cards.isEmpty {
                container.snp.remakeConstraints { make in
                    make.leading.top.trailing.equalToSuperview()
                    make.bottom.equalTo(recommandCard.snp.top)
                }
                recommandCard.snp.remakeConstraints { make in
                    make.leading.trailing.bottom.equalToSuperview()
                }
            } else {
                container.snp.remakeConstraints { make in
                    make.edges.equalToSuperview()
                }
            }
        } else if type == "aiDiagnoseRecommend" {
            recommandCard.snp.remakeConstraints { make in
                make.leading.trailing.bottom.equalToSuperview()
            }
        }
    }
    
    /// Background image view with AI cell background
    private let backgroundImageView = UIImageView(image: .acc_imageNamed("acc_publish_guide_cell_background_ai"))
    
    /// Container view for main content
    private let container = UIView()
    /// Icon displayed next to title
    private let titleIcon = UIImageView()
    /// Icon displayed with title label
    private let titleLabelIcon = UIImageView(image: .acc_imageNamed("acc_publish_guide_cell_icon_title"))
    /// Label for displaying title text
    private let titleLabel = UILabel()
    /// Label for displaying content text
    private let contentLabel = UILabel()
    /// Label for first action button
    private let action1Label = UILabel()
    /// Label for second action button
    private let action2Label = UILabel()
    /// Icon displayed with action buttons
    private let actionIcon = UIImageView(image: .acc_imageNamed("acc_publish_guide_cell_icon_top"))
    /// Button to cancel the current action
    private let cancelButton = UIButton()
    /// Button to complete the current action
    private let completeButton = UIButton()
    
    /// Card view for recommended content
    private let recommandCard = SOPersonalHomePagePublishCellComponentAIRecommandCard()
}

class SOPersonalHomePagePublishCellComponentAIRecommandCard: UIView {
    
    var navigatePostActionHandler: ((SOPost) -> Void)?
    var navigateUserActionHandler: ((SOPost) -> Void)?
    var navigateChatActionHandler: ((SOPost) -> Void)?
    var reloadActionHandler: (() -> Void)?
    var cancelActionHandler: (() -> Void)?
    
    private var data: SOPersonalHomePagePublishData?
    private var items: [SOPost] = []
    
    // MARK: - Life Circle
    
    init() {
        super.init(frame: .zero)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    @objc
    private func foldAction() {
        guard let data = data else { return }
        data.isFold = !data.isFold
        SOPersonalHomePagePublishActionManager.shared.recordPublishAIFold(isFold: data.isFold)
        reloadActionHandler?()
        if data.isFold {
            eventPostFold()
        } else {
            eventPostUnfold()
        }
    }
    
    @objc
    private func cancelAction() {
        cancelActionHandler?()
    }
    
    // MARK: - Data
    
    func update(data: SOPersonalHomePagePublishData) {
        self.data = data
        updateUI(data: data)
        updateUIConstraints(data: data)
    }
    
    // MARK: - UI
    
    private func setupUI() {
        sperator.backgroundColor = .soul.color(light: .soul.color(hex: 0xDBE5FF), dark: .soul.color(hex: 0xDBE5FF, alpha: 0.1))
        addSubview(sperator)
        titleLabel.text = "根据你的瞬间内容，为你推荐"
        titleLabel.font = .soul.PingFangSC(size: 13)
        titleLabel.textColor = .soul.color(3)
        addSubview(titleLabel)
        foldButton.setTitleColor(.soul.color(hex: 0x8B54FF), for: .normal)
        foldButton.titleLabel?.font = .soul.PingFangSC(size: 13, type: .medium)
        foldButton.setTitle("收起", for: .normal)
        foldButton.addTarget(self, action: #selector(foldAction), for: .touchUpInside)
        addSubview(foldButton)
        cancelButton.setImage(.acc_imageNamed("acc_publish_guide_cell_cancel"), for: .normal)
        cancelButton.addTarget(self, action: #selector(cancelAction), for: .touchUpInside)
        addSubview(cancelButton)
        addSubview(collectionView)
    }
    
    private func setupUIConstraints() {
        sperator.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.trailing.equalTo(-16)
            make.top.equalTo(0)
            make.height.equalTo(1)
        }
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.top.equalTo(sperator.snp.bottom).offset(16)
            make.height.equalTo(18)
        }
        foldButton.snp.makeConstraints { make in
            make.trailing.equalTo(-16)
            make.centerY.equalTo(titleLabel)
        }
        collectionView.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.trailing.equalTo(-16)
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.height.equalTo(collectionLayout.itemSize.height)
            make.bottom.equalTo(-16)
        }
    }
    
    private func updateUI(data: SOPersonalHomePagePublishData) {
        guard let cards = data.cards, !cards.isEmpty else { return }
        items = cards.compactMap { $0.post }
        foldButton.setTitle(data.isFold ? "展开" : "收起", for: .normal)
        collectionView.reloadData()
    }
    
    private func updateUIConstraints(data: SOPersonalHomePagePublishData) {
        if data.styles?.first?.type == "aiDiagnoseRecommend" {
            foldButton.snp.remakeConstraints { make in
                make.trailing.equalTo(cancelButton.snp.leading).offset(-3)
                make.centerY.equalTo(titleLabel)
            }
            cancelButton.snp.remakeConstraints { make in
                make.trailing.equalTo(-5)
                make.centerY.equalTo(titleLabel)
                make.width.height.equalTo(30)
            }
            cancelButton.isHidden = false
        } else {
            foldButton.snp.remakeConstraints { make in
                make.trailing.equalTo(-16)
                make.centerY.equalTo(titleLabel)
            }
            cancelButton.isHidden = true
        }
        if data.isFold {
            titleLabel.snp.remakeConstraints { make in
                make.leading.equalTo(16)
                make.top.equalTo(sperator.snp.bottom).offset(16)
                make.height.equalTo(18)
                make.bottom.equalTo(-16)
            }
            collectionView.isHidden = true
        } else {
            titleLabel.snp.remakeConstraints { make in
                make.leading.equalTo(16)
                make.top.equalTo(sperator.snp.bottom).offset(16)
                make.height.equalTo(18)
            }
            collectionView.snp.makeConstraints { make in
                make.leading.equalTo(16)
                make.trailing.equalTo(-16)
                make.top.equalTo(titleLabel.snp.bottom).offset(12)
                make.height.equalTo(collectionLayout.itemSize.height)
                make.bottom.equalTo(-16)
            }
            collectionView.isHidden = false
        }
    }
    
    private let sperator = UIView()
    private let titleLabel = UILabel()
    private let foldButton = UIButton()
    private let cancelButton = UIButton()
    private lazy var collectionView: UICollectionView = {
        let view = SoulUIRecognizeSimultaneouslyCollectionView(frame: .zero, collectionViewLayout: collectionLayout)
        view.recognizeSimultaneouslyDisabled = true
        view.dataSource = self
        view.delegate = self
        view.backgroundColor = .clear
        view.showsHorizontalScrollIndicator = false
        view.contentInset = .init(top: 0, left: 0, bottom: 0, right: 0)
        view.soul.register(SOPersonalHomePagePublishCellComponentAIRecommandCardCell.self)
        return view
    }()
    private lazy var collectionLayout: UICollectionViewFlowLayout = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.itemSize = .init(width: 132, height: 164)
        layout.minimumLineSpacing = 8
        return layout
    }()
}

extension SOPersonalHomePagePublishCellComponentAIRecommandCard: UICollectionViewDataSource, UICollectionViewDelegate {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        items.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.soul.dequeue(SOPersonalHomePagePublishCellComponentAIRecommandCardCell.self, for: indexPath)
        if let post = items[safe: indexPath.item] {
            cell.update(post: post)
            eventPostExpose(postId: post.postId)
        }
        cell.navigatePostActionHandler = { [weak self] post in
            self?.eventPostClick(postId: post.postId)
            self?.navigatePostActionHandler?(post)
        }
        cell.navigateUserActionHandler = { [weak self] post in
            self?.eventPostUser(postId: post.postId)
            self?.navigateUserActionHandler?(post)
        }
        cell.navigateChatActionHandler = { [weak self] post in
            self?.eventPostChat(postId: post.postId)
            self?.navigateChatActionHandler?(post)
        }
        return cell
    }
}

extension SOPersonalHomePagePublishCellComponentAIRecommandCard {
    
    // Event Track
    
    private func eventPostExpose(postId: String?) {
        var params: [String: Any] = [:]
        params["pId"] = postId
        SoulEvent.eventExpose("PostFb_PostWatch", params: params, pageId: nil, pagePrama: nil)
    }
    
    private func eventPostClick(postId: String?) {
        var params: [String: Any] = [:]
        params["pId"] = postId
        SoulEvent.eventClick("PostFb_clk", params: params, pageId: nil, pagePrama: nil)
    }
    
    private func eventPostChat(postId: String?) {
        var params: [String: Any] = [:]
        params["pId"] = postId
        SoulEvent.eventClick("PostFb_Chat", params: params, pageId: nil, pagePrama: nil)
    }
    
    private func eventPostUser(postId: String?) {
        var params: [String: Any] = [:]
        params["pId"] = postId
        SoulEvent.eventClick("PostFb_Avatar", params: params, pageId: nil, pagePrama: nil)
    }
    
    private func eventPostFold() {
        SoulEvent.eventClick("PostFb_SQ", params: nil, pageId: nil, pagePrama: nil)
    }
    
    private func eventPostUnfold() {
        SoulEvent.eventClick("PostFb_ZK", params: nil, pageId: nil, pagePrama: nil)
    }
}

class SOPersonalHomePagePublishCellComponentAIRecommandCardCell: UICollectionViewCell {
    
    var navigatePostActionHandler: ((SOPost) -> Void)?
    var navigateUserActionHandler: ((SOPost) -> Void)?
    var navigateChatActionHandler: ((SOPost) -> Void)?
    
    private var post: SOPost?
    
    // MARK: - Life Circle
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    @objc
    private func postAction() {
        guard let post = post else { return }
        navigatePostActionHandler?(post)
    }
    
    @objc
    private func userAction() {
        guard let post = post else { return }
        navigateUserActionHandler?(post)
    }
    
    @objc
    private func chatAction() {
        guard let post = post else { return }
        navigateChatActionHandler?(post)
    }
    
    // MARK: - Data
    
    func update(post: SOPost) {
        self.post = post
        updateUI(post: post)
        updateUIConstraints(post: post)
    }
    
    // MARK: - UI
    
    private func setupUI() {
        contentView.layer.cornerRadius = 6
        contentView.layer.masksToBounds = true
        contentView.backgroundColor = .soul.color(0)
        
        imageView.backgroundColor = .soul.color(14)
        imageView.contentMode = .scaleAspectFill
        imageView.layer.masksToBounds = true
        imageView.isUserInteractionEnabled = true
        imageView.addGestureRecognizer(UITapGestureRecognizer.init(target: self, action: #selector(postAction)))
        contentView.addSubview(imageView)
        imageView.addSubview(recommendLabel)
        imageView.addSubview(imageIcon)
        textView.lineBreakMode = .byTruncatingTail
        textView.numberOfLines = 0
        imageView.addSubview(textView)
        
        profileOnlineView.layer.cornerRadius = 3
        profileOnlineView.layer.masksToBounds = true
        profileOnlineView.layer.borderWidth = 1
        profileOnlineView.layer.soul.borderColor(0)
        profileOnlineView.backgroundColor = .soul.color(hex: 0x52D270)
        profileUserView.addSubview(profileOnlineView)
        profileUserView.sizeType = 16
        profileUserView.isUserInteractionEnabled = true
        profileUserView.addGestureRecognizer(UITapGestureRecognizer.init(target: self, action: #selector(userAction)))
        contentView.addSubview(profileUserView)
        profileNameLabel.font = .soul.PingFangSC(size: 11)
        profileNameLabel.textColor = .soul.color(3)
        profileNameLabel.textAlignment = .left
        profileNameLabel.lineBreakMode = .byTruncatingTail
        profileNameLabel.isUserInteractionEnabled = true
        profileNameLabel.addGestureRecognizer(UITapGestureRecognizer.init(target: self, action: #selector(userAction)))
        contentView.addSubview(profileNameLabel)
        chatButton.setImage(.acc_imageNamed("acc_publish_guide_cell_icon_chat"), for: .normal)
        chatButton.addTarget(self, action: #selector(chatAction), for: .touchUpInside)
        contentView.addSubview(chatButton)
        
        profileOnlineView.isHidden = true
    }
    
    private func setupUIConstraints() {
        imageView.snp.makeConstraints { make in
            make.leading.top.trailing.equalToSuperview()
            make.bottom.equalTo(profileUserView.snp.top).offset(-8)
        }
        imageIcon.snp.makeConstraints { make in
            make.top.equalTo(10)
            make.trailing.equalTo(-10)
        }
        textView.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.trailing.equalTo(-16)
            make.top.equalTo(12)
            make.bottom.equalTo(-12)
        }
        recommendLabel.snp.makeConstraints { make in
            make.leading.equalTo(6)
            make.bottom.equalTo(-6)
            make.trailing.lessThanOrEqualTo(-6)
        }
        profileUserView.snp.makeConstraints { make in
            make.leading.equalTo(8)
            make.bottom.equalTo(-6)
            make.width.height.equalTo(18)
        }
        profileNameLabel.snp.makeConstraints { make in
            make.leading.equalTo(profileUserView.snp.trailing).offset(4)
            make.trailing.equalTo(chatButton.snp.leading).offset(-4)
            make.centerY.equalTo(profileUserView)
        }
        profileOnlineView.snp.makeConstraints { make in
            make.trailing.bottom.equalTo(1)
            make.width.height.equalTo(6)
        }
        chatButton.snp.makeConstraints { make in
            make.trailing.equalTo(-8)
            make.centerY.equalTo(profileUserView)
            make.width.height.equalTo(18)
        }
    }
    
    private func updateUI(post: SOPost) {
        imageView.alignment = imageViewAlignment(attachment: post.attachments?.firstObject as? SOAttachment)
        imageView.so_setImage(with: imageViewURL(post: post))
        imageIcon.update(post: post)
        recommendLabel.update(post: post)
        if let content = post.content, !content.isEmpty, post.type == "TEXT" {
            let attributedText = NSMutableAttributedString(attributedString: SAPostTextLayoutFactory.fliterTextFlags(withContent: content, config: textViewAttributes()))
            attributedText.yy_lineBreakMode = .byTruncatingTail
            textView.attributedText = attributedText
        } else {
            textView.attributedText = nil
        }
        
        if let avatarName = post.avatarName {
            profileUserView.setHeadWithName(avatarName, size: 16)
        }
        if let avatarColor = post.avatarColor {
            profileUserView.setHeadBgWithName(avatarColor, size: 16)
        }
        profileNameLabel.text = post.signature
        profileOnlineView.isHidden = !post.authorOnline
    }
    
    private func updateUIConstraints(post: SOPost) {
        
    }
    
    private let imageView = YYAnimatedImageViewAligned()
    private let imageIcon = SOPersonalHomePagePublishCellComponentAIRecommandPostIcon()
    private let textView = UILabel()
    private let recommendLabel = SOPersonalHomePagePublishCellComponentAIRecommandLabel()
    private let profileUserView = SOHeadPortraitView()
    private let profileNameLabel = UILabel()
    private let profileOnlineView = UIView()
    private let chatButton = UIButton()
}

extension SOPersonalHomePagePublishCellComponentAIRecommandCardCell {
    
    private func imageViewAlignment(attachment: SOAttachment?) -> YYAnimatedImageViewAlignedMask {
        guard let attachment = attachment else { return .none }
        
        let imageWidth = attachment.fileWidth.doubleValue
        let imageHeight = attachment.fileHeight.doubleValue
        guard imageWidth > 0, imageHeight > 0 else { return .none }
        
        let imageRatio = imageHeight / imageWidth
        let screenRatio = UIScreen.main.bounds.height / UIScreen.main.bounds.width
        guard imageRatio > 1.5 * screenRatio else { return .none }
        
        let imageSize = CGSize(width: imageWidth, height: imageHeight)
        if YYAnimatedImageViewAligned.pics_isVerticalLongImage(with: imageSize) {
            return .topAspect
        } else {
            return .none
        }
    }
    
    private func imageViewURL(post: SOPost) -> URL? {
        guard let attachment = post.attachments?.firstObject as? SOAttachment else { return nil }
        
        if post.type == "IMAGE" {
            guard let urlString = attachment.fileUrl else { return nil }
            if attachment.fileFormat == "gif" {
                return SoulGetMediaAddressManager.shareInstance().getImageUrl(withPath: urlString, size: imageViewRenderSize(), type: .gif)
            } else {
                return SoulGetMediaAddressManager.shareInstance().getImageUrl(withPath: urlString, size: imageViewRenderSize(), type: .avif)
            }
        } else if post.type == "VIDEO" {
            if let urlString = attachment.videoCoverUrl, !urlString.isEmpty {
                return SoulGetMediaAddressManager.shareInstance().getImageUrl(withPath: urlString, size: imageViewRenderSize(), type: .avif)
            } else if let urlString = attachment.fileUrl {
                let urlString = SoulGetVideoSnapshotManager.getVideoSnapshot(withUrl: urlString, time: attachment.bestFrameTime?.intValue ?? 0, size: imageViewRenderSize())
                if urlString != nil {
                    return URL(string: urlString)
                } else {
                    return nil
                }
            } else {
                return nil
            }
        } else {
            return nil
        }
    }
    
    private func imageViewRenderSize() -> CGSize {
        .zero
    }
    
    private func textViewAttributes() -> SOPostTextLayoutConfig {
        let config = SOPostTextLayoutConfig()
        config.normalTextColor = .soul.color(3)
        config.hightLightTextColor = .soul.color(3)
        config.textFont = .soul.PingFangSC(size: 14, type: .medium)
        config.lineHeight = 20
        config.lineSpace = 2
        config.priorityContentTitle = false
        return config
    }
}

class SOPersonalHomePagePublishCellComponentAIRecommandLabel: UIView {
    
    // MARK: - Life Circle
    
    init() {
        super.init(frame: .zero)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    // MARK: - Data
    
    func update(post: SOPost) {
        updateUI(post: post)
        updateUIConstraints(post: post)
    }
    
    // MARK: - UI
    
    private func setupUI() {
        container.backgroundColor = .black.withAlphaComponent(0.3)
        container.layer.cornerRadius = 9
        container.layer.masksToBounds = true
        addSubview(container)
        container.addSubview(imageView)
        titleLabel.textColor = .white
        titleLabel.font = .soul.PingFangSC(size: 10, type: .medium)
        container.addSubview(titleLabel)
    }
    
    private func setupUIConstraints() {
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        imageView.snp.makeConstraints { make in
            make.leading.equalTo(6)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(12)
        }
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(imageView.snp.trailing).offset(2)
            make.trailing.equalTo(-6)
            make.top.bottom.equalToSuperview()
            make.height.equalTo(18)
        }
        titleLabel.setContentCompressionResistancePriority(.defaultLow, for: .horizontal)
    }
    
    private func updateUI(post: SOPost) {
        if let recReason = post.recTagModel?.recReason, !recReason.isEmpty {
            container.isHidden = false
            titleLabel.text = recReason
        } else {
            container.isHidden = true
        }
    }
    
    private func updateUIConstraints(post: SOPost) {
        
    }
    
    private let container = UIView()
    private let imageView = UIImageView(image: .acc_imageNamed("acc_publish_guide_cell_icon_recommand_label_like"))
    private let titleLabel = UILabel()
}

class SOPersonalHomePagePublishCellComponentAIRecommandPostIcon: UIView {
    
    /// Defines the different styles of post icons
    enum Style {
        /// Image post with count of images
        case image(_ count: Int)
        /// Video post
        case video
        /// Text-only post
        case text
    }
    
    private var style: Style = .image(0)
    
    // MARK: - Life Circle
    
    init() {
        super.init(frame: .zero)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    // MARK: - Data
    
    func update(post: SOPost) {
        if post.type == "IMAGE" {
            let count = post.attachments?.count ?? 0
            style = .image(count)
        } else if post.type == "VIDEO" {
            style = .video
        } else {
            style = .text
        }
        update(style: style)
    }
    
    func update(style: Style) {
        updateUI(style: style)
        updateUIConstraints(style: style)
    }
    
    // MARK: - UI
    
    private func setupUI() {
        addSubview(videoContainer)
        
        imageContainer.backgroundColor = .black.withAlphaComponent(0.3)
        imageContainer.layer.cornerRadius = 4
        imageContainer.layer.masksToBounds = true
        addSubview(imageContainer)
        imageIcon.contentMode = .scaleAspectFit
        imageContainer.addSubview(imageIcon)
        imageCountLabel.textAlignment = .right
        imageCountLabel.textColor = .white
        imageCountLabel.font = .soul.PingFangSC(size: 12)
        imageContainer.addSubview(imageCountLabel)
        
        imageContainer.isHidden = true
    }
    
    private func setupUIConstraints() {
        imageContainer.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(20)
        }
        imageIcon.snp.makeConstraints { make in
            make.leading.equalTo(6)
            make.centerY.equalToSuperview()
        }
        imageCountLabel.snp.remakeConstraints { make in
            make.leading.equalTo(imageIcon.snp.trailing).offset(2)
            make.trailing.equalTo(-4)
            make.centerY.equalToSuperview()
        }
        
        videoContainer.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
            make.width.height.equalTo(20)
        }
    }
    
    private func updateUI(style: Style) {
        switch style {
        case .image(let count):
            imageCountLabel.text = "+\(count-1)"
            imageContainer.isHidden = count <= 1
            videoContainer.isHidden = true
        case .video:
            imageContainer.isHidden = true
            videoContainer.isHidden = false
        case .text:
            imageContainer.isHidden = true
            videoContainer.isHidden = true
        }
    }
    
    private func updateUIConstraints(style: Style) {
        switch style {
        case .image:
            videoContainer.snp.removeConstraints()
            imageContainer.snp.remakeConstraints { make in
                make.edges.equalToSuperview()
                make.height.equalTo(20)
            }
        case .video:
            imageContainer.snp.removeConstraints()
            videoContainer.snp.remakeConstraints { make in
                make.edges.equalToSuperview()
                make.width.height.equalTo(20)
            }
        case .text:
            imageContainer.snp.removeConstraints()
            videoContainer.snp.removeConstraints()
        }
    }
    
    private let videoContainer = UIImageView(image: .acc_imageNamed("acc_publish_guide_cell_icon_video"))
    
    private let imageContainer = UIView()
    private let imageIcon = UIImageView(image: .acc_imageNamed("acc_publish_guide_cell_icon_image"))
    private let imageCountLabel = UILabel()
}
