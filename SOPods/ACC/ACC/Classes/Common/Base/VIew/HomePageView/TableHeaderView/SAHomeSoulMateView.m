//
//  SAHomeSoulMateView.m
//  Soul_New
//
//  Created by 尚书威 on 2020/4/2.
//  Copyright © 2020 Soul. All rights reserved.
//

#import <YYCategories/YYCategories.h>
#import <SoulCoreBase/SoulCoreBase.h>
#import "SAHomeSoulMateView.h"
#import <SoulAssets/SoulAssets.h>
#import <Masonry/Masonry.h>
#import <SoulUIKit/SoulUIKit.h>
#import <SoulHeadPortrait/SoulHeadPortrait.h>

@interface SAHomeSoulMateView ()

@property(nonatomic, strong) UIView *bgView;
@property(nonatomic, strong) SOHeadPortraitView *myHeaderView;
@property(nonatomic, strong) SOHeadPortraitView *soulMaterHeaderView;
@property(nonatomic, strong) UIImageView *arrowImageView;

@end

@implementation SAHomeSoulMateView
- (instancetype)init {
    if (self = [super init]) {
        _type = SAHomeSoulMateView_OldHomePage;
        [self initUI];
    }
    return self;
}

- (instancetype)initWithType:(SAHomeSoulMateViewType)type {
    if (self = [super init]) {
        _type = type;
        [self initUI];
    }
    return self;
}

- (void)setSoulMate:(SOSoulModel *)soulMate {
    _soulMate = soulMate;
    [self updateHead:self.soulMaterHeaderView withColorName:SOStringWith(soulMate.avatarColor, @"") headName:SOStringWith(soulMate.avatarName, @"")];
}

- (void)setPersonalInfo:(NSDictionary *)personalInfo {
    _personalInfo = personalInfo;
    NSString *headName = [personalInfo stringValueForKey:@"avatarName" default:@""];
    NSString *headBgName = [personalInfo stringValueForKey:@"avatarColor" default:@""];
    [self updateHead:self.myHeaderView withColorName:headBgName headName:headName];
}

- (void)initUI {
    self.backgroundColor = GET_COLOR(0);
    [self addSubview:self.bgView];
    [self.bgView addSubview:self.myHeaderView];
    [self.bgView addSubview:self.soulMaterHeaderView];
    [self.bgView addSubview:self.titleLabel];
    [self.bgView addSubview:self.arrowImageView];

    BOOL isOldStyle = (_type == SAHomeSoulMateView_OldHomePage);
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self);
        make.left.mas_equalTo(16);
        make.height.mas_equalTo(isOldStyle ? 46 : 36);
        make.right.mas_equalTo(self.mas_right).offset(-16);
    }];
    [self.myHeaderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(isOldStyle ? 32 : 28);
        make.left.mas_equalTo(isOldStyle ? 12 : 6);
        make.centerY.mas_equalTo(self.bgView);
    }];
    [self.soulMaterHeaderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(isOldStyle ? 32 : 28);
        make.left.mas_equalTo(self.myHeaderView.mas_left).offset(isOldStyle ? 26 : 21);
        make.centerY.mas_equalTo(self.bgView);
    }];
    [self.arrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(isOldStyle ? 16 : 12);
        make.right.mas_equalTo(self.bgView.mas_right).offset(-16);
        make.centerY.mas_equalTo(self.bgView);
    }];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.soulMaterHeaderView.mas_right).offset(isOldStyle ? 12 : 5);
        make.height.mas_equalTo(isOldStyle ? 22 : 18);
        make.right.mas_equalTo(self.arrowImageView.mas_left).offset(-12);
        make.centerY.mas_equalTo(self.bgView);
    }];
    SOWeakIfy(self);
    [self addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithActionBlock:^(id _Nonnull sender) {
        SOStrongIfy(self);
        if (self.clickSoulMateBlock) {
            self.clickSoulMateBlock();
        }
    }]];
}

- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [[UIView alloc] init];
        _bgView.backgroundColor = GET_COLOR(14);
        if (_type == SAHomeSoulMateView_OldHomePage) {
            [_bgView.layer so_setLayerBorderColorTag:4];
            _bgView.layer.borderWidth = 0.5;
            _bgView.layer.cornerRadius = 23;
        } else if (_type == SAHomeSoulMateView_MetaHomePage) {
            _bgView.layer.cornerRadius = 8;
        }
    }
    return _bgView;
}

- (SOHeadPortraitView *)myHeaderView {
    if (!_myHeaderView) {
        _myHeaderView = [[SOHeadPortraitView alloc] init];
        if (_type == SAHomeSoulMateView_OldHomePage) {
            _myHeaderView.sizeType = 28;
            _myHeaderView.headBgView.backgroundColor = GET_COLOR(14);
            _myHeaderView.frame = CGRectMake(0, 0, 32, 32);
            _myHeaderView.backgroundColor = [UIColor whiteColor];
            _myHeaderView.layer.cornerRadius = 16;
        } else if (_type == SAHomeSoulMateView_MetaHomePage) {
            _myHeaderView.sizeType = 24;
            _myHeaderView.headBgView.backgroundColor = GET_COLOR(14);
            _myHeaderView.frame = CGRectMake(0, 0, 28, 28);
            _myHeaderView.backgroundColor = GET_COLOR(14);
            _myHeaderView.layer.cornerRadius = 14;
        }
        _myHeaderView.clipsToBounds = YES;
    }
    return _myHeaderView;
}

- (SOHeadPortraitView *)soulMaterHeaderView {
    if (!_soulMaterHeaderView) {
        _soulMaterHeaderView = [[SOHeadPortraitView alloc] init];
        if (_type == SAHomeSoulMateView_OldHomePage) {
            _soulMaterHeaderView.sizeType = 28;
            _soulMaterHeaderView.headBgView.backgroundColor = GET_COLOR(14);
            _soulMaterHeaderView.frame = CGRectMake(0, 0, 32, 32);
            _soulMaterHeaderView.backgroundColor = [UIColor whiteColor];
            _soulMaterHeaderView.layer.cornerRadius = 16;
        } else if (_type == SAHomeSoulMateView_MetaHomePage) {
            _soulMaterHeaderView.sizeType = 24;
            _soulMaterHeaderView.headBgView.backgroundColor = GET_COLOR(14);
            _soulMaterHeaderView.frame = CGRectMake(0, 0, 28, 28);
            _soulMaterHeaderView.backgroundColor = GET_COLOR(14);
            _soulMaterHeaderView.layer.cornerRadius = 14;
        }
        _soulMaterHeaderView.clipsToBounds = YES;
    }
    return _soulMaterHeaderView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        if (_type == SAHomeSoulMateView_OldHomePage) {
            _titleLabel.font = [UIFont fontWithName:[SOFontDefine fontMediumName] size:16];
            _titleLabel.textColor = GET_COLOR(2);
        } else if (_type == SAHomeSoulMateView_MetaHomePage) {
            _titleLabel.font = FONT(TextFontName, 12);
            _titleLabel.textColor = GET_COLOR(3);
        }
        _titleLabel.text = @"我的Soulmate空间";
    }
    return _titleLabel;
}

- (UIImageView *)arrowImageView {
    if (!_arrowImageView) {
        _arrowImageView = [[UIImageView alloc] init];
        if (_type == SAHomeSoulMateView_OldHomePage) {
            _arrowImageView.image = [UIImage imageNamed:@"homepage_icon_soulmate_into"];
        } else if (_type == SAHomeSoulMateView_MetaHomePage) {
            _arrowImageView.image = [SOColorDefine autoImageWithLImage:[UIImage comm_SVGImageNamed:@"arrow_small_right"] dImage:[UIImage comm_SVGImageNamed:@"arrow_small_right" tintColor:HEXCOLOR(0x686881, 1)]];
        }
    }
    return _arrowImageView;
}

- (void)updateHead:(SOHeadPortraitView *)headView withColorName:(NSString *)colorName headName:(NSString *)headName {
    [headView setHeadBgWithName:colorName size:84 placeholderImage:[UIImage imageNamed:colorName]];
    [headView setHeadWithName:headName size:60 placeholderImage:[UIImage imageNamed:headName]];
}

@end
