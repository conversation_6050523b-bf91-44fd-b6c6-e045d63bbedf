//
//  SOUserBubblingView.m
//  ACC
//
//  Created by xupeng on 2021/11/18.
//

#import "SOUserBubblingView.h"
#import <SOWebImage/SOWebImage.h>
#import <Masonry/Masonry.h>
#import <SoulUIKit/SOUIKit.h>
#import <SoulAssets/UIImage+SoulAssets.h>

@interface SOUserBubblingView ()

@property(nonatomic, strong) SOUserBubblingModel *bubblingModel;

@property(nonatomic, strong) UIImageView *bgImageView;
@property(nonatomic, strong) UIImageView *iconImageView;
@property(nonatomic, strong) UILabel *titleLabel;
@property(nonatomic, strong) UILabel *timeLabel;
@property(nonatomic, strong) UILabel *contentLabel;

@end

@implementation SOUserBubblingView

- (instancetype)initWithFrame:(CGRect)frame bubblingModel:(SOUserBubblingModel *)bubblingModel {
    if (self = [super initWithFrame:frame]) {
        self.bubblingModel = bubblingModel;
        [self setupUI];
        [self playAnimation];
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.bgImageView];

    CGFloat iconTop = self.bubblingModel.desc.length > 0 ? 10 : 19;
    [self.bgImageView addSubview:self.iconImageView];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@10);
        make.top.equalTo(@(iconTop));
        make.height.width.equalTo(@16);
    }];

    [self.bgImageView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconImageView.mas_right).offset(4);
        make.height.equalTo(@16);
        make.centerY.equalTo(self.iconImageView);
    }];

    [self.bgImageView addSubview:self.timeLabel];
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-10);
        make.centerY.equalTo(self.iconImageView);
        make.height.equalTo(@14);
    }];

    if (self.bubblingModel.desc.length > 0) {
        [self.bgImageView addSubview:self.contentLabel];
        [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.iconImageView);
            make.top.equalTo(self.iconImageView.mas_bottom).offset(4);
            make.height.equalTo(@14);
        }];
    }
}

- (void)playAnimation {
    [UIView animateWithDuration:0.3 animations:^{
        self.bgImageView.center = CGPointMake(70, 27);
        self.bgImageView.transform = CGAffineTransformIdentity;
    }];
    [UIView animateWithDuration:0.3 delay:5.3 options:0 animations:^{
        self.bgImageView.center = CGPointMake(0, 54);
        self.bgImageView.transform = CGAffineTransformMakeScale(0.01, 0.01);
    }                completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

#pragma mark -- getter --

- (UIImageView *)bgImageView {
    if (!_bgImageView) {
        _bgImageView = [[UIImageView alloc] initWithFrame:self.bounds];
        _bgImageView.center = CGPointMake(0, 54);
        _bgImageView.transform = CGAffineTransformMakeScale(0.01, 0.01);
        _bgImageView.image = [UIImage acc_imageNamed:@"acc_user_bubbling"];
    }
    return _bgImageView;
}

- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [UIImageView new];
        [_iconImageView so_setImageWithURL:[NSURL URLWithString:self.bubblingModel.mood]];
    }
    return _iconImageView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.text = self.bubblingModel.stateTip;
        _titleLabel.font = [UIFont systemFontOfSize:12.0];
        _titleLabel.textColor = [UIColor whiteColor];
    }
    return _titleLabel;
}

- (UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [UILabel new];
        _timeLabel.text = self.bubblingModel.createStr;
        _timeLabel.font = [UIFont systemFontOfSize:10.0];
        _timeLabel.textColor = UICOLOR_HEX_ALPHA(0xC1C1C1, 1);
    }
    return _timeLabel;
}

- (UILabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [UILabel new];
        _contentLabel.text = self.bubblingModel.desc;
        _contentLabel.font = [UIFont systemFontOfSize:10.0];
        _contentLabel.textColor = UICOLOR_HEX_ALPHA(0xC1C1C1, 1);
    }
    return _contentLabel;
}


@end
