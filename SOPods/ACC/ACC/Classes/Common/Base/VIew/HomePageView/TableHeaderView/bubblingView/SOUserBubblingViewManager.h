//
//  SOUserBubblingViewManager.h
//  ACC
//
//  Created by xupeng on 2021/11/18.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SOUserBubblingViewManager : NSObject


+ (void)getUserBubblingInfo:(NSString *)userId superView:(UIView *)superView isNewVersion:(BOOL)isNewVersion showPoint:(CGPoint)showPoint showUserBubbling:(void (^)(void))showUserBubbling;


@end

NS_ASSUME_NONNULL_END
