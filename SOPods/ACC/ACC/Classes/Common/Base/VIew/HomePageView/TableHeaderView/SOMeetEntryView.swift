//
//  SOMeetEntryView.swift
//  ACC
//
//  Created by Vencent on 2023/1/17.
//

import SnapKit
import SORemoteResKit
import SoulAssets
import SoulUI<PERSON>it
import SOWebImage
import UIKit
import YYCategories

@objc
public class SOMeetEntryView: UIView {
    let arrowView: UIImageView = {
        let imageView = UIImageView()
        imageView.soSetImage(withResKey: "user_home/homepage_meet_banner_arrow")
        return imageView
    }()

    lazy var containerView: UIView = {
        let view = UIView()
        view.clipsToBounds = true
        view.layer.cornerRadius = 10

        view.addSubview(backgroundImageView)
        view.addSubview(normalContainerView)
        view.addSubview(closeButton)
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        normalContainerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        closeButton.snp.makeConstraints { make in
            make.top.right.equalToSuperview()
            make.size.equalTo(CGSize(width: 20, height: 17))
        }
        return view
    }()

    lazy var backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        return imageView
    }()

    lazy var normalContainerView: UIView = {
        let view = UIView()
        view.addSubview(avartView)
        view.addSubview(titleLabel)
        view.addSubview(actionButton)
        avartView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(12)
        }
        titleLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.lessThanOrEqualTo(actionButton.snp.left).offset(-8)
            make.left.equalTo(avartView.snp.right).offset(10)
        }
        actionButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.size.equalTo(CGSize(width: 97, height: 36))
            make.right.equalTo(-12)
        }
        return view
    }()

    lazy var avartView: UIView = {
        let view = UIView()
        view.addSubview(backAvartImageView)
        view.addSubview(frontAvartImageView)
        backAvartImageView.snp.makeConstraints { make in
            make.left.equalTo(0)
            make.top.equalTo(0)
            make.size.equalTo(CGSize(width: 28, height: 28))
        }
        frontAvartImageView.snp.makeConstraints { make in
            make.left.equalTo(backAvartImageView).offset(14)
            make.top.equalTo(backAvartImageView).offset(14)
            make.right.equalTo(0)
            make.bottom.equalTo(0)
            make.size.equalTo(CGSize(width: 28, height: 28))
        }
        return view
    }()

    let frontAvartImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 14
        imageView.layer.borderWidth = 1
        imageView.layer.borderColor = UIColor.white.cgColor
        return imageView
    }()

    let backAvartImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 14
        imageView.layer.borderWidth = 1
        imageView.layer.borderColor = UIColor.white.cgColor
        return imageView
    }()

    let titleLabel: UILabel = {
        let label = UILabel()
        label.numberOfLines = 0
        return label
    }()

    lazy var actionButton: UIView = {
        let view = UIView()
        let tap = UITapGestureRecognizer(target: self, action: #selector(actionButtonOnTap))
        view.addGestureRecognizer(tap)
        let lotView = LOTAnimationView()
        lotView.loopAnimation = true
        lotView
            .setAnimationWithResKey(
                "user_home/homepage_meet_banner_entry_lottie",
                placeholderLottieName: nil
            ) { [weak lotView] success in
                if success {
                    lotView?.play()
                }
            }
        view.addSubview(lotView)
        lotView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        return view
    }()

    lazy var closeButton: UIButton = {
        let button = UIButton()
        button.soSetImage(withResKey: "user_home/homepage_meet_banner_close_icon", for: .normal)
        button.addTarget(self, action: #selector(closeButtonOnTap), for: .touchUpInside)
        return button
    }()

    let onTap: (SOMeetEntryView) -> Void
    let onActionTap: (SOMeetEntryView) -> Void
    let onClose: (SOMeetEntryView) -> Void
    @objc
    public init(
        onTap: @escaping (SOMeetEntryView) -> Void,
        onActionTap: @escaping (SOMeetEntryView) -> Void,
        onClose: @escaping (SOMeetEntryView) -> Void
    ) {
        self.onTap = onTap
        self.onActionTap = onActionTap
        self.onClose = onClose
        super.init(frame: .zero)
        self.initializeUI()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    var bannerData: SOFeelingMeetBannerData?
    @objc
    public func update(data: SOFeelingMeetBannerData) {
        bannerData = data
        guard let bannerData = bannerData else { return }
        if bannerData.superVip {
            self.backgroundImageView.so_setImage(with: URL(string: bannerData.showImage))
            self.normalContainerView.isHidden = true
        } else {
            self.backgroundImageView.soSetImage(withResKey: "user_home/homepage_meet_banner_bg")

            self.normalContainerView.isHidden = false
            let avatarURLs = data.avatarValue.components(separatedBy: ",").map { URL(string: $0) }
            if !avatarURLs.isEmpty {
                self.frontAvartImageView.so_setImage(with: avatarURLs[0])
            }
            if avatarURLs.count > 1 {
                self.backAvartImageView.so_setImage(with: avatarURLs[1])
            }

            let defaultAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 14, weight: .medium),
                .foregroundColor: UIColor(hexString: "282828") ?? .black
            ]
            let attributedText = NSMutableAttributedString(string: "\(data.text) ", attributes: defaultAttributes)

            attributedText.append(.init(string: "\(data.count)", attributes: [
                .font: UIFont.systemFont(ofSize: 18, weight: .medium),
                .foregroundColor: UIColor(hexString: "EFB63E") ?? .yellow
            ]))

            attributedText.append(.init(string: " 次", attributes: defaultAttributes))
            self.titleLabel.attributedText = attributedText
        }
    }

    @objc
    func selfOnTap() {
        self.onTap(self)
    }

    @objc
    func actionButtonOnTap() {
        self.onActionTap(self)
    }

    @objc
    func closeButtonOnTap() {
        self.onClose(self)
    }

    func initializeUI() {
        self.alpha = SOColorDefine.isNightMode() ? 0.7 : 1
        self.addSubview(self.arrowView)
        self.addSubview(self.containerView)
        self.arrowView.snp.makeConstraints { make in
            make.top.equalTo(-4)
            make.right.equalTo(-69)
            make.size.equalTo(CGSize(width: 14, height: 8))
        }
        self.containerView.snp.makeConstraints { make in
            make.top.equalTo(arrowView.snp.bottom).offset(-1)
            make.left.equalTo(16)
            make.right.equalTo(-16)
            make.height.equalTo(62)
            make.bottom.equalTo(-8)
        }
        let tap = UITapGestureRecognizer(target: self, action: #selector(self.selfOnTap))
        self.addGestureRecognizer(tap)
    }
}
