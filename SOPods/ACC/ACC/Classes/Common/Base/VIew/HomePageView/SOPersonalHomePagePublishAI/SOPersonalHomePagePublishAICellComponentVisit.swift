//
//  SOPersonalHomePagePublishAICellComponentVisit.swift
//  ACC
//
//  Created by al<PERSON> on 2025/7/17.
//

import UIKit
import SnapKit
import SoulUIKitExtended

/// Visit component for displaying AI visit suggestions
/// Shows content and post cards with action button and close functionality
class SOPersonalHomePagePublishAICellComponentVisit: UIView {
    
    /// Callback to close the AI visit component
    @objc
    public var closeActionHandler: (() -> Void)?
    
    /// Data model containing visit information
    private var data: SOPersonalHomePagePublishAIData?
    
    // MARK: - Life Circle
    
    init() {
        super.init(frame: .zero)
        
        setupUI()
        setupUIConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Action
    
    /// Handles close button tap to disable AI visit component
    @objc
    private func closeAction() {
        SOPersonalHomePagePublishAIDataStore.shared.disable()
        closeActionHandler?()
    }
    
    /// <PERSON><PERSON> container tap to navigate to AI assistant page
    /// Records analytics event and opens router URL
    @objc
    private func routerAction() {
        guard let routerURL = data?.scoreData?.routerURL, let url = URL(string: routerURL) else { return }
        try? SoulRouterManager.sharedInstance().handleOpen(url, fromVc: nil, appParas: nil)
        
        SoulEvent.eventClick("AI_Home_Assistant_Clk", params: nil, pageId: nil, pagePrama: nil)
    }
    
    // MARK: - Data
    
    /// Updates the component with new AI visit data
    /// - Parameter data: The AI visit data containing content and post information
    func update(data: SOPersonalHomePagePublishAIData) {
        self.data = data
        updateUI(data: data)
        updateUIConstraints(data: data)
    }
    
    // MARK: - UI
    
    /// Sets up the initial UI components and gesture recognizers
    private func setupUI() {
        addSubview(container)
        container.addSubview(contentContainer)
        container.addSubview(postContainer)
        container.addSubview(button)
        container.addSubview(closeButton)
        
        closeButton.addTarget(self, action: #selector(closeAction), for: .touchUpInside)
        container.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(routerAction)))
    }
    
    /// Sets up the UI constraints for all components
    private func setupUIConstraints() {
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        contentContainer.snp.makeConstraints { make in
            make.leading.top.equalToSuperview()
            make.trailing.equalTo(postContainer.snp.leading)
            make.height.equalTo(120)
        }
        postContainer.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview()
            make.width.equalTo(133)
            make.height.equalTo(120)
        }
        button.snp.makeConstraints { make in
            make.leading.equalTo(16)
            make.trailing.equalTo(-16)
            make.top.equalTo(contentContainer.snp.bottom)
            make.height.equalTo(36)
        }
        
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(2)
            make.trailing.equalTo(-2)
            make.width.height.equalTo(24)
        }
    }
    
    /// Updates the UI with the provided AI visit data
    /// - Parameter data: The AI visit data
    private func updateUI(data: SOPersonalHomePagePublishAIData) {
        contentContainer.update(data: data)
        postContainer.update(data: data)
        button.setTitle(data.visitData?.buttonName ?? "查看AI助理", for: .normal)
    }
    
    /// Updates UI constraints based on the provided data
    /// - Parameter data: The AI visit data
    private func updateUIConstraints(data: SOPersonalHomePagePublishAIData) {
        
    }
    
    // MARK: - UI Components
    
    /// Main container view for all content
    private let container = UIView()
    
    /// Container for visit content (score and title)
    private let contentContainer = SOPersonalHomePagePublishAICellComponentVisitContent()
    
    /// Container for visit post cards
    private let postContainer = SOPersonalHomePagePublishAICellComponentVisitCard()
    
    /// Action button to view AI assistant
    private let button = UIButton().then {
        $0.layer.cornerRadius = 18
        $0.layer.masksToBounds = true
        $0.setImage(.acc_imageNamed("acc_publish_ai_score_icon"), for: .normal)
        $0.setTitleColor(.soul.color(1), for: .normal)
        $0.setBackgroundImage(.acc_imageNamed("acc_publish_ai_visit_button_background"), for: .normal)
        $0.titleLabel?.font = .soul.PingFangSC(size: 15, type: .medium)
        $0.isUserInteractionEnabled = false
    }
    
    /// Close button to dismiss the visit component
    private let closeButton = UIButton().then {
        $0.setImage(.acc_imageNamed("acc_publish_ai_cell_close_icon"), for: .normal)
    }
}
