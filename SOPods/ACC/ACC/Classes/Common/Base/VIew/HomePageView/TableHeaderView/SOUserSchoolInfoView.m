//
//  SOUserSchoolInfoView.m
//  ACC
//
//  Created by 尚书威 on 2021/9/17.
//

#import "SOUserSchoolInfoView.h"
#import <SoulAssets/SoulAssets.h>

@interface SOUserSchoolInfoView ()

@property(nonatomic, strong) UIView *containerView;
@property(nonatomic, strong) UIView *iconBgView;
@property(nonatomic, strong) UILabel *iconLabel;
@property(nonatomic, strong) UILabel *schoolLabel;

@end

@implementation SOUserSchoolInfoView

- (instancetype)init {
    if (self = [super init]) {
        [self setupSubViews];
    }
    return self;
}

- (void)setSchoolInfoModel:(SOUserSchoolInfoModel *)schoolInfoModel {
    _schoolInfoModel = schoolInfoModel;
    BOOL hasSchoolInfo = (schoolInfoModel.schoolName.length > 0);
    if (hasSchoolInfo) {
        self.schoolLabel.text = [NSString stringWithFormat:@"校园认证：%@", schoolInfoModel.schoolName];
    }
    self.containerView.hidden = !hasSchoolInfo;
    self.iconBgView.hidden = !hasSchoolInfo;
    self.iconLabel.hidden = !hasSchoolInfo;
    self.schoolLabel.hidden = !hasSchoolInfo;
}

- (void)showSchoolDetail {
    NSString *regularString1 = @"我的学校：";
    NSString *regularString2 = @"\n\n通过校园认证后，其他认证用户访问你的主页时可以看到你的认证学校。\n\n可以前往设置-隐私，设置是否对其他校园认证用户可见";
    NSString *contentString = [NSString stringWithFormat:@"%@%@%@", regularString1, self.schoolInfoModel.schoolName, regularString2];
    SOAlertAction *confirmAction = [SOAlertAction actionWithTitle:@"好的" action:^{

    }];
    confirmAction.isPromot = YES;
    SOAlertView *alertView = [[SOAlertView alloc] initWithTitle:@"学校认证" content:contentString topImage:nil alertActions:@[confirmAction] subAction:nil];
    [alertView show];
    [alertView adaptDarkMode];
}

- (void)setupSubViews {
    [self addSubview:self.containerView];
    [self.containerView addSubview:self.iconBgView];
    [self.iconBgView addSubview:self.iconLabel];
    [self.containerView addSubview:self.schoolLabel];

    self.containerView.hidden = YES;
    self.iconBgView.hidden = YES;
    self.iconLabel.hidden = YES;
    self.schoolLabel.hidden = YES;

    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16);
        make.right.mas_equalTo(-16);
        make.height.mas_equalTo(32);
        make.top.mas_equalTo(0);
    }];
    [self.iconBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(14, 14));
        make.left.mas_equalTo(8);
        make.centerY.mas_equalTo(self.containerView);
    }];
    [self.iconLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.iconBgView);
    }];
    [self.schoolLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.iconBgView.mas_right).offset(4);
        make.centerY.mas_equalTo(self.containerView);
        make.right.mas_equalTo(-8);
        make.height.mas_equalTo(30);
    }];
}

- (UIView *)containerView {
    if (!_containerView) {
        _containerView = [[UIView alloc] init];
        _containerView.backgroundColor = GET_COLOR(14);
        _containerView.layer.cornerRadius = 16;
        _containerView.layer.masksToBounds = YES;
        [_containerView.layer so_setLayerBorderColorTag:4];
        _containerView.layer.borderWidth = 0.5;
        _containerView.layer.masksToBounds = YES;
        SOWeakIfy(self);
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithActionBlock:^(id _Nonnull sender) {
            SOStrongIfy(self);
            if (self.isSelfHomePage) {
                [self showSchoolDetail];
            }
        }];
        [_containerView addGestureRecognizer:tap];
    }
    return _containerView;
}

- (UIView *)iconBgView {
    if (!_iconBgView) {
        _iconBgView = [[UIView alloc] init];
        _iconBgView.backgroundColor = GET_COLOR(1);
        _iconBgView.layer.cornerRadius = 7;
        _iconBgView.layer.masksToBounds = YES;
    }
    return _iconBgView;
}

- (UILabel *)iconLabel {
    if (!_iconLabel) {
        _iconLabel = [[UILabel alloc] init];
        _iconLabel.textColor = GET_COLOR(0);
        _iconLabel.text = @"校";
        _iconLabel.font = FONT(TextFontName_Bold, 8);
        _iconLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _iconLabel;
}

- (UILabel *)schoolLabel {
    if (!_schoolLabel) {
        _schoolLabel = [[UILabel alloc] init];
        _schoolLabel.font = FONT(TextFontName, 13);
        _schoolLabel.textColor = GET_COLOR(2);
        _schoolLabel.text = @"校园认证：";
    }
    return _schoolLabel;
}

@end

@interface SOMetaUserSchoolInfoView ()

@property(nonatomic, strong) UIView *containerView;
@property(nonatomic, strong) UIImageView *iconImageView;
@property(nonatomic, strong) UILabel *schoolLabel;

@end

@implementation SOMetaUserSchoolInfoView

- (instancetype)init {
    if (self = [super init]) {
        [self setupSubViews];
    }
    return self;
}

- (void)setSchoolInfoModel:(SOUserSchoolInfoModel *)schoolInfoModel {
    _schoolInfoModel = schoolInfoModel;
    BOOL hasSchoolInfo = (schoolInfoModel.schoolName.length > 0);
    if (hasSchoolInfo) {
        self.schoolLabel.text = [NSString stringWithFormat:@"校园认证：%@", schoolInfoModel.schoolName];
    }
    self.containerView.hidden = !hasSchoolInfo;
    self.iconImageView.hidden = !hasSchoolInfo;
    self.schoolLabel.hidden = !hasSchoolInfo;
}

- (void)showSchoolDetail {
    NSString *regularString1 = @"我的学校：";
    NSString *regularString2 = @"\n\n通过校园认证后，其他认证用户访问你的主页时可以看到你的认证学校。\n\n可以前往设置-隐私，设置是否对其他校园认证用户可见";
    NSString *contentString = [NSString stringWithFormat:@"%@%@%@", regularString1, self.schoolInfoModel.schoolName, regularString2];
    SOAlertAction *confirmAction = [SOAlertAction actionWithTitle:@"好的" action:^{

    }];
    confirmAction.isPromot = YES;
    SOAlertView *alertView = [[SOAlertView alloc] initWithTitle:@"学校认证" content:contentString topImage:nil alertActions:@[confirmAction] subAction:nil];
    [alertView show];
    [alertView adaptDarkMode];
}

- (void)setupSubViews {
    [self addSubview:self.containerView];
    [self.containerView addSubview:self.iconImageView];
    [self.containerView addSubview:self.schoolLabel];

    self.containerView.hidden = YES;
    self.iconImageView.hidden = YES;
    self.schoolLabel.hidden = YES;

    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16);
        make.right.mas_equalTo(-16);
        make.height.mas_equalTo(26);
        make.top.mas_equalTo(0);
    }];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(14, 14));
        make.left.mas_equalTo(8);
        make.centerY.mas_equalTo(self.containerView);
    }];
    [self.schoolLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.iconImageView.mas_right).offset(6);
        make.centerY.mas_equalTo(self.containerView);
        make.right.mas_equalTo(-12);
        make.height.mas_equalTo(18);
    }];
}

- (UIView *)containerView {
    if (!_containerView) {
        _containerView = [[UIView alloc] init];
        _containerView.backgroundColor = GET_COLOR(14);
        _containerView.layer.cornerRadius = 13;
        _containerView.layer.masksToBounds = YES;
        SOWeakIfy(self);
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithActionBlock:^(id _Nonnull sender) {
            SOStrongIfy(self);
            if (self.isSelfHomePage) {
                [self showSchoolDetail];
            }
        }];
        [_containerView addGestureRecognizer:tap];
    }
    return _containerView;
}

- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] init];
        _iconImageView.image = [UIImage acc_imageNamed:@"icon_meta_homepage_school"];
    }
    return _iconImageView;
}

- (UILabel *)schoolLabel {
    if (!_schoolLabel) {
        _schoolLabel = [[UILabel alloc] init];
        _schoolLabel.font = FONT(TextFontName, 11);
        _schoolLabel.textColor = GET_COLOR(2);
        _schoolLabel.text = @"校园认证：";
    }
    return _schoolLabel;
}

@end
