//
//  SOPersonalHomePagePublishCellLayout.swift
//  ACC
//
//  Created by 1021500723 on 2024/1/3.
//

import UIKit
import Soul<PERSON><PERSON>itExtended
import YYText

/// Layout class for calculating dimensions of the personal homepage publish cell
public class SOPersonalHomePagePublishCellLayout: SOPersonalHomePageCellLayout {
    
    /// Data model containing the publish cell content
    @objc
    public var data: SOPersonalHomePagePublishData? {
        didSet {
            layoutHeight()
        }
    }
    
    /// Performs the layout calculation
    /// - Returns: Self for method chaining
    @discardableResult
    public override func layout() -> Any {
        layoutHeight()
        return self
    }
    
    /// Calculates the height of the cell based on its content type and state
    private func layoutHeight() {
        height = 0
        guard let data = data, let style = data.styles?.first, let type = style.type else { return }
        
        // Handle different content types
        if type == "aiDiagnose" {
            // Calculate height for AI diagnosis type
            let contentHeight: CGFloat
            if let content = style.contents?.first?.title {
                // Calculate text height using YYTextLayout
                let attributedString = NSMutableAttributedString(string: content, attributes: [.font : UIFont.soul.PingFangSC(size: 13)])
                let layout = YYTextLayout(containerSize: .init(width: UIScreen.soul.width - 64, height: CGFloat.greatestFiniteMagnitude), text: attributedString)
                contentHeight = CGFloat((layout?.rowCount ?? 3) * 20)
            } else {
                // Default height if no content
                contentHeight = 60
            }
            // Base height (8 padding + 98 for header) + dynamic content height
            height = 8 + 98 + contentHeight
            
            // Handle AI guide exposure and card visibility
            if let card = data.cards?.first, let post = card.post {
                SOPersonalHomePagePublishActionManager.shared.recordPublishAIGuideExpose(post: post)
                if SOPersonalHomePagePublishActionManager.shared.triggerTrapForAI() {
                    data.cards = nil
                }
            }
            
            // Add additional height for cards section if present
            if let cards = data.cards, !cards.isEmpty {
                data.isFold = SOPersonalHomePagePublishActionManager.shared.triggerTrapForAIFold()
                if data.isFold {
                    // Height for folded state
                    height += 51
                } else {
                    // Height for expanded state
                    height += 227
                }
            }
            
        } else if type == "aiDiagnoseRecommend" {
            // Add additional height for cards section if present
            if let cards = data.cards, !cards.isEmpty {
                data.isFold = SOPersonalHomePagePublishActionManager.shared.triggerTrapForAIFold()
                if data.isFold {
                    // Height for folded state
                    height = 8 + 51
                } else {
                    // Height for expanded state
                    height = 8 + 227
                }
            }
        } else if type == "socialCard" {
            // Height for social card type (padding + header)
            height = 8 + 98
        } else {
            // Default height for other types
            height = 8 + 76
        }
    }
}
