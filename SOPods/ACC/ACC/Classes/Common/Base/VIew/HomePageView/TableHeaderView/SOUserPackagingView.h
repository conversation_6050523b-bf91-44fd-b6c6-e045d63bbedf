
//  SOUserPackagingView.h
//  ACC
//  Created by liang on 2024/9/19.


#import <UIKit/UIKit.h>
#import "SOUserPackModel.h"
NS_ASSUME_NONNULL_BEGIN

@interface SOUserPackagingView : UIView

@property (nonatomic,strong) UIImageView * leftImageView;
@property (nonatomic,strong) UILabel * titleLabel;
@property (nonatomic,strong) UIButton * closeBtn;
@property (nonatomic,strong) UIButton * topShowBtn;
@property (nonatomic,strong) UIButton * bottomShowBtn;
@property (nonatomic,strong) UILabel * tipsLabel;
@property (nonatomic,strong) UILabel * contentLabel;
@property (nonatomic,strong) UIImageView * contentImageView;
@property (nonatomic,strong) UIImageView * rightArrowImageView;
@property (nonatomic,strong) UIImageView * bottomArrowImageView;
@property (nonatomic,assign) BOOL newUserPage;
@property (nonatomic,assign) BOOL otherPeoplePage;
@property (nonatomic,assign) CGFloat viewHeight;
@property (nonatomic,assign) CGFloat contentHeight;
@property (nonatomic,assign) BOOL display;
@property (nonatomic,assign) BOOL showImage;
@property (nonatomic,assign) BOOL canUpdateDescription;


-(void)updateUIWithData:(SOUserPackModel*)data;
-(void)updateUIWithOtherPeopleData:(SOUserPackModel*)data otherID:(NSString*)otherID;

@property(nonatomic, copy) void (^closePackBlock)(void);
@property(nonatomic, copy) void (^showPackBlock)(void);
@property(nonatomic, copy) void (^updateDescriptionBlock)(BOOL canUpdate);

@end

NS_ASSUME_NONNULL_END
