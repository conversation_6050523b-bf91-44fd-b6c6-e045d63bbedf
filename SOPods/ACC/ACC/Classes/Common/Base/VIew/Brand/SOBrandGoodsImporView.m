//
//  SOBrandGoodsImporView.m
//  Soul_New
//
//  Created by 刘强 on 2020/12/10.
//  Copyright © 2020 Soul. All rights reserved.
//

#import "SOBrandGoodsImporView.h"
#import <SoulUIKit/SoulUIKit.h>

@interface SOBrandGoodsImporView ()

@property(nonatomic, strong) UIView *bgView;
@property(nonatomic, strong) UIImageView *iconImage;
@property(nonatomic, strong) UILabel *contentLabel;
@property(nonatomic, strong) UIImageView *gotoImage;

@end

@implementation SOBrandGoodsImporView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self createUI];
    }
    return self;
}

#pragma mark - Click

- (void)tapBgView {
    if (self.jumpMallShopVCBlock) {
        self.jumpMallShopVCBlock();
    }
}

#pragma mark -UI

- (void)createUI {
    self.backgroundColor = GET_COLOR(0);
    [self addSubview:self.bgView];
    [self.bgView addSubview:self.iconImage];
    [self.bgView addSubview:self.contentLabel];
    [self.bgView addSubview:self.gotoImage];
}

- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [[UIView alloc] initWithFrame:CGRectMake(16, 16, self.width - 32, 48)];
        _bgView.backgroundColor = GET_COLOR(14);
        _bgView.layer.masksToBounds = YES;
        _bgView.layer.cornerRadius = 24.f;
        [_bgView.layer so_setLayerBorderColorTag:4];
        _bgView.layer.borderWidth = 1.f;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapBgView)];
        [_bgView addGestureRecognizer:tap];
    }
    return _bgView;
}

- (UIImageView *)iconImage {
    if (!_iconImage) {
        _iconImage = [[UIImageView alloc] initWithFrame:CGRectMake(18, 10, 26, 26)];
        _iconImage.image = [UIImage imageNamed:@"perspage_icon_brand_goodsImpor"];
    }
    return _iconImage;
}

- (UILabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [[UILabel alloc] initWithFrame:CGRectMake(54, 13.5, 100, 21)];
        _contentLabel.font = FONT(TextFontName, 15);
        _contentLabel.textColor = GET_COLOR(2);
        _contentLabel.text = @"品牌好物";
    }
    return _contentLabel;
}

- (UIImageView *)gotoImage {
    if (!_gotoImage) {
        _gotoImage = [[UIImageView alloc] initWithFrame:CGRectMake(_bgView.width - 12 - 20, (_bgView.height - 12) / 2.f, 12, 12)];
        _gotoImage.image = SOUL_IMAGE_COMMON(@"focusList_cell_icon_more");
    }
    return _gotoImage;
}


@end
