//
//  SOBrandCertificationView.m
//  Soul_New
//
//  Created by 刘强 on 2020/12/10.
//  Copyright © 2020 Soul. All rights reserved.
//

#import "SOBrandCertificationView.h"
#import <SoulUIKit/SoulUIKit.h>
#import <SoulAssets/SoulAssets.h>

@interface SOBrandCertificationView ()

@property(nonatomic, strong) UIImageView *iconImage;
@property(nonatomic, strong) UILabel *contentLabel;

@end

@implementation SOBrandCertificationView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self createUI];
    }
    return self;
}

- (void)createUI {
    [self addSubview:self.iconImage];
    [self addSubview:self.contentLabel];
}

- (void)updateUIWithContent:(NSString *)content isSelfHomePage:(BOOL)isSelfHomePage{
    NSMutableAttributedString *atrbString = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@ ",content]];
    atrbString.yy_color = UIColorFromRGB(0xffffff);
    if (!isSelfHomePage) {
        NSTextAttachment *attch = [[NSTextAttachment alloc] init];
        attch.image = [UIImage acc_imageNamed:@"brandCertification_goto_icon"];
        attch.bounds = CGRectMake(0, 0, 6, 10);
        NSAttributedString *string = [NSAttributedString attributedStringWithAttachment:attch];
        [atrbString appendAttributedString:string];
    }
    _contentLabel.attributedText = atrbString;
    [_contentLabel sizeToFit];
    _contentLabel.height = 20;
    _iconImage.x = (self.width - _contentLabel.width - _iconImage.width - 4) / 2.f;
    _contentLabel.x = CGRectGetMaxX(_iconImage.frame) + 4;
}


- (UIImageView *)iconImage {
    if (!_iconImage) {
        _iconImage = [[UIImageView alloc] initWithFrame:CGRectMake(0, (self.height - 18) / 2.f, 18, 18)];
        _iconImage.image = [UIImage imageNamed:@"perspage_icon_brand"];
    }
    return _iconImage;
}

- (UILabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, (self.height - 20) / 2.f, 0, 20)];
        _contentLabel.font = FONT(TextFontName, 16);
        _contentLabel.textColor = [UIColor customColorWithString:@"ffffff"];
    }
    return _contentLabel;
}

@end
