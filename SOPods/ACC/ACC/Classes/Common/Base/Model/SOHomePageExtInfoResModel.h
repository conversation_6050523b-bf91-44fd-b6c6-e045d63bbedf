//
//  SOHomePageExtInfoResModel.h
//  ACC
//
//  Created by 尚书威 on 2022/8/9.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SOHomePageAigcRankModel : NSObject

@property (nonatomic, copy, nullable) NSString *jumpUrl;  //虚拟人榜单 跳转链接
@property (nonatomic, assign)int rank;      //虚拟人榜单 排名
@property (nonatomic, copy, nullable) NSString *iconUrl;
@property (nonatomic, copy, nullable) NSString *title;
@property (nonatomic, copy, nullable) NSString *bgIconUrl;
@property (nonatomic, copy, nullable) NSString *nightBgIconUrl;
@end

@interface SOHomePageMedalModel : NSObject

@property (nonatomic, copy) NSString *medalId;      /// 勋章id
@property (nonatomic, assign) NSInteger level;      /// 勋章等级
@property (nonatomic, copy) NSString *name;         /// 勋章名称
@property (nonatomic, copy) NSString *iconUrl;      /// 图标链接
@property (nonatomic, copy) NSString *linkUrl;      /// 跳转链接
@property (nonatomic, copy) NSString *bizType;      /// 类型 basic-基础 vertical-垂类 activity-活动

@end

@interface SOHomePageVipMedal : NSObject

@property (nonatomic, copy) NSString *medalId;      /// 勋章id
@property (nonatomic, assign) NSInteger level;      /// 勋章等级
@property (nonatomic, copy) NSString *name;         /// 勋章名称
@property (nonatomic, copy) NSString *iconUrl;      /// 图标链接
//@property (nonatomic, copy) NSString *linkUrl;      /// 跳转链接
@property (nonatomic, copy) NSString *bizType;      /// 类型 basic-基础 vertical-垂类 activity-活动
@property (nonatomic, copy) NSString *onOnclickUrl; ///点击跳转地址

@end


@interface SOHomePageJudgeModel : NSObject

@property (nonatomic, copy) NSString *jumpUrl; 
@property (nonatomic, assign) BOOL show;


@end


/// 专属会员 对象
@interface SOHomePageAiPartnerVipModel : NSObject

@property (nonatomic, copy) NSString *jumpUrl; // 跳转 Url
@property (nonatomic, copy) NSString *iconUrl; // 图标
@property (nonatomic, copy) NSString *bgIconUrl; // 日间背景图
@property (nonatomic, copy) NSString *nightBgIconUrl; // 夜间背景图
@property (nonatomic, copy) NSString *title; // 标题文案

@end


@interface SOHomePageExtInfoResModel : NSObject

@property (nonatomic, strong) NSArray <SOHomePageMedalModel *>*medals;
@property (nonatomic, copy) NSString *medalWallUrl;             // 勋章墙跳转链接
@property (nonatomic, strong) SOHomePageAigcRankModel *intimacyRankModel;// 虚拟人榜单
@property (nonatomic, strong, nullable) SOHomePageAiPartnerVipModel *aiPartnerVipModel; // 虚拟人专属会员入口
@property (nonatomic, assign, readonly) BOOL hiddenSSR;         // 佩戴有超过6级的垂类勋章 或 佩戴勋章大于等于两个  隐藏SSR展示


@end

@interface SODigitalMuseumInfoModel : NSObject

@property (nonatomic, strong) NSString *iconUrl;
@property (nonatomic, strong) NSString *jumpUrl;
@property (nonatomic, assign) NSInteger level;

/// 新版个人主页数字藏品展馆入口配置项
/// 等级动画资源
@property (nonatomic, strong) NSString *animUrl;
/// 等级色值
@property (nonatomic, strong) NSString *color;
/// 底框资源
@property (nonatomic, strong) NSString *barUrl;
/// 箭头资源
@property (nonatomic, strong) NSString *arrowUrl;
/// 是否最近编辑过 true:最近编辑过 false:最近没有编辑过
@property (nonatomic, assign) BOOL recentEditFlag;
/// 推广资源位最近更新标记 true:最近更新过 false:最近未更新
@property (nonatomic, assign) BOOL promoteFlag;
/// 推广资源位更新时间
@property (nonatomic, strong) NSString *promoteUpdateTime;

@property (nonatomic, strong) NSString *showWord;

@property (nonatomic, assign) BOOL showNew;

@end

@interface SODigitalMuseumModel : NSObject

@property (nonatomic, assign) BOOL showMuseum;
@property (nonatomic, strong) SODigitalMuseumInfoModel *museumInfo;

@end

@interface SOACCPublishEntryResponseCardData : NSObject

@property (nonatomic, strong) NSString *id;
@property (nonatomic, strong, nullable) NSString *type;
@property (nonatomic, strong) NSString *title;
@property (nonatomic, strong, nullable) NSString *desc;
@property (nonatomic, strong, nullable) NSString *titleIcon;

@end

@interface SOACCPublishEntryResponseData : NSObject

@property (nonatomic, strong) NSString *title;
@property (nonatomic, strong) NSArray<SOACCPublishEntryResponseCardData *> *guideDTOList;
@property (nonatomic, strong, nullable) NSString *requestId;

@end

@interface SOACCPublishCreatGroupModel : NSObject

@property (nonatomic, copy) NSString * type;
@property (nonatomic, copy) NSString * code;
@property (nonatomic, copy) NSString * name;
@property (nonatomic, copy) NSString * iconUrl;
@property (nonatomic, copy) NSString * jumpUrl;
@property (nonatomic, copy) NSString * tuid;

@end

NS_ASSUME_NONNULL_END
