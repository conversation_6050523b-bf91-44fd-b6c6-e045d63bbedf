//
//  SOUserExtensionInfo.h
//  Soul_New
//
//  Created by HG on 2019/1/4.
//  Copyright © 2019 Soul. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UserAvatarModel.h"

@interface SOUserExtensionInfo : NSObject

@property(nonatomic, assign) BOOL displayOldAvatar;
/// 是否是限时头像
@property(nonatomic, assign) BOOL limitedTimeAvatar;
/// 当前头像过期剩余时间（秒）
@property(nonatomic, assign) NSInteger remainingTime;
/// 该头像是否有永久头像
@property(nonatomic, assign) BOOL hasForeverAvatar;
/// 用户是否已拥有该头像的永久头像
@property(nonatomic, assign) BOOL hasForeverAvatarPrivilege;
/// 当前头像商品 ID
@property(nonatomic, copy, nullable) NSString *commodityIdentity;

@property(nonatomic, strong, nullable) NSArray *avatarModelList;
@property(nonatomic, copy) NSString *avatarURLString;        // 新自定义头像链接
@property(nonatomic, copy, nullable) NSString *popupAvatarUrl;
@property(nonatomic, copy, nullable) NSString *avatarSVGInfoJsonString;// 头像的SVG还原信息(客户端用不上,传递给H5使用)
@property(nonatomic, strong, nullable) UserAvatarModel *almostExpiredAvatar;     // 即将过期的头像

@property(nonatomic, assign) NSInteger avatarCount;//虚拟人数量
@property(nonatomic, assign) BOOL dressUpNewUser;//是否是3D装扮新用户
@property(nonatomic, assign) BOOL hasMuseum;//是否有藏品馆
@property(nonatomic, strong, nullable) NSNumber* from; // 1 AI
@property(nonatomic, copy, nullable) NSString *jumpAiActivityUrl; /// AI 头像跳转链接

/// 活动入口图标
@property(nonatomic, copy, nullable) NSString *activityCornerUrl;
/// 活动入口跳转链接
@property(nonatomic, copy, nullable) NSString *jumpActivityUrl;

/// UGC 表情ID
@property(nonatomic, copy, nullable) NSString *ugcExpressionId;
/// UGC 表情URL
@property(nonatomic, copy, nullable) NSString *ugcExpressionUrl;

@end

