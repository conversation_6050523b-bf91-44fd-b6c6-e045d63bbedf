//
//  SOHomePageExtInfoResModel.m
//  ACC
//
//  Created by 尚书威 on 2022/8/9.
//

#import "SOHomePageExtInfoResModel.h"
#import <SoulCoreBase/SoulCoreBase.h>
@implementation SOHomePageAigcRankModel

@end

@implementation SOHomePageAiPartnerVipModel

@end

@implementation SOHomePageMedalModel

+ (nullable NSDictionary<NSString *, id> *)modelCustomPropertyMapper {
    return @{
        @"medalId": @"id",
    };
}

@end

@implementation SOHomePageVipMedal

+ (nullable NSDictionary<NSString *, id> *)modelCustomPropertyMapper {
    return @{
        @"medalId": @"id",
    };
}

@end

@implementation SOHomePageJudgeModel


@end




@implementation SOHomePageExtInfoResModel

+ (nullable NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    return @{
        @"medals": [SOHomePageMedalModel class],
        @"intimacyRankModel": [SOHomePageAigcRankModel class],
        @"aiPartnerVipModel": [SOHomePageAiPartnerVipModel class]
    };
}

- (BOOL)hiddenSSR {
    __block BOOL shouldHiddenSSR = NO;
    if (self.medals.count >= 2) {
        shouldHiddenSSR = YES;
        return shouldHiddenSSR;
    }
    /// 个人主页去除此限制
//    [self.medals enumerateObjectsUsingBlock:^(SOHomePageMedalModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
//        if ([obj.bizType isEqualToString:@"vertical"] && obj.level >= 6) {
//            shouldHiddenSSR = YES;
//            *stop = YES;
//        }
//    }];
    return shouldHiddenSSR;
}

@end

@implementation SODigitalMuseumInfoModel

@end

@implementation SODigitalMuseumModel

- (NSDictionary *)modelCustomWillTransformFromDictionary:(NSDictionary *)dic {
    if ([dic objectForKey:@"rankValue"]) {
        
        NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithDictionary:dic];
        dict[@"showMuseum"] = @(!SOGlobalPropertyManager.sharedInstance.isInReview);
        
        NSInteger level = SOIntegerWith(dic[@"rankValue"], 0);
        NSMutableDictionary* entranceConfig = [NSMutableDictionary dictionaryWithDictionary:SODictionaryWith(dic[@"entranceConfig"], @{})];
        entranceConfig[@"level"] = @(level);
        dict[@"museumInfo"] = entranceConfig;
        return dict;
    }else {
        NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithDictionary:dic];
        if (SOGlobalPropertyManager.sharedInstance.isInReview) {
            dict[@"showMuseum"] = @(NO);
        }
        return dict;
    }
}

@end

@implementation SOACCPublishEntryResponseCardData

@end

@implementation SOACCPublishEntryResponseData

+ (nullable NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    return @{
        @"guideDTOList": [SOACCPublishEntryResponseCardData class],
    };
}

@end

@implementation SOACCPublishCreatGroupModel

@end
