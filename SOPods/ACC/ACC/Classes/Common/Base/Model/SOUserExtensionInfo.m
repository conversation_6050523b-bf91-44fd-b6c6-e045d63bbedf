//
//  SOUserExtensionInfo.m
//  Soul_New
//
//  Created by HG on 2019/1/4.
//  Copyright © 2019 Soul. All rights reserved.
//
#import "SOUserExtensionInfo.h"

@implementation SOUserExtensionInfo

+ (nullable NSDictionary<NSString *, id> *)modelCustomPropertyMapper {
    return @{
        @"avatarURLString": @"avatarUrl",
        @"avatarSVGInfoJsonString": @"avatarParams",
        @"avatarSVGInfoJsonString": @"avatarParams",
        @"from": @"fromAi",
    };
}

+ (nullable NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    return @{
        @"avatarModelList": [UserAvatarModel class],
    };
}

@end
