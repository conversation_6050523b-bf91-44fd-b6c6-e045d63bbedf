{"v": "5.6.9", "fr": 60, "ip": 0, "op": 480, "w": 96, "h": 36, "nm": "导出 - 3个头像 - 7切图", "ddd": 0, "assets": [{"id": "image_0", "w": 36, "h": 36, "u": "images/", "p": "SOPlanetChatRoomUserHead_border.png", "e": 0}, {"id": "image_1", "w": 36, "h": 36, "u": "images/", "p": "img_1.png", "e": 0}, {"id": "image_2", "w": 36, "h": 36, "u": "images/", "p": "img_2.png", "e": 0}, {"id": "image_3", "w": 36, "h": 36, "u": "images/", "p": "img_3.png", "e": 0}, {"id": "image_4", "w": 36, "h": 36, "u": "images/", "p": "img_4.png", "e": 0}, {"id": "image_5", "w": 36, "h": 36, "u": "images/", "p": "img_5.png", "e": 0}, {"id": "image_6", "w": 36, "h": 36, "u": "images/", "p": "img_6.png", "e": 0}, {"id": "image_7", "w": 36, "h": 36, "u": "images/", "p": "img_7.png", "e": 0}], "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "边框.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [100]}, {"t": 60, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [18, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"t": 60, "s": [-12, 18, 0]}], "ix": 2}, "a": {"a": 0, "k": [18, 18, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "1.png", "cl": "png", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [100]}, {"t": 60, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [18, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"t": 60, "s": [-12, 18, 0]}], "ix": 2}, "a": {"a": 0, "k": [18, 18, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "边框.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 150, "s": [100]}, {"t": 180, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [48, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [18, 18, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [18, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"t": 180, "s": [-12, 18, 0]}], "ix": 2}, "a": {"a": 0, "k": [18, 18, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "2.png", "cl": "png", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 150, "s": [100]}, {"t": 180, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [48, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [18, 18, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [18, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"t": 180, "s": [-12, 18, 0]}], "ix": 2}, "a": {"a": 0, "k": [18, 18, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "边框.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 270, "s": [100]}, {"t": 300, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [78, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [48, 18, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [48, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [18, 18, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [18, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"t": 300, "s": [-12, 18, 0]}], "ix": 2}, "a": {"a": 0, "k": [18, 18, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "3.png", "cl": "png", "refId": "image_3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 270, "s": [100]}, {"t": 300, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [78, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [48, 18, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [48, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [18, 18, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [18, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"t": 300, "s": [-12, 18, 0]}], "ix": 2}, "a": {"a": 0, "k": [18, 18, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 2, "nm": "边框.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 390, "s": [100]}, {"t": 420, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [78, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [48, 18, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [48, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 300, "s": [18, 18, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 390, "s": [18, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"t": 420, "s": [-12, 18, 0]}], "ix": 2}, "a": {"a": 0, "k": [18, 18, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 2, "nm": "4.png", "cl": "png", "refId": "image_4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 390, "s": [100]}, {"t": 420, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [78, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [48, 18, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [48, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 300, "s": [18, 18, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 390, "s": [18, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"t": 420, "s": [-12, 18, 0]}], "ix": 2}, "a": {"a": 0, "k": [18, 18, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 2, "nm": "边框.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 150, "s": [0]}, {"t": 180, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [78, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 300, "s": [48, 18, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 390, "s": [48, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"t": 420, "s": [18, 18, 0]}], "ix": 2}, "a": {"a": 0, "k": [18, 18, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 2, "nm": "5.png", "cl": "png", "refId": "image_5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 150, "s": [0]}, {"t": 180, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 270, "s": [78, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 300, "s": [48, 18, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 390, "s": [48, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"t": 420, "s": [18, 18, 0]}], "ix": 2}, "a": {"a": 0, "k": [18, 18, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 2, "nm": "边框.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 270, "s": [0]}, {"t": 300, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 390, "s": [78, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"t": 420, "s": [48, 18, 0]}], "ix": 2}, "a": {"a": 0, "k": [18, 18, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 2, "nm": "6.png", "cl": "png", "refId": "image_6", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 270, "s": [0]}, {"t": 300, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 390, "s": [78, 18, 0], "to": [-5, 0, 0], "ti": [5, 0, 0]}, {"t": 420, "s": [48, 18, 0]}], "ix": 2}, "a": {"a": 0, "k": [18, 18, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 2, "nm": "边框.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 390, "s": [0]}, {"t": 420, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [78, 18, 0], "ix": 2}, "a": {"a": 0, "k": [18, 18, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 2, "nm": "7.png", "cl": "png", "refId": "image_7", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 390, "s": [0]}, {"t": 420, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [78, 18, 0], "ix": 2}, "a": {"a": 0, "k": [18, 18, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}], "markers": []}