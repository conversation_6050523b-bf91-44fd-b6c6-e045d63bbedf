//
//  SOPostModel.h
//  Soul_New
//
//  Created by fang on 2018/5/22.
//  Copyright © 2018年 Soul. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <SOUGCInterface/SOUGCInterface.h>
#import <SOPublishInterface/SOCoAuthor.h>
#import <SoulCoreBase/SoulCoreBase.h>
#import "SOPreAddPostModel.h"
#import "SOVoteItemsModel.h"
#import <YYModel/YYModel.h>
#import "TagsModel.h"
#import <SOPublishInterface/SOMusicSongModel.h>
#import "SOTopicInfoModel.h"
#import "SOInnerTag.h"
#import <SOUGCInterface/SOPostGIftInfoModel.h>
#import <SOUGCInterface/SOGeoPositionInfoModel.h>
#import "SOHotCommentModel.h"
#import "SOPromptLabelMaps.h"
#import "SOPostRecommendComment.h"
#import "SODislikeEntriesModel.h"
#import "SOSquareHotCommentModel.h"
#import "SOPostExtModel.h"
#import "SoulSquareFilterDoorModel.h"
#import "SOInnerTagModel.h"
#import "SORecommandInfoModel.h"
#import "SOPostCommodityInfoModel.h"
#import <SOPublishInterface/SOReleaseSchoolModel.h>
#import "SOPraiseTypeItem.h"
#import "SOPostRoomProfileModel.h"
#import "SOPostCampusModel.h"
#import "SOGlobalBusinessModel.h"
#import "SOPostSpecialBizModel.h"
#import "SOAttachment+SoulOSSMarker.h"
#import "SOMarkItem.h"
#import "SOHotSoulInfoModel.h"
#import "SOPostRecTagModel.h"
#import "SOPostRecommendedCityModel.h"
#import <SoulCoreBase/SoulCoreBase.h>
#import "SOPostMedalRankModel.h"
#import "SOPostSuperVIPModel.h"
#import "SORecCircleCardModel.h"
#import "SOPostCommonLabelModel.h"
#import "SOOfficialBanner.h"
#import "SOPostSendPostRemindVO.h"
#import "SOPostCommericalModel.h"
#import "SOHeadBarModel.h"
#import "SOPrivateChatListItem.h"
#import "SOPostSkinModel.h"
#import <SOUGCInterface/SOCommonAnchorModel.h>
#import "SOLinkModel.h"
#import "SOPostAdditionalModel.h"
#import "SOPostProfileModel.h"
#import "SOPostPromoteInfo.h"
#import "SOPostAbnormalStateModel.h"
#import "SOSafetyTipsModel.h"

@class SOWornMedalModel;
@class SAPostConmmentItem;
@class SOPostAITextModel;
@class SOPostCreateTimeModel,SOPostEmotionModel,SOPostWeatherModel;

@class YYTextLayout;

typedef NS_ENUM(NSUInteger, SOPostType) {
    SOPostTypeCommon,           // 通用类型
    SOPostTypeSending,          // 来自发帖当前正在发送的帖子
    SOPostTypeFollowRecommand,  // 主广场关注广场的帖子
};

typedef NS_ENUM(NSUInteger, SOTimelineCellType) {
    SOTimelineCellTypeNormal = 0, ///< 主广场推荐
    SOTimelineCellTypeFollowRecommend,
    SOTimelineCellTypeMainFollowed,
    SOTimelineCellTypeMainRecommend,
    SOTimelineCellTypeMainLocation, // 同城（暂未发现使用）
    SOTimelineCellTypeMainRecent,   // 主广场-最新（暂未发现使用）
    SOTimelineCellTypeTopicInfo, // 帖子详情
    SOTimelineCellTypePostSchoolMoments,//校圈
    SOTimelineCellTypePostSchoolBar, //校园吧
    SOTimelineCellTypePostMySchool,   // 校园吧-我的学校
    SOTimelineCellTypeRecCategory   // 广场分类

};

//点击关注时候全局通知
extern NSString * const _Nullable KPostDidChangeFollowStatusNotification;

NS_ASSUME_NONNULL_BEGIN

@interface SOPost : NSObject

@property(nonatomic, copy, nullable) NSString *pSearch; ///< 搜索模块埋点使用
@property(nonatomic, strong, nullable) SOImmerasiveEmptyModel *immerasiveEmptyModel;
@property(nonatomic, copy, nullable) NSString *fromSource; ///帖子模型的来源 "PUSH"

@property(nonatomic, strong, nullable) NSString *currentTagId; //  当前请求属于哪个tagId
@property(nonatomic, assign) SOPostType sourceType;
@property(nonatomic, strong, nullable) SOPreAddPostModel *preAddPostModel;
@property(nonatomic, assign) BOOL recentChatUser;
@property(nonatomic, strong, nullable) SOPostRoomProfileModel *postRoomProfileModel;

@property(nonatomic, copy, nullable) NSString *algoInfo;
@property(nonatomic, assign, readonly) BOOL isImageVote;
@property(nonatomic, assign) BOOL authorOnline;
@property(nonatomic, assign, readonly, getter=isOneSelf) BOOL oneSelf;
@property(nonatomic, copy, nullable) NSString *authorIdUnEcpt;   ///< 解密后的authorId
@property(nonatomic, copy, nullable) NSString *authorIdEcpt;     ///< 加密的authorId
@property(nonatomic, copy, nullable) NSString *userActiveTime;   ///< 用户上次活跃的时间
@property(nonatomic, copy, nullable) NSString *matchDegree;      ///< 关注官场推荐的匹配度

@property(nonatomic, strong, nullable) NSNumber *id;
@property(nonatomic, strong, nullable) NSArray <SOInnerTag *> *innerTags;
@property(nonatomic, strong, nullable) NSArray <SOWornMedalModel *> *wornMedals;
@property(nonatomic, strong, nullable) NSArray <SOPostCommonLabelModel *> *labelAuthList;
@property(nonatomic, copy, nullable) NSString *tagName;
@property(nonatomic, copy, nullable) NSString *type;      //取值枚举：TEXT，IMAGE，VIDEO，AUDIO
@property(nonatomic, copy, nullable) NSString *state;     //取值枚举：NORMAL，USER_DELETE，ADMIN_DELETE，ADMIN_LOCK，SYSTEM_LOCK
@property(nonatomic, copy, nullable) NSString *content;   //
@property(nonatomic, copy, nullable) NSString *contentTitle;
@property(nonatomic, copy, nullable) NSString *recomandContent;

//双列图片封面
@property(nonatomic, copy, nullable) NSString *postCoverAutoMatchImageUrl;

//取值枚举：晴，霾，阴，雨，雪，喜，乐，哭，平静，气，爱，怒，呆，邪恶，不开心，晴天，多云，阴天，小雨，大雨，大风，雾霾，阴，雨天，下雪，SUNNY，CLOUDY，OVERCAST，HEAVYRAIN，LIGHTRAIN，SMOG，SNOW，FOG，RAIN
@property(nonatomic, strong, nullable) NSNumber *authorId;
@property(nonatomic, strong, nullable) NSString *weather;
@property(nonatomic, strong, nullable) NSNumber *topped;
@property(nonatomic, strong, nullable) NSNumber *adminTopped;
@property(nonatomic, strong, nullable) NSNumber *soulmate;
@property(nonatomic, strong, nullable) NSNumber *createTime;
//返回ms
@property(nonatomic, strong, nullable) NSNumber *authorOnlineTime;

@property(nonatomic, strong, nullable) NSNumber *displayTime;
@property(nonatomic, strong, nullable) NSNumber *modifyTime;
@property(nonatomic, strong, nullable) NSNumber *deleteTime;
@property(nonatomic, strong, nullable) NSNumber *adminRecommended;
@property(nonatomic, strong, nullable) NSString *relayUUID;
/// 发后编辑时间
@property(nonatomic, strong, nullable) NSNumber *editTime;

//单位毫秒 记录视频播放节点 ms
@property(nonatomic, assign) CGFloat currentTime;

@property(nonatomic, assign) CGFloat totalTime;
//单点播放记录
@property(nonatomic, assign) float playProgress;

@property (nonatomic, strong, nullable) NSString *eventParameterOfClickDetailType;

/// 社交名片贴标志  1:社交名片贴
@property (nonatomic, assign) NSInteger isSocializeCard;

/// AI 排版
@property (nonatomic, strong, nullable) SOPostAITextModel *aiTextModel;

// MARK: - Attachments

/// Audio
@property (nonatomic, strong, nullable) NSString *audioTranslationContent;

// MARK: - Sort Data

/// 端智能模型：推理分数
@property (nonatomic, assign) float sortAIScore;
@property (nonatomic, assign) int sortAIScoreIndex;

/// 画风规则
@property (nonatomic, strong, nullable) NSArray<NSNumber *> *sortLabels;

// MARK: - Post Data

/// 帖子特征
/// Key: String
/// Value: Base64Encoded String
@property (nonatomic, strong, nullable) NSDictionary<NSString *, NSString *> *postModelInfo;

// MARK: - Permission

/// 评论权限：
/// 0:所有人可评
/// 1:我关注的人可评
/// 2:关注我的人可评
/// 3:所有人都不可评
@property (nonatomic, assign) NSInteger commentAccessType;
/// 是否可以评论
@property (nonatomic, strong, nullable) NSNumber *commentFlagValue;

/// 可见范围
/// PUBLIC：广场可见
/// HOMEPAGE：仅主页可见
/// STRANGER：仅陌生人可见
/// TAG：仅话题广场可见
/// PRIVATE：仅自己可见
/// BLOCK：指定用户不可见
@property (nonatomic, copy, nullable) NSString *visibility;
/// 可见范围：指定用户不可见，不可见用户列表
@property (nonatomic, strong, nullable) NSArray<SOPostPermissionInvisibleUser *> *blockUserList;

/// 下载权限
@property (nonatomic, strong, nullable) NSNumber *download;

/// 转发权限
@property (nonatomic, strong, nullable) NSNumber *relay;

/// 搬运权限
@property (nonatomic, strong, nullable) NSNumber *tort;

/// 权限管理：创作申明，创作内容由AI生成
@property (nonatomic, strong, nullable) NSNumber *permissionOfCreationByAI;

/// 社交分类 (0:非社交，1:弱社交，2:强社交)
@property (nonatomic, strong, nullable) NSString *socialLabel;

/// 违规标识
@property (nonatomic, strong, nullable) SOPostAbnormalStateModel *postAbnormalStateModel;


// MARK: - Location

/// 地理位置
@property (nonatomic, strong, nullable) SOGeoPositionInfoModel *geoPositionInfo;

// MARK: - Event

@property (nonatomic, strong, nullable) NSDictionary *eventParameters;

// MARK: - Comment

@property (nonatomic, strong, nullable) NSArray<NSString *> *postQuickCommentList;

// MARK: - UserFeed

@property (nonatomic, strong, nullable) NSString *userFeedUserIdEcpt;
@property (nonatomic, strong, nullable) NSString *userFeedRecommandContent;
@property (nonatomic, strong, nullable) NSString *userFeedSendEmojiId;
@property (nonatomic, assign) BOOL *userFeedSendEmojiSuccess;
/// 是否支持自动播放
/// 1. 1张图片时为NO
/// 2. 被手动中断为NO
/// 3. 播放结束为NO
@property (nonatomic, assign) BOOL userFeedAutoPlayEnable;

// MARK: -

/// 是否开启了私聊按钮
@property (nonatomic, assign) BOOL isPrivateChatEnable;

/// 图片沉浸式：是否开启清屏功能
@property (nonatomic, assign) BOOL imageFeedCellClearState;

/// 是否展示优质标签引导
@property (nonatomic, assign) BOOL isShowHighQualityLabelGuide;

//24H 推荐词
@property(nonatomic, strong, nullable) NSString *recommendWord;
//24H 帖子可见性倒计时文案
@property(nonatomic, strong, nullable) NSString *postShowCountdownText;

//soulmate帖子对应的soulmateId
@property(nonatomic, strong, readonly, nullable) NSNumber *soulmateUserId;

//用户关联的精选话题 可为空
@property (nonatomic, strong, nullable) NSArray <TagsModel *>*selectedTags;
@property(nonatomic, copy, nullable) NSString *soulmateUserIdEcpt;//该贴作者是否是当前用户关注用户，会显示'来自关注

@property(nonatomic, strong, nullable) NSNumber *followed;
@property(nonatomic, assign) BOOL authorFollowMe; // 对方是否关注我
@property(nonatomic, strong, nullable) NSNumber *collected;
@property(nonatomic, strong, nullable) NSNumber *liked;
//点赞类型  0.普通点赞  1,爱心 2, 狗头 3,吃瓜 4, 酸了
@property(nonatomic, strong) NSNumber *likeType;

@property(nonatomic, copy, nullable) NSString *alias;

@property(nonatomic, copy, nullable) NSString *buttonType; //私聊按钮是否漏出.

// 拍一拍相关属性
@property(nonatomic, strong, nullable) NSNumber *patCount; // 拍一拍次数
@property(nonatomic, assign) BOOL hasPatted; // 当前用户是否已拍一拍

//只做承载 不是 post 模型接口返回的 24H 帖子 私聊信息
@property (nonatomic, strong) NSArray <SOPrivateChatListItem *>*privateChatMsgList;

@property(nonatomic, strong, nullable) SAPostConmmentItem *commentItem;
@property(nonatomic, strong, nullable) NSMutableArray<SOAttachment *> * _Nullable attachments;
@property(nonatomic, strong, nullable) NSMutableArray<SOHiddenTagsModel *> *privacyTagModelList;
@property(nonatomic, strong, nullable) NSMutableArray<SOHiddenTagsModel *> *onShowPrivacyTagModelList;
@property(nonatomic, strong, nullable) NSMutableArray<SODislikeEntriesModel *> *dislikeEntries;
@property(nonatomic, assign) BOOL showNoRelation;
@property(nonatomic, strong, nullable) SOVoteItemsModel *voteItemListModel;
@property(nonatomic, assign) BOOL voteCanPublish;
@property(nonatomic, copy, nullable) NSString *commentNum;
@property(nonatomic, copy, nullable) NSString *followNum;
@property(nonatomic, copy, nullable) NSString *likeNum;
@property(nonatomic, copy, nullable) NSString *shareNum;
@property(nonatomic, strong, nullable) NSNumber *comments;
@property(nonatomic, strong, nullable) NSNumber *follows;
@property(nonatomic, strong, nullable) NSNumber *likes;
@property(nonatomic, strong, nullable) NSNumber *shares;
@property(nonatomic, copy, nullable) NSString *comeFrom;
@property(nonatomic, copy, nullable) NSString *signature;

//老关系互动逻辑
@property (nonatomic, assign) BOOL mutual;

//距离多少KM 文案
@property (nonatomic, copy, nullable) NSString *postDistance;

//风险提示Model
@property (nonatomic, strong, nullable) SORiskModel *riskModel;

//置顶标识
@property(nonatomic, strong, nullable) NSArray <SOMarkItem *>*postMarkModels;

//热评区域对象
@property(nonatomic, strong, nullable) SOSquareHotCommentModel *squareHotComment;

@property(nonatomic, strong, nullable) NSMutableArray *tags;
//被推荐的时间会有推荐时间
@property(nonatomic, strong, nullable) NSNumber *recTime;
//推荐帖子来源tag
@property(nonatomic, strong, nullable) NSString *recTag;
@property(nonatomic, strong, nullable) NSString *commentId;

//共创相关信息
@property(nonatomic, strong, nullable) SOCoAuthor *coauthor;
@property(nonatomic, strong, nullable) NSArray *commentContent;
@property(nonatomic, strong, nullable) NSMutableArray *atList;
// 大V保护
// 0:不展示
// null或1 ：展示
@property(nonatomic, strong, nullable) NSNumber *chatOpt;

@property(nonatomic, strong, nullable) SORecommandInfoModel *recommendInfo;
/// 帖子优质推荐标签模型
@property (nonatomic, strong, nullable) SOPostRecommandHighQualityModel *highQualityLabelModel;

//是否是自动保量
@property(nonatomic, assign) BOOL autoQuality;

/// 热soul
@property(nonatomic, strong, nullable) SOHotSoulInfoModel *hotSoulInfo;

//心动标识
@property(nonatomic, strong, nullable) SOPostAuthorModel *postAuthorModel;
@property(nonatomic, strong, nullable) SOPostChatCityBankBannerModel *cityRankBanner;
@property (nonatomic, strong, nullable) SOBridgingPostModel *bridgingPostModel;

@property (nonatomic, strong, nullable) SOHeadBarModel *headBar;

//原始数据
@property(nonatomic, strong, nullable) NSArray <SOPraiseTypeItem *> *praiseDetails;

@property(nonatomic, strong, nullable) NSArray <SOPraiseTypeItem *> *constructPraiseDetails;

//本地数据////////////////////////////
@property(nonatomic, assign) CGFloat cellHeight;
@property(nonatomic, strong, nullable) NSMutableArray <NSValue *> *mediaRects;
@property(nonatomic, assign) BOOL isFold; //如果是长文需要判断是否是折起状态
@property(nonatomic, assign) BOOL isFoldEnable; //是否支持折叠
//发布在用
@property(nonatomic, assign) long localId;
@property(nonatomic, assign) BOOL isGif;
@property(nonatomic, assign) BOOL isLocalModel;
@property(nonatomic, strong, nullable) NSString *localStatus;
@property(nonatomic, strong, nullable) NSString *publishUUID; //本地路径

@property(nonatomic, strong, nullable) NSMutableAttributedString *attributedContent;
@property(nonatomic, assign) CGFloat contentHeight;

@property(nonatomic, copy, nullable) NSString *actionType; // for 交互过的帖子
@property(nonatomic, copy, nullable) NSString *actionTitle; // 交互的文本显示
@property(nonatomic, copy, nullable) NSString *chatMaidian;

@property(nonatomic, assign) NSTimeInterval displayStartTime;
@property(nonatomic, assign) NSTimeInterval displayInrectTime;  // 到达指定位置时记录

@property(nonatomic, strong, nullable) NSMutableArray<SOHotCommentModel *> *hotComment;

@property(nonatomic, strong, nullable) NSNumber *recallSRC;
@property(nonatomic, strong, nullable) NSString *algID;

@property(nonatomic, strong, nullable) NSArray *expIds;

@property(nonatomic, strong, nullable) NSMutableArray *recommendComments;

@property(nonatomic, copy, nullable) NSString *requestId;
@property(nonatomic, copy, nullable) NSString *algExt;
@property(nonatomic, copy, nullable) NSString *recParam;
//jonStr 推荐通用参数透传
@property(nonatomic, copy, nullable) NSString *clientTrackInfo;
//jonStr 业务通用参数透传
@property(nonatomic, copy, nullable) NSString *bizClientInfo;

@property(nonatomic, assign) NSUInteger praiseTime;        // 点赞的时间
@property(nonatomic, assign) NSUInteger commentTime;       // 评论的时间
@property(nonatomic, assign) NSUInteger collecttionTime;   // 收藏的时间
@property(nonatomic, assign) NSUInteger voteTime;          // 投票时间
@property(nonatomic, assign) NSUInteger viewTime;          // 投票时间
@property(nonatomic, assign) NSUInteger collectTime;
@property(nonatomic, assign) BOOL superVIP;
@property(nonatomic, assign) BOOL showSuperVIP;
@property(nonatomic, assign) BOOL superstar; // 明星账号

@property (nonatomic, assign) NSInteger loopAnimationCount;

//跳转类型判断
@property(nonatomic, strong, nullable) SOPostExtModel *postExtModel;

//活动入口配置
@property(nonatomic, strong, nullable) SOPostJumpModel *postJumpModel;
/// 并存的活动跳转配置
@property(nonatomic, strong, nullable) NSArray<SOPostJumpModel *> *postJumps;

@property(nonatomic, strong, nullable) YYTextLayout *locationTextLayout;

@property(nonatomic, strong, nullable) NSArray<SOPostMaterialModel *> *postMaterialsInfo;

//officialTag =1 (匿名小助手)
@property(nonatomic, assign) NSInteger officialTag;

//新版本答案君和匿名小助手
@property(nonatomic, strong, nullable) SOInnerTagModel *officialTags;

//热评/暖评 comment Model
@property(nonatomic, strong, nullable) NSArray<SOTopicInfoModel *> *hotComments;

//虚拟人评论
@property(nonatomic, strong, nullable) SOTopicInfoModel *squareCommentNestedDto;

// 是否应该显示qq引导
@property(nonatomic) BOOL shouldShowQQMusicGuide;

@property(nonatomic, assign) BOOL fromtag;

@property(nonatomic, assign) BOOL cTopped;
// 是否是当前用户主页的帖子 个人主页 1。他人主页 2。soulmate 3
@property(nonatomic, assign) NSInteger isMyHomePagePost;

// 是否来自瞬间详情推荐贴
@property(nonatomic, assign) BOOL isDetailRecommendPost;

// 判断是不是新的post对象还是其他地方的引用，点赞需不需要 Notification +1
@property(nonatomic, assign) BOOL isReferencePost;

@property(nonatomic, strong, nullable) SOMusicSongModel *songInfoResModel;

//创作者激励进度
@property (nonatomic, strong, nullable) NSNumber *exposureProgress;

//校圈
@property(nonatomic, strong, nullable) SOReleaseSchoolModel *collegeCircleModel;
@property(nonatomic, strong, nullable) SOPostCampusModel *campusModel;

//公共数据的数据源
@property(nonatomic, strong, nullable) SOGlobalBusinessModel *globalViewModel;

/// 风险类型（fakeContent:假图片，fakeText:假内容，fakePerson:假图片， fakeImage:假图片）
@property(nonatomic, strong, nullable) NSString *riskType;
/// 虚假内容搬运提示文案
@property(nonatomic, strong, nullable) NSString *riskContent;
/// 风险来源(IN_WEBSITE:站内,OUT_WEBSITE:站外)
@property(nonatomic, strong, nullable) NSString *riskSource;
/// 风险跳转链接
@property(nonatomic, strong, nullable) NSString *riskUrl;

/// 通用锚点
@property (nonatomic, strong, nullable) NSArray <SOCommonAnchorModel *> *tailAnchors;
/// 通用锚点类型
/// 0:普通类型
/// 10:导流类型
@property (nonatomic, assign) NSInteger tailAnchorShowType;

//官方贴动态高亮链接
@property(nonatomic, strong, nullable) NSArray <SOLinkModel *> *linkModels;

//IP 展示地址
@property(nonatomic, strong, nullable) NSString *showIpLocationInfo;

/// AI 推荐理由
@property (nonatomic, strong, nullable) NSString *aiRecReason;

//绑定每一刷的帖子与页码 默认是 1
@property (nonatomic, strong, nullable) NSString *pageIndex;
//列表位置 绝对位置 初始化为准
@property (nonatomic, strong, nullable) NSString *postion;

@property (nonatomic, strong, nullable) NSString *requestTime;

// ----------------------------------
//帖子头像
@property(nonatomic, copy, nullable) NSString *avatarName;
@property(nonatomic, copy, nullable) NSString *avatarColor;
@property(nonatomic, copy, nullable) NSString *commodityUrl;//头像挂件
//soulmate 头像
@property(nonatomic, copy, nullable) NSString *targetAvatarName;
@property(nonatomic, copy, nullable) NSString *targetAvatarColor;
@property(nonatomic, copy, nullable) NSString *targetCommodityUrl;//头像挂件
///< 帖子id
@property(nonatomic, copy, nullable) NSString *postId;
///< 帖子加密id
@property(nonatomic, copy, nullable) NSString *postIdEcpt;
// 帖子收藏
@property(nonatomic, copy, nullable) NSString *collectCountStr;
@property(nonatomic, assign) NSInteger collectCount;
@property(nonatomic, assign) BOOL isCollected;
// 帖子点赞
@property(nonatomic, copy, readonly) NSString *likeCountStr;
@property(nonatomic, assign) NSUInteger likeCount;
@property(nonatomic, assign) BOOL isLiked;
// 帖子分享
@property(nonatomic, copy, nullable) NSString *shareCountStr;
@property(nonatomic, assign) NSUInteger shareCount;
@property(nonatomic, assign) BOOL isShared;
// 帖子评论
@property(nonatomic, copy, readonly) NSString *commentCountStr;
@property(nonatomic, assign) NSUInteger commentCount;
@property(nonatomic, assign) BOOL isCommented;
//帖子瞬间浏览量
@property(nonatomic, assign) NSInteger exposure;
/// 礼物推广提示icon资源地址
@property (nonatomic, strong, nullable) NSString *promoteIconUrl;

// 拍一拍计数相关
@property(nonatomic, copy, readonly) NSString *patCountStr;

@property(nonatomic, copy, nullable) NSString *audioPlayTimes; /**< 音频播放次数 */

//帖子审核参数
@property(nonatomic, strong, nullable) SOPostReviewModel *postReviewModel;

//标签状态管理map (目前应用场景是帖子详情)
@property(nonatomic, strong, nullable) SOPromptLabelMaps *promptLabelMaps;

@property(nonatomic, copy, nullable) NSString *videoTag;  // 视频分类tag

@property(nonatomic, copy, nullable) NSString *videoTagId;// 视频分类id

@property(nonatomic, assign) BOOL isSSR;

//同城曝光推广
//帖子推广状态 0:不可推广 1:可推广
@property(nonatomic, assign) NSInteger promoteState;
//推广url
@property(nonatomic, copy, nullable) NSString *promoteUrl;
//是否展示推广入口标志
@property(nonatomic, assign) BOOL showPromote;

@property(nonatomic, strong, nullable) SOPostPromoteInfo *promoteGiftModel; //是否展示帖子标签
/// 帖子加热入口优化 数据 https://soulapp.feishu.cn/wiki/Hh3cwsaWZiJqGskMOLncl6jlnqf
@property (nonatomic, strong, nullable) SOPostPromoteCouponUseTip *couponUseTip;


//isGuideAnimation(自定义字段兼容处理,后续会改造)
@property(nonatomic, assign) BOOL isGuideAnimation;

@property(nonatomic, strong, nullable) NSString *showGeo; //是否展示地理位置

@property(nonatomic, strong, nullable) SOPostGIftInfoModel * giftInfo;
@property(nonatomic, assign) BOOL showGiftInfo;//是否展示送礼信息 根据ab和送礼信息是否有效判断

@property(nonatomic, assign) BOOL privacyTagPost;//是否是引力签发布model

@property(nonatomic, strong, nullable) SoulSquareFilterDoorModel *stickerIcon;
@property(nonatomic, strong, nullable) SoulSquareFilterDoorModel *filterIcon;
@property(nonatomic, strong, nullable) SoulSquareFilterDoorModel *templateIcon;

@property(nonatomic, copy, nullable) NSArray<SOPostCommodityInfoModel *> *commodityInfoModel;

@property(nonatomic, copy, nullable) NSString *sid;
@property(nonatomic, copy, nullable) NSString *title;
@property(nonatomic, strong, nullable) NSNumber *jumpType;
@property(nonatomic, copy, nullable) NSString *jumpObject;
@property(nonatomic, copy, nullable) NSString *jumpUrl;
@property(nonatomic, copy, nullable) NSString *showImage;
@property(nonatomic, copy, nullable) NSString *subContent;

/// 已关联的好物信息
@property(nonatomic, copy, nullable) NSArray<SOPostCommodityInfoModel *> *postCommodityInfos;

///是否是该用户首次发帖
@property(nonatomic, assign) BOOL firstPost;

@property(nonatomic, assign) BOOL shouldShowFirstEncourage;

/// 最相关Tag， 视频Tab 中使用
@property(nonatomic, strong, nullable) SOInnerTag *mostRelatedTag;

/// 特殊业务model， 现阶段只有 top 帖子置顶
@property(nonatomic, strong, nullable) SOPostSpecialBizModel *specialBizModel;
/// 帖子详情页 ssr 的toast 提示字段
@property(nonatomic, copy, nullable) NSString *ssrMotivateToast;
/// 帖子更多工具配置
@property(nonatomic, strong, nullable) SOPostAlertActionToolModel *alertActionToolModel;

@property (nonatomic, strong, nullable) SOPostRecommondTagReasonModel *recReasonTag;

/// 帖子统一跳转路由信息
@property (nonatomic, strong, nullable) SOPostUnifyJumpModel *unifyJumpRouteModel;

/// 超级星人徽章
@property (nonatomic, strong, nullable) SOPostSuperVIPModel *superVIPMedal;

/// 用户勋章垂类榜单排名（没有排名信息则当前Model为null）
@property (nonatomic, strong, nullable) SOPostMedalRankModel *medalRankModel;

/// 用户发帖数量
@property (nonatomic, assign) NSInteger postNums;
/// 勋章  排名（等于-1表示未上榜）
@property (nonatomic, assign) NSInteger medalRank;
/// 客户端皮肤
@property (nonatomic, strong) SOPostSkinModel *postSkinData;

/// 众裁-内容精选用户
@property (nonatomic, strong, nullable) NSArray<SOSimpleUserInfoModel *> *juryHighLightUserList;

/// 本地存储是否正在申请中
@property (nonatomic, assign) BOOL isLocalApplying;
/// 是否引导用户关注作者
@property (nonatomic, assign) BOOL showLeadFollowPage;

// MARK: - 推荐卡片

/// 推荐卡片类型 0.新样式卡片 1:话题卡片 2:地理位置卡片 3 圈子卡片 4.定位卡片
@property (nonatomic, assign) NSInteger recCardType;

/// 推荐卡片对象，话题
@property (nonatomic, strong, nullable) SOPostRecTagModel *recTagModel;

/// 推荐卡片对象，地理位置
@property (nonatomic, strong, nullable) SOPostRecommendedCityModel *recPositionModel;

/// 推荐圈子卡片内容
@property (nonatomic, strong, nullable) SORecCircleCardModel *recCircleModel;

/// praiseEffectId字段 用于配制点赞特效的 id
@property (nonatomic, strong, nullable) NSString *praiseEffectId;

/// 帖子点赞特效每天限制次数
@property (nonatomic, assign) NSInteger praiseEffectDailyLimit;

/// 用户名展示的标签列表
@property (nonatomic, strong, nullable) NSArray<SOPostUserProfileIconModel *> *postUserIconList;

/// 是否为悄悄关注
@property (nonatomic, assign) BOOL secretFollowFlag;

/// authorChatRoomState  建群状态
@property (nonatomic, strong, nullable) NSString * authorChatRoomState;

@property (nonatomic, strong, nullable) SOPostChatRoomRecModel *chatRoomRecModel;

@property (nonatomic, strong, nullable) NSString *loveFlagUrl;

/// 当recCardType 位5 时，用于展示推荐用户卡片的 推荐原因信息
@property (nonatomic, strong, nullable) NSArray<SORecReasonModel *> *recReasonList;

@property(nonatomic, assign) BOOL isSelfRecTip;//添加至推荐tip
/// 次数 作者推荐评论剩余次数 -1:没有推荐权力, 0:推荐次数已用完
@property (nonatomic, strong, nullable) NSNumber *authorRecRightNum;

/// 帖子详情页发帖引导对象（不为空，则显示引导view）
@property (nonatomic, strong, nullable) SOPostSendPostRemindVO *sendPostRemindVO;

/// 发后编辑当前帖子id（曝光埋点使用）
@property (nonatomic, strong, nullable) NSString * editPostId;

/// 发后编辑当前版本号（曝光埋点使用）
@property (nonatomic, strong, nullable) NSString * postVersion;

/// 游戏号，视频号号 活动卡片
@property (nonatomic, strong, nullable) SOPostCommericalModel *postCommercialVO;
/// 获取对应的sessionId
@property (nonatomic, copy, nullable) NSString *(^getSessionIdBlock)(void);
/// 3. 曝光埋点增加当前帖子的限流状态参数（isLimiting  0否 1是）
@property (nonatomic, assign) NSInteger authorChatFc;
/// 帖子展示位置
@property (nonatomic, weak, nullable) NSIndexPath *indexPath;

/// 是否能够使用 getSessionIdBlock
@property (nonatomic, assign, readonly) BOOL isGetSessionEnable;

/// 帖子24h剩余时间描述
@property (nonatomic, strong, nullable) NSString *post24hTitle;
/// 视频推荐
@property (nonatomic, strong, nullable) SOVideoFriendRecommedModel *videoPostRecModel;
/// 视频推荐：是否展示推荐提示
@property (nonatomic, assign) BOOL showRecommedUserAlert;

/// 帖子点赞类型
@property (nonatomic, assign) NSInteger postPraiseBizType;

///  * 打招呼类型，1：打招呼
@property (nonatomic, assign) NSInteger sayHelloType;

/// 是否已经点击打招呼
@property (nonatomic, assign) BOOL hadClickSayHelloType;
/// 用户群聊房id
@property (nonatomic, strong, nullable) NSString *chatRoomId;

@property (nonatomic, copy, nullable) NSString *showLocationInfoV2;

//问答贴标志字段: 0:普通贴, 1:问题贴, 2:答案贴.
@property (nonatomic, copy, nullable) NSString *aiQuestionState;

//问题贴到发布url
@property (nonatomic, copy, nullable) NSString *aiAnswerJumpUrl;

@property (nonatomic, strong, nullable) NSString *sceneType; // 场景类型

//问答贴总结
@property (nonatomic, strong, nullable) NSArray <SOQuestionPostSumaryModel *>*answerList;

//发布来源
@property (nonatomic, copy, nullable) NSString *publishPageFrom;

//用户信息区域
@property (nonatomic, strong, nullable) NSArray <SOUserPostAreaViewElementModel *>* userPostAreaViewFirstList;

@property (nonatomic, strong, nullable) NSArray <SOUserPostAreaViewElementModel *> * userPostAreaViewSecondList;

//是不是缓存数据Post 过滤埋点
@property (nonatomic, assign) BOOL isCache;
//是否已经关联推荐帖子
@property (nonatomic, assign) BOOL isRelation;
@property (nonatomic, copy, nullable) NSString *relationTitle;
@property (nonatomic, assign) BOOL configRelationEndColor;
//关联的主postId
@property (nonatomic, strong) NSString *relatePostId;
//关联的子集合
@property (nonatomic, strong, nullable) NSMutableArray <SOPost *>*relationPosts;
//关联子对象 非post 转成 post载体
@property (nonatomic, strong, nullable) id relateBizModel;

//私聊是否忽略限流
@property (nonatomic, assign) BOOL ignoreChatLimit;

//临时标识
@property (nonatomic, copy, nullable) NSString *tempLocalState;


//非业务参数 强制切换来源与广场
@property (nonatomic, assign) BOOL forceFromSquare;

/// AI树洞贴新增Model- 时间格式化对象
@property (nonatomic, strong, nullable) SOPostCreateTimeModel *createTimeModel;
/// AI树洞贴新增Model- 心情Model
@property (nonatomic, strong, nullable) SOPostEmotionModel *emotionModel;
/// AI树洞贴新增Model- 天气Model
@property (nonatomic, strong, nullable) SOPostWeatherModel *weatherModel;

/// 安全提示模型
/// 用于展示安全提示信息
/// 例如： AI生成内容的安全提示
@property (nonatomic, strong, nullable) SOSafetyTipsModel *safetyTips;


//AI订阅元数据
@property (nonatomic, strong, nullable) SORichTextModel *richText;

//AI订阅音频元数据
@property (nonatomic, strong, nullable) SORichTextAudioModel *richTextAudioModel;

//一定包含Url
@property (nonatomic, assign) BOOL aiDingYue;

//视频贴卡顿数量
@property (nonatomic, assign) NSInteger cattonCount;

//订阅的帖子 但是不一定包含Url
@property (nonatomic, assign) BOOL local_subscribState;

/// 点赞动效 媒资id
- (NSString * _Nullable)likeLottieResId;

//在线状态文案转化
- (NSString * _Nullable)authorOnlineTimeStr;

/// 是否可以评论
- (BOOL)commentFlag;

/// 次数 作者推荐评论剩余次数 -1:没有推荐权力, 0:推荐次数已用完
- (NSInteger)authorRecRightCount;

/// 转换成字典
- (NSDictionary * _Nullable)transformToDictionary;

- (void)refreshShareState:(BOOL)isShared;

- (void)refreshCollectState:(BOOL)isCollected;

- (void)refreshLikeState:(BOOL)isLiked;

- (void)refreshCommentState:(BOOL)isCommented;

- (NSArray *)getTagIds;

- (void)setLike:(BOOL)like;

- (void)updateCollectCount:(BOOL)collect;

- (BOOL)shouldAutoPlayVideo;

- (BOOL)hasVideo;

- (BOOL)isVideo:(NSInteger)index;

- (void)appendComment:(SOPostRecommendComment *)comment;

- (NSInteger)firstVideo;

- (BOOL)isLongVideoPost;

- (void)insertCampModelToTags;

- (BOOL)isMe;

/// 是否已关联好物
- (BOOL)isRelated;

- (void)setRelated:(BOOL)related;

///帖子是否是树洞类型
- (BOOL)postIsAITreeHoleType;

- (NSString *)getAttachmentIDs;

@end

@interface SOPostAITextModel : NSObject

@property (nonatomic, strong, nullable) NSString *content;
@property (nonatomic, strong, nullable) NSMutableArray *atList;
@property (nonatomic, assign) BOOL enable;

@end

// MARK: AI 树洞Model
/// 帖子时间格式化对象
@interface SOPostCreateTimeModel : NSObject
/// 年
@property (nonatomic, assign) NSInteger year;
/// 月
@property (nonatomic, assign) NSInteger month;
/// 日
@property (nonatomic, assign) NSInteger day;

@end

/// 帖子心情Model
@interface SOPostEmotionModel : NSObject

@property (nonatomic, strong, nullable) NSString *name;
@property (nonatomic, strong, nullable) NSString *imageUrl;
@property (nonatomic, strong, nullable) NSString *lottieUrl;
@property (nonatomic, strong, nullable) NSString *unClickImageUrl; //未点赞时的图标

@end

/// 帖子天气Model
@interface SOPostWeatherModel : NSObject

@property (nonatomic, strong, nullable) NSString *name;
@property (nonatomic, strong, nullable) NSString *imageUrl;

@end
NS_ASSUME_NONNULL_END
