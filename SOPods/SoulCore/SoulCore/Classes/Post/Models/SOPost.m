//
//  SOPostModel.m
//  Soul_New
//
//  Created by fang on 2018/5/22.
//  Copyright © 2018年 Soul. All rights reserved.
//
#import "SOPost.h"
#import <SoulUtils/SoulUtils.h>
#import <YYCategories/NSString+YYAdd.h>
#import <YYCategories/NSNumber+YYAdd.h>
#import <AFNetworking/AFNetworking.h>
#import "SOWornMedalModel.h"
#import "SORecCircleCardModel.h"
#import "SOPostLottieLimitManage.h"
#import <YYCategories/YYCategories.h>

@interface SOPost () {
    BOOL _related;
}
@end

@implementation SOPost

@synthesize globalViewModel = _globalViewModel;

@dynamic isImageVote, oneSelf;

+ (nullable NSDictionary<NSString *, id> *)modelCustomPropertyMapper {
    SOPost *post = nil;
    return @{
            @keypath(post, postId): @"id",
            @keypath(post, collectCountStr): @"followNum",
            @keypath(post, collectCount): @"follows",
            @keypath(post, isCollected): @"collected",
            @keypath(post, likeCount): @"likes",
            @keypath(post, isLiked): @"liked",
            @keypath(post, shareCountStr): @"shareNum",
            @keypath(post, shareCount): @"shares",
            @keypath(post, isShared): @"isShared",
            @keypath(post, commentCount): @"comments",
            @keypath(post, isCommented): @"isCommented",
            @keypath(post, videoTag): @"videoCategory.category",
            @keypath(post, videoTagId): @"videoCategory.id",
            @keypath(post, isSSR): @"ssr",
            @keypath(post, giftInfo): @"giftMap",
            @keypath(post, targetCommodityUrl): @"soulmateCommodityUrl",
            @keypath(post, commodityInfoModel): @"postCommodityInfos",
            @keypath(post, postRoomProfileModel): @"postRoomProfileModel",
            @keypath(post, commentFlagValue): @"commentFlag",
            @keypath(post, sortLabels): @"labelList",
            @keypath(post, isSocializeCard): @"socialCardFlag",
            @keypath(post, aiTextModel): @"postAiPrintContentDTO",
            @keypath(post, highQualityLabelModel): @"highGradePostIconModel",
            @keypath(post, globalViewModel): @"globalViewModelV1",
            @keypath(post, permissionOfCreationByAI): @"usedAiFlag",
            @keypath(post, userFeedUserIdEcpt): @"userIdEcpt",
            @keypath(post, userFeedRecommandContent): @"recTag",
            @keypath(post, audioTranslationContent): @"asr",
            @keypath(post, patCount): @"patCount",
            @keypath(post, hasPatted): @"hasPatted",
    };
}

+ (nullable NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    SOPost *post = nil;
    return @{
            @keypath(post, tags): [TagsModel class],
            @keypath(post, hotComment): [SOHotCommentModel class],
            @keypath(post, attachments): [SOAttachment class],
            @keypath(post, recommendComments): [SOPostRecommendComment class],
            @keypath(post, privacyTagModelList): [SOHiddenTagsModel class],
            @keypath(post, dislikeEntries): [SODislikeEntriesModel class],
            @keypath(post, innerTags): [SOInnerTag class],
            @keypath(post, wornMedals): [SOWornMedalModel class],
            @keypath(post, labelAuthList): [SOPostCommonLabelModel class],
            @keypath(post, commodityInfoModel): [SOPostCommodityInfoModel class],
            @keypath(post, postRoomProfileModel): [SOPostRoomProfileModel class],
            @keypath(post, praiseDetails): [SOPraiseTypeItem class],
            @keypath(post, postJumps): [SOPostJumpModel class],
            @keypath(post, postMaterialsInfo): [SOPostMaterialModel class],
            @keypath(post, postMarkModels): [SOMarkItem class],
            @keypath(post, hotComments) : [SOTopicInfoModel  class],
            @keypath(post, juryHighLightUserList) : [SOSimpleUserInfoModel class],
            @keypath(post, postUserIconList) : [SOPostUserProfileIconModel class],
            @keypath(post, recReasonList) : [SORecReasonModel class],
            @keypath(post, selectedTags) : [TagsModel class],
            @keypath(post, blockUserList) : [SOPostPermissionInvisibleUser class],
            @keypath(post, tailAnchors): [SOCommonAnchorModel class],
            @keypath(post, linkModels): [SOLinkModel class],
            @keypath(post, userPostAreaViewFirstList): [SOUserPostAreaViewElementModel class],
            @keypath(post, userPostAreaViewSecondList): [SOUserPostAreaViewElementModel class],
            @keypath(post, answerList): [SOQuestionPostSumaryModel class]
    };
}

- (void)setPostId:(NSString *)postId {
    _postId = postId;
}

- (BOOL)aiDingYue {
    BOOL hasAIUrl = self.richText.templateUrl.length;
    _aiDingYue= hasAIUrl;
    return _aiDingYue;
}

- (NSString * _Nullable)authorOnlineTimeStr {
    NSString *onlineTimeStr = nil;
    if (self.authorOnline) return onlineTimeStr;
    if (!SOString(self.authorOnlineTime).length) return onlineTimeStr;
    NSTimeInterval date = [[NSDate date] timeIntervalSince1970] * 1000;
    NSTimeInterval leaveTime = self.authorOnlineTime.integerValue;
    NSInteger margin = (date - leaveTime)/1000/60;
    if (margin < 0) return onlineTimeStr;
    if (margin < 1) {
        margin = 1;
    }
    if (margin <= 30) {
        onlineTimeStr = [NSString stringWithFormat:@"%ld分钟前活跃",(long)margin];
    } else if (margin > 30 && margin < 60) {
        onlineTimeStr = @"刚刚活跃";
    } else if (margin >= 60 && margin < 24 * 60) {
        onlineTimeStr = @"今日活跃";
    }
    return onlineTimeStr;
}

- (void)setPSearch:(NSString *)pSearch {
    if (!_pSearch || ![pSearch isEqualToString:_pSearch]) {
        NSDictionary *pSearchMap = [pSearch JSONObject];
        _pSearch = pSearchMap[@"sid"];
        //原始数据兜底
        if (!_pSearch) {
            _pSearch = pSearch;
        }
    }
}

- (BOOL)isRelated {
    return _related || self.postCommodityInfos.count > 0;
}
 
- (NSString *)showLocationInfoV2 {
    return _showLocationInfoV2;
}

- (NSString *)recomandContent {
    if (!_recomandContent) {
        if (self.recParam.length) {
            _recomandContent = self.recParam;
        }
        if (self.algoInfo.length) {
            _recomandContent = [_recomandContent stringByAppendingString:self.algoInfo];
        }
        _recomandContent = [NSString stringWithFormat:@"{%@} \n%@", _recomandContent, self.content];
    }
    return _recomandContent;
}

- (void)setRelated:(BOOL)related {
    _related = related;
}

- (NSArray *)constructPraiseDetails {
    if (!_constructPraiseDetails) {
        NSMutableArray *array = [[NSMutableArray alloc] init];
        for (int i = 1; i < 5; i++) {
            SOPraiseTypeItem *item = [[SOPraiseTypeItem alloc] init];
            item.type = @(i);
            //赋值真是数据.
            switch (i) {
                case 1: {
                    item.praiseCount = @(0);
                    for (SOPraiseTypeItem *praiseItem in _praiseDetails) {
                        if (praiseItem.type.integerValue == 1) {
                            item.praiseCount = praiseItem.praiseCount;
                        }
                    }
                    [array addObj:item];
                }
                    break;
                case 2: {
                    item.praiseCount = @(0);
                    for (SOPraiseTypeItem *praiseItem in _praiseDetails) {
                        if (praiseItem.type.integerValue == 2) {
                            item.praiseCount = praiseItem.praiseCount;
                        }
                    }
                    [array addObj:item];
                }
                    break;
                case 3: {
                    item.praiseCount = @(0);
                    for (SOPraiseTypeItem *praiseItem in _praiseDetails) {
                        if (praiseItem.type.integerValue == 3) {
                            item.praiseCount = praiseItem.praiseCount;
                        }
                    }
                    [array addObj:item];
                }
                    break;
                case 4: {
                    item.praiseCount = @(0);
                    for (SOPraiseTypeItem *praiseItem in _praiseDetails) {
                        if (praiseItem.type.integerValue == 4) {
                            item.praiseCount = praiseItem.praiseCount;
                        }
                    }
                    [array addObj:item];
                }
                    break;

                default:
                    break;
            }
        }
        _constructPraiseDetails = array;
    }
    return _constructPraiseDetails;
}

- (NSDictionary *)modelCustomWillTransformFromDictionary:(NSDictionary *)dic {
    if (dic) {
        NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithDictionary:dic];
        if ([dict[@"chatOpt"] isKindOfClass:[NSNull class]]) {
            dict[@"chatOpt"] = @(1);
        }
        return dict;
    }
    return dic;
}

- (BOOL)modelCustomTransformFromDictionary:(NSDictionary *)dic {
    if (![self.postId isValid]) {
        self.postId = dic[@"postId"];
        if ([self.postId isKindOfClass:[NSNumber class]]) {
            self.postId = ((NSNumber *) self.postId).stringValue;
        }
    }

    if (self.postRoomProfileModel) {
        CGSize size = [self.postRoomProfileModel.topic boundingRectWithSize:CGSizeMake(166, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading attributes:@{NSFontAttributeName: [UIFont fontWithName:@"PingFangSC-Medium" size:14]} context:nil].size;
        self.postRoomProfileModel.topicLabelHeight = size.height;
    }

    return YES;
}

#pragma mark - public methods

- (NSString *)likeCountStr {
    if (_likeCount <= 0) {
        return @"";
    }
    if (_likeCount >= 1000) {
        return [NSString stringWithFormat:@"%.1fk", _likeCount / 1000.f];
    } else {
        return [NSString stringWithFormat:@"%ld", _likeCount];
    }
}

- (NSString *)collectCountStr {
    if (_collectCount >= 1000) {
        _collectCountStr = [NSString stringWithFormat:@"%.1fk", _collectCount / 1000.f];
    } else {
        _collectCountStr = [NSString stringWithFormat:@"%ld", _collectCount];
    }
    return _collectCountStr;
}

- (NSString *)shareCountStr {
    if (_shareCount >= 1000) {
        _shareCountStr = [NSString stringWithFormat:@"%.1fk", _shareCount / 1000.f];
    } else {
        _shareCountStr = [NSString stringWithFormat:@"%ld", _shareCount];
    }
    return _shareCountStr;
}

- (NSString *)patCountStr {
    NSUInteger patCountValue = self.patCount ? self.patCount.unsignedIntegerValue : 0;
    if (patCountValue <= 0) {
        return @"";
    }
    if (patCountValue >= 1000) {
        return [NSString stringWithFormat:@"%.1fk", patCountValue / 1000.f];
    } else {
        return [NSString stringWithFormat:@"%ld", patCountValue];
    }
}

- (NSString *)commentCountStr {
    if (_commentCount >= 1000) {
        _shareCountStr = [NSString stringWithFormat:@"%.1fk", _commentCount / 1000.f];
    } else {
        _shareCountStr = [NSString stringWithFormat:@"%ld", _commentCount];
    }
    return _shareCountStr;
}


- (void)refreshLikeState:(BOOL)isLiked {
    if (_isLiked == isLiked) return;
    _isLiked = isLiked;
    _likeCount += _isLiked ? 1 : -1;
}

- (BOOL)showPromote {
#if DEBUG
    _showPromote = YES;
#endif
    return _showPromote;
}

- (void)refreshCommentState:(BOOL)isCommented {
    if (_isCommented == isCommented) return;
    _isCommented = isCommented;

    if (![NSString isNum:self.likeCountStr]) return;
    _commentCount += _isCommented ? 1 : -1;
}

- (void)refreshCollectState:(BOOL)isCollected {
    if (_isCollected == isCollected) return;
    _isCollected = isCollected;

    if (![NSString isNum:_collectCountStr]) return;
    if (isCollected){
        _collectCount += 1;
    } else {
        _collectCount = MAX(_collectCount - 1, 0);
    }
    [self updateCollectCount:isCollected];
}

- (void)refreshShareState:(BOOL)isShared {
    if (_isShared == isShared) return;
    _isShared = isShared;

    if (![NSString isNum:_shareCountStr]) return;
    _shareCountStr = [NSString stringWithFormat:@"%ld", _shareCountStr.integerValue + (_isShared ? 1 : -1)];
    _shareCount += 1;
}

- (BOOL)isMe {
    return [SoulUserManager isSelfEcpt:self.authorIdEcpt];
}

#pragma mark - notification methods

- (void)postChangeNotification:(NSNotification *)notification {
    SOPost *changingPost = notification.userInfo[@"model"];
    if (![_postId isEqualToString:changingPost.postId]) return;

    _likeCount = changingPost.likeCount;
    _liked = changingPost.liked;

    _shareCount = changingPost.shareCount;
    _shareCountStr = changingPost.shareCountStr;
    _isShared = changingPost.isShared;

    _commentCount = changingPost.commentCount;

    _collectCount = changingPost.collectCount;
    _collectCountStr = changingPost.collectCountStr;
    _collected = changingPost.collected;
}

- (void)postLikeContChangeNotification:(NSNotification *)notification {
    SOPost *changingPost = notification.userInfo[@"model"];
    NSString *likeType = notification.userInfo[@"type"];
    if (![_postId isEqualToString:changingPost.postId]) return;
    self.likeCount = changingPost.likeCount;
    self.isLiked = changingPost.isLiked;
    self.liked = changingPost.liked;
    //超级点赞数据更新.
    if (!self.isLiked) { //取消点赞
        self.likeType = @(0);
    } else {//点赞
        if (likeType.length) {
            self.likeType = @(likeType.integerValue);
        }
    }
}

- (void)postCollectContChangeNotification:(NSNotification *)notification {
    SOPost *changingPost = notification.userInfo[@"model"];
    if (![_postId isEqualToString:changingPost.postId]) return;
    [self refreshCollectState:changingPost.isCollected];
}

- (void)postCommentContChangeNotification:(NSNotification *)notification {
    SOPost *changingPost = notification.userInfo[@"model"];
    if (![_postId isEqualToString:changingPost.postId]) return;
    [self refreshCommentState:changingPost.isCommented];
}

- (void)postShareContChangeNotification:(NSNotification *)notification {
    NSString *postId = notification.userInfo[@"postid"];
    if (![_postId isEqualToString:postId]) return;

    self.shareCount += 1;
//    self.isShared = changingPost.isShared;
}

- (void)postChangeFollowStatusNotification:(NSNotification *)notification {
    NSString *postId = notification.userInfo[@"postId"];
    if (![_postId isEqual:postId]) return;
    self.followed = @(1);
}

#pragma mark - notification methods end

//iphone
- (BOOL)isFlag:(NSString *)content1 {
    return NO;
}

- (BOOL)isImageVote {
    BOOL isImageVote = NO;
    if (self.voteItemListModel && self.voteItemListModel.voteItemModels.count) {
        SOVoteSelectedModel *voteModel = [self.voteItemListModel.voteItemModels firstObject];
        if ([voteModel.type isEqualToString:@"2"]) {
            isImageVote = YES;
        }
    }
    return isImageVote;
}


- (NSNumber *)liked {
    if (_liked) {
        return _liked;
    }
    return [NSNumber numberWithBool:0];
}

- (NSNumber *)id {
    if ([_id.class isKindOfClass:[NSNumber class]]) {
        return _id;
    }

    if (![NSString isNum:[NSString stringWithFormat:@"%@", _id]]) {
        return [NSNumber numberWithInteger:0];
    }
    return [NSNumber numberWithInteger:[_id integerValue]];
}

- (NSArray *)getTagIds {
    if (!self.tags.count) {
        return @[];
    }
    NSMutableArray *array = [@[] mutableCopy];
    for (id obj in self.tags) {
        if ([obj isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dic = obj;
            if (dic[@"id"]) {
                [array addObject:[NSNumber numberWithLong:[[NSString stringWithFormat:@"%@", dic[@"id"]] longValue]]];
            }
        } else if ([obj isKindOfClass:[TagsModel class]]) {
            TagsModel *model = obj;
            if ([model.id isKindOfClass:[NSNumber class]] && [model.id.stringValue length]) {
                [array addObject:model.id];
            }
        }
    }
    return array;
}

- (NSString *)type {
    if (!_type.length) {
        _type = @"TEXT";
    }
    return _type;
}

- (NSNumber *)soulmateUserId {
    return [NSNumber numberWithString:[_soulmateUserIdEcpt decode]];
}

- (void)setActionType:(NSString *)actionType {
    _actionType = actionType;
    _actionTitle = [self actionStringForActionType:actionType andSourcePost:self];
}

//praise/comment/collect
- (NSString *)actionStringForActionType:(NSString *)actionType andSourcePost:(SOPost *)post {
    NSString *title = nil;
    NSString *dateString = nil;

    if ([self.actionType isEqualToString:@"praise"]) {// 点赞
        title = @"点赞了这条瞬间";
        dateString = [self dateStringForTimeStamp:post.praiseTime];
    }
    if ([self.actionType isEqualToString:@"comment"]) {// 评论
        title = @"评论了这条瞬间";
        dateString = [self dateStringForTimeStamp:post.commentTime];
    }
    if ([self.actionType isEqualToString:@"collect"]) {// 收藏
        title = @"收藏了这条瞬间";
        dateString = [self dateStringForTimeStamp:post.collectTime];
    }

    NSString *contentString = [NSString stringWithFormat:@"我%@%@", dateString, title];
    return contentString;
}

- (NSString *)dateStringForTimeStamp:(NSUInteger)timeStampMs {
    NSUInteger timeStamp = timeStampMs / 1000;
    NSString *timeString = nil;

    NSUInteger currentTime = [[NSDate date] timeIntervalSince1970];

    NSDate *date = [NSDate dateWithTimeIntervalSince1970:timeStamp];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    NSUInteger leftTime = currentTime - timeStamp;
    if (leftTime > 24 * 3600) {
        [dateFormatter setDateFormat:@"MM月dd日"];
        timeString = [dateFormatter stringFromDate:date];
    } else {
        timeString = [NSDate compareCurrentTime:currentTime - timeStamp];
    }
    return timeString;
}

- (BOOL)shouldAutoPlayVideo {
    if ([self hasVideo]) {
        if ([AFNetworkReachabilityManager sharedManager].networkReachabilityStatus == AFNetworkReachabilityStatusReachableViaWiFi) {
            return YES;
        }
        SOAttachment *attachment = self.attachments.firstObject;
        return (attachment.fileDuration.integerValue <= 10);
    }
    return NO;
}

- (BOOL)hasVideo {
    if (self.attachments.count) {
        for (SOAttachment *attachment in self.attachments) {
            if ([attachment.type isEqualToString:@"VIDEO"]) {
                return YES;
            }
        }
    }
    return NO;
}

- (NSInteger)firstVideo {
    if (self.attachments.count) {
        for (int i = 0; i < self.attachments.count; i++) {
            if ([self isVideo:i]) {
                return i;
            }
        }
    }
    return 0;
}

- (BOOL)isVideo:(NSInteger)index {
    if (index >= self.attachments.count) {
        return NO;
    }
    SOAttachment *attachment = self.attachments[index];
    return [attachment.type isEqualToString:@"VIDEO"];
}


- (void)appendComment:(SOPostRecommendComment *)comment {
    if (self.recommendComments.count == 0) {
        self.recommendComments = [NSMutableArray new];
        [self.recommendComments addObject:comment];
    } else if (self.recommendComments.count == 1) {
        SOPostRecommendComment *com = self.recommendComments.firstObject;
        BOOL isSelf;
        isSelf = [SoulUserManager isSelf:com.authorId];
        if (isSelf) {
            [self.recommendComments replaceObjectAtIndex:0 withObject:comment];
        } else {
            [self.recommendComments addObject:comment];
        }
    } else {
        [self.recommendComments replaceObjectAtIndex:1 withObject:comment];
    }
}

- (BOOL)isLongVideoPost {
    if (self.attachments.count == 1) {
        SOAttachment *attachment = self.attachments.firstObject;
        return [attachment.type isEqualToString:@"VIDEO"] && attachment.fileDuration.integerValue > 10;
    }
    return 0;
}

- (void)insertCampModelToTags {
    if (self.campusModel.name.length > 0) {
        for (TagsModel *tag in self.tags) {
            if (tag.isSchoolBar) {
                return;
            }
        }
        NSMutableArray *tagsArray = [NSMutableArray array];
        TagsModel *tagModel = [[TagsModel alloc] init];
        tagModel.isSchoolBar = YES;
        tagModel.id = self.campusModel.schoolId;
        tagModel.name = @"校园吧";
        NSInteger postIdInt = [self.postId intValue];
        tagModel.postId = @(postIdInt);
        [tagsArray addObject:tagModel];
        if (self.tags.count > 0) {
            [tagsArray addObjectsFromArray:self.tags];
        }
        self.tags = tagsArray;
    }
}

- (BOOL)isOneSelf {
    return [self.authorId.stringValue isEqualToString:[SOUserInfoManager sharedInstance].userId] && self.authorId.stringValue.length;
}

- (void)setLike:(BOOL)like {
    if (self.liked.boolValue == like) {
        return;
    }
    if (like) {
        self.liked = @(1);
        if ([NSString isNum:self.likeNum]) {
            self.likeNum = [NSString stringWithFormat:@"%ld", self.likeNum.integerValue + 1];
            self.likes = @(self.likes.integerValue + 1);
        }
    } else {
        self.liked = @(0);
        if ([NSString isNum:self.likeNum] && self.likeNum.integerValue > 0) {
            self.likeNum = [NSString stringWithFormat:@"%ld", self.likeNum.integerValue - 1];
            self.likes = @(self.likes.integerValue - 1);
        }
    }
}

- (void)updateCollectCount:(BOOL)collect {
    if (self.collected.boolValue == collect) {
        return;
    }
    if (collect) {
        self.collected = @(1);
        if ([NSString isNum:self.followNum]) {
            self.followNum = [NSString stringWithFormat:@"%ld", self.followNum.integerValue + 1];
            self.follows = @(self.follows.integerValue + 1);
        }
    } else {
        self.collected = @(0);
        if ([NSString isNum:self.followNum] && self.followNum.integerValue > 0) {
            self.followNum = [NSString stringWithFormat:@"%ld", self.followNum.integerValue - 1];
            self.follows = @(self.follows.integerValue - 1);
        }
    }
}

/// 仅埋点用，无数据是使用-100
- (NSString *)algExt {
    if (_algExt.length <= 0) {
        return @"-100";
    }
    return _algExt;
}
- (BOOL)commentFlag{
    if (self.commentFlagValue == nil){
        return true;
    }
    return self.commentFlagValue.boolValue;
}
/// 媒资id
- (NSString * _Nullable)likeLottieResId {
    /// praiseEffectDailyLimit 为0 不限制次数
    /// praiseEffectId 存在且不为0
    /// 才会去检查数量
    if (self.praiseEffectDailyLimit > 0 && self.praiseEffectId != nil && ![self.praiseEffectId isEqualToString:@"0"]) {
        /// 获取对应的数据
        NSInteger count = [SOPostLottieLimitManage.shareInstance getLottiePlayCount:self.praiseEffectId];
        if (count >= self.praiseEffectDailyLimit) {
            return nil;
        }
    }
    return self.praiseEffectId;
}

- (NSInteger)authorRecRightCount {
    if (self.authorRecRightNum != nil){
        return self.authorRecRightNum.integerValue;
    }
    return -1;
}

- (BOOL)isGetSessionEnable {
    id isCloseSessionId = [[SoulConfigManager sharedInstance].globalConfig objectForKey:@"ugc_square_event_close_sessionId"];
    if (SOBool(isCloseSessionId) == true) {
        return false;
    }
    return true;
}

- (NSDictionary *)transformToDictionary {
    id value = [SoulConfigManager.sharedInstance.globalConfig objectForKey:@"ugc_post_yymodel_transform"];
    if (SOBoolWith(value, true) == true) {
        @try {
            NSDictionary *dic = self.yy_modelToJSONObject;
            if ([dic isKindOfClass:NSDictionary.class]){
                return dic;
            }
        } @catch (NSException *exception) {
        } @finally {
        }
    }
    return nil;
}

- (BOOL)postIsAITreeHoleType {
    return self.sceneType.integerValue == 109;
}

- (BOOL)local_subscribState {
    _local_subscribState = self.sceneType.integerValue == 110;
    return _local_subscribState;
}

- (NSString *)getAttachmentIDs {
    NSMutableArray *attachmentIds = [NSMutableArray new];
    for (SOAttachment * attachment in self.attachments) {
        if ([attachment.type isEqualToString:@"IMAGE"]) {
            [attachmentIds addObject:SOString(attachment.id)];
        } else { //暂不支持其他类型
            continue;
        }
    }
    return SOString([attachmentIds jsonStringEncoded]);
}

@end

@implementation SOPostAITextModel

@end

@implementation SOPostCreateTimeModel

@end

@implementation SOPostEmotionModel

@end

@implementation SOPostWeatherModel

@end
